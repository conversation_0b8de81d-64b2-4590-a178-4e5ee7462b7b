<!-- Statuses -->
<div class="col-md-2 mb-3">
    <div class="form-group">
        <label for="statuses">وضعیت</label>
        <select data-control="select2" class="form-select form-select-sm"
                id="statuses" multiple="multiple" name="statuses[]">
            <option value="">انتخاب کنید</option>
            @foreach($params['statuses'] as $status)
                <option value="{{$status['value']}}"
                        @if($status['value'] == ($search['statuses'] ?? -1)) selected @endif() >{{$status['label']}}</option>
            @endforeach
        </select>
    </div>
</div>

<!-- Todolist Category Types -->


<!-- Project Types -->
<div class="col-md-2 mb-3">
    <div class="form-group">
        <label for="projectTypes">نوع پروژه</label>
        <select data-control="select2" class="form-select form-select-sm "
                id="projectTypes" multiple="multiple" name="projectTypes[]" >
            <option value="">انتخاب کنید</option>
            @foreach($params['projectTypes'] as $data)
                <option value="{{$data['value']}}"
                        @if($data['value'] == ($search['projectTypes'] ?? -1)) selected @endif() >{{$data['label']}}</option>
            @endforeach
        </select>
    </div>
</div>

<!-- Payment Status -->
<div class="col-md-2 mb-3">
    <div class="form-group">
        <label for="typePaymentStatus">نوع واریزی</label>
        <select data-control="select2" class="form-select form-select-sm "
                id="typePaymentStatus" multiple="multiple" name="typePaymentStatus[]">
            <option value="">انتخاب کنید</option>
            @foreach($params['typePaymentStatus'] as $data)
                <option value="{{$data['value']}}"
                        @if($data['value'] == ($search['typePaymentStatus'] ?? -1)) selected @endif() >{{$data['label']}}</option>
            @endforeach
        </select>
    </div>
</div>
<div class="col-md-2 mb-3">
    <div class="form-group">
        <label for="paymentStatus">وضعیت واریزی</label>
        <select data-control="select2" class="form-select form-select-sm "
                id="paymentStatus" multiple="multiple" name="paymentStatus[]">
            <option value="">انتخاب کنید</option>
            @foreach($params['paymentStatus'] as $data)
                <option value="{{$data['value']}}"
                        @if($data['value'] == ($search['paymentStatus'] ?? -1)) selected @endif() >{{$data['label']}}</option>
            @endforeach
        </select>
    </div>
</div>
<div class="col-2 form-section"
   >
    <label class="form-label">بانک واریزی</label>
    <select class="form-select form-select-sm mb-2" multiple="multiple" data-control="select2"
            id="bank_id"
            data-hide-search="false" data-placeholder="انتخاب کنید" 
            name="bank_id[]">
        <option value="">انتخاب کنید</option>
        @foreach ($banks as $bank)
            <option value="{{ $bank->id }}"
                    @if ($bank->id == ($search['bank_id'] ?? -1)) selected @endif>
                {{ $bank->name }}
            </option>
        @endforeach
    </select>
</div>
<!-- Mehrsan Status -->
<div class="col-md-4 mb-3">
    <div class="form-group">
        <label for="mehrsanStatus">وضعیت مهرسان</label>
        <select data-control="select2" class="form-select form-select-sm "
                id="mehrsanStatus" multiple="multiple" name="mehrsanStatus[]">
            <option value="">انتخاب کنید</option>
            @foreach($params['mehrsanStatus'] as $data)
                <option value="{{$data['value']}}"
                        @if($data['value'] == ($search['mehrsanStatus'] ?? -1)) selected @endif() >{{$data['label']}}</option>
            @endforeach
        </select>
    </div>
</div>

<!-- Inspection Status -->
<div class="col-md-4 mb-3">
    <div class="form-group">
        <label for="inspectionStatus">وضعیت بازدید</label>
        <select data-control="select2" class="form-select form-select-sm "
                id="inspectionStatus" multiple="multiple" name="inspectionStatus[]">
            <option value="">انتخاب کنید</option>
            @foreach($params['inspectionStatus'] as $data)
                <option value="{{$data['value']}}"
                        @if($data['value'] == ($search['inspectionStatus'] ?? -1)) selected @endif() >{{$data['label']}}</option>
            @endforeach
        </select>
    </div>
</div>

<!-- Support Status -->
<div class="col-md-4 mb-3">
    <div class="form-group">
        <label for="supportStatus">وضعیت پشتیبانی</label>
        <select data-control="select2" class="form-select form-select-sm "
                id="supportStatus" multiple="multiple" name="supportStatus[]">
            <option value="">انتخاب کنید</option>
            @foreach($params['supportStatus'] as $data)
                <option value="{{$data['value']}}"
                        @if($data['value'] == ($search['supportStatus'] ?? -1)) selected @endif() >{{$data['label']}}</option>
            @endforeach
        </select>
    </div>
</div>

<!-- Insurance Status -->
<div class="col-md-4 mb-3">
    <div class="form-group">
        <label for="insuranceStatus">وضعیت بیمه</label>
        <select data-control="select2" class="form-select form-select-sm "
                id="insuranceStatus" multiple="multiple" name="insuranceStatus[]">
            <option value="">انتخاب کنید</option>
            @foreach($params['insuranceStatus'] as $data)
                <option value="{{$data['value']}}"
                        @if($data['value'] == ($search['insuranceStatus'] ?? -1)) selected @endif() >{{$data['label']}}</option>
            @endforeach
        </select>
    </div>
</div>

<!-- Design Results -->
<div class="col-md-4 mb-3">
    <div class="form-group">
        <label for="designResults">نتایج طراحی</label>
        <select data-control="select2" class="form-select form-select-sm "
                id="designResults" multiple="multiple" name="designResults[]">
            <option value="">انتخاب کنید</option>
            @foreach($params['designResults'] as $data)
                <option value="{{$data['value']}}"
                        @if($data['value'] == ($search['designResults'] ?? -1)) selected @endif() >{{$data['label']}}</option>
            @endforeach
        </select>
    </div>
</div>

<!-- Current Phases -->
<div class="col-md-4 mb-3">
    <div class="form-group">
        <label for="currentPhases">مراحل اجرایی</label>
        <select data-control="select2" class="form-select form-select-sm "
                id="currentPhases" multiple="multiple" name="currentPhases[]">
            <option value="">انتخاب کنید</option>
            @foreach($params['currentPhases'] as $data)
                <option value="{{$data['value']}}"
                        @if($data['value'] == ($search['currentPhases'] ?? -1)) selected @endif() >{{$data['label']}}</option>
            @endforeach
        </select>
    </div>
</div>

<!-- Structure Type -->
<div class="col-md-4 mb-3">
    <div class="form-group">
        <label for="structureType">نوع سازه</label>
        <select data-control="select2" class="form-select form-select-sm "
                id="structureType" multiple="multiple" name="structureType[]">
            <option value="">انتخاب کنید</option>
            @foreach($params['designResults'] as $data)
                <option value="{{$data['value']}}"
                        @if($data['value'] == ($search['structureType'] ?? -1)) selected @endif() >{{$data['label']}}</option>
            @endforeach
        </select>
    </div>
</div>

<!-- Inverter Models -->
<div class="col-md-4 mb-3">
    <div class="form-group">
        <label for="inverterModels">مدل اینورتر</label>
        <select data-control="select2" class="form-select form-select-sm "
                id="inverterModels" multiple="multiple" name="inverterModels[]">
            <option value="">انتخاب کنید</option>
            @foreach($params['inverterModels'] as $data)
                <option value="{{$data['value']}}"
                        @if($data['value'] == ($search['inverterModels'] ?? -1)) selected @endif() >{{$data['label']}}</option>
            @endforeach
        </select>
    </div>
</div>

<!-- Support Organization -->
<div class="col-md-4 mb-3">
    <div class="form-group">
        <label for="supportOrganization">سازمان حمایتی</label>
        <select data-control="select2" class="form-select form-select-sm "
                id="supportOrganization" multiple="multiple" name="supportOrganization[]">
            <option value="">انتخاب کنید</option>
            @foreach($support_organizations as $data)
                <option value="{{$data['value']}}"
                        @if($data['value'] == ($search['supportOrganization'] ?? -1)) selected @endif() >{{$data['label']}}</option>
            @endforeach
        </select>
    </div>
</div>

<!-- Todolist Category Types (multiple) -->
<div class="col-md-4 mb-3">
    <div class="form-group">
        <label for="todolistCategoryTypes">نواقص و بررسی</label>
        <select data-control="select2" class="form-select form-select-sm "
                id="todolistCategoryTypes" name="todolistCategoryTypes[]" multiple>
            @foreach($params['todolistCategoryTypes'] as $data)
                <option value="{{$data['value']}}"
                        @if(in_array($data['value'],$search['todolistCategoryTypes'] ?? []) )
                            selected @endif() >{{$data['label']}}</option>
            @endforeach
        </select>
    </div>
</div>

<div class="col-md-4 mb-3">
    <div class="form-group">
        <label for="powerPlantCapacity">ظرفیت نیروگاه</label>
        <select data-control="select2" class="form-select form-select-sm "
                id="powerPlantCapacity" multiple="multiple" name="powerPlantCapacity[]">
            <option value="">انتخاب کنید</option>
            @foreach($params['powerPlantCapacity'] as $data)
                <option value="{{$data['value']}}"
                        @if($data['value'] == ($search['powerPlantCapacity'] ?? -1)) selected @endif() >{{$data['label']}}</option>
            @endforeach
        </select>
    </div>
</div>

<!-- Province -->
<div class="col-4 form-section"
     data-role="admin|accounting|warehouse|support|design|execution">
    <label class="form-label">استان</label>
    <select class="form-select form-select-sm mb-2" data-control="select2"
            id="province"
            data-hide-search="false" data-placeholder="انتخاب کنید" 
            name="province_id">
        <option value="">انتخاب کنید</option>
        @foreach ($provinces as $province)
            <option value="{{ $province->id }}"
                    @if ($province->id == ($search['province_id'] ?? -1)) selected @endif>
                {{ $province->name }}
            </option>
        @endforeach
    </select>
</div>

<!-- County -->
<div class="col-4 form-section"
     data-role="admin|accounting|warehouse|support|design|execution">
    <label class="form-label">شهرستان</label>
    <select class="form-select form-select-sm mb-2" data-control="select2"
            id="county"
            data-hide-search="false" data-placeholder="انتخاب کنید"
            name="county_id">
        <option value="">انتخاب کنید</option>
        @foreach ($counties as $county)
            <option value="{{ $county->id }}"
                    @if ($county->id == ($search['county_id'] ?? -1)) selected @endif>
                {{ $county->name }}
            </option>
        @endforeach
    </select>
</div>

<!-- City -->
<div class="col-4 form-section"
     data-role="admin|accounting|warehouse|support|design|execution">
    <label class="form-label">شهر</label>
    <select class="form-select form-select-sm mb-2" data-control="select2"
            id="city"
            data-hide-search="false" data-placeholder="انتخاب کنید"
            name="city_id">
        <option value="">انتخاب کنید</option>
        @foreach ($cities as $city)
            <option value="{{ $city->id }}"
                    @if ($city->id == ($search['city_id'] ?? -1)) selected @endif>
                {{ $city->name }}
            </option>
        @endforeach
    </select>
</div>
<div class="col-4 form-section"
     data-role="admin|accounting|warehouse|support|design|execution">
    <label class="form-label">روستا</label>
    <select class="form-select form-select-sm mb-2" multiple="multiple" data-control="select2"
            id="province"
            data-hide-search="false" data-placeholder="انتخاب کنید" 
            name="village_id[]">
        <option value="">انتخاب کنید</option>
        @foreach ($villages as $village)
            <option value="{{ $village->id }}"
                    @if ($village->id == ($search['village_id'] ?? -1)) selected @endif>
                {{ $village->name }}
            </option>
        @endforeach
    </select>
</div>