@extends('layouts.master')

@section('title', __('بروزرسانی وضعیت مهرسان'))
@section('page-css')
@stop
@section('content')
    <div class="toolbar mb-5 mb-lg-7" id="kt_toolbar">
        <div class="page-title d-flex flex-column me-3">
            {{-- <h1 class="d-flex text-dark fw-bold my-1 fs-3">{{__("ایجاد کاربر با بارگذاری فایل")}}</h1> --}}
        </div>
    </div>
    <div class="content flex-column-fluid" id="kt_content">

        @if(session('created_toast'))
            <div class="alert alert-success">
                {{ session('created_toast') }}
            </div>
        @endif

        @if(session('error_toast'))
            <div class="alert alert-danger">
                {{ session('error_toast') }}
            </div>
        @endif

        <div class="mt-5 mb-5 card card-flush">
            <div class="card-header align-items-center py-5 gap-2 gap-md-5">
                <div class="card-title">
                    <h3 class="card-title align-items-start flex-column">
                        <span class="card-label mb-2 fw-bold text-dark">بروزرسانی وضعیت مهرسان با بارگذاری فایل</span>
                        
                    </h3>

                </div>

            </div>
            <div class="card-body pt-0">
                <div class="mb-5 row">

                    <form method="post" action="{{route('projects.store-import-file')}}" 
                          class="form d-flex flex-column flex-lg-row "
                          enctype="multipart/form-data">
                        @csrf
                        <div class="col m-1">
                            <label for="name" class=" form-label">فایل مهرسان</label>
                            <input type="file" accept="text/xlsx" name="file" id="file"
                                   class="form-control form-control mb-2">
                        </div>
                        
                        <div class="col mt-8 d-flex justify-content-end">
                            <div class="fa-pull-right">
                                <a href="{{ asset('sample-mehrsan-change.xlsx') }}"
                                   class="btn text-primary btn-light">دانلود نمونه فایل
                                    </a>
                                <button type="submit" id=""
                                        class="btn  btn-primary">
                                    <span class="indicator-label">ذخیره</span>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

    </div>
@endsection

@section('page-js')

@endsection
