<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Project\CreateOrUpdateProjectRequest;
use App\Models\Project;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ProjectController extends Controller
{

    public function index(Request $request)
    {
        $request->validate([
            'searchText' => 'nullable|string',
            'limit' => 'nullable|integer|min:1',
            'offset' => 'nullable|integer|min:0',
        ], [
            'limit.required_with' => 'The limit field is required when offset is present.',
        ]);

        $validator = Validator::make($request->all(), [
            'limit' => 'required_with:offset'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation Error',
                'errors' => $validator->errors(),
            ], 422);
        }

        $searchText = $request->input('searchText');
        $limit = $request->input('limit');
        $offset = $request->input('offset');

        $user = auth()->user()->userUpdate;

        $projects = Project::withTrashed()
            ->with([
                'user:id,first_name,last_name,national_code', 
                'serialPanels' => function ($query) {
                    $query->withTrashed();
                }, 
                'support' => function ($query) {
                    $query->withTrashed();
                },
                'deposit' => function ($query) {
                    $query->withTrashed();
                }, 
                'checking' => function ($query) {
                    $query->withTrashed();
                }, 
                'defect' => function ($query) {
                    $query->withTrashed();
                }, 
                'executionDefect' => function ($query) {
                    $query->withTrashed();
                }, 
                'visitor' => function ($query) {
                    $query->withTrashed();
                },
                'thirdParty' => function ($query) {
                    $query->withTrashed();
                },
            ])
            ->when(!empty($searchText), function ($query) use ($searchText) {
                $query->where('projects.title', 'like', "%$searchText%")
                    ->orWhereHas('user', function ($query) use ($searchText) {
                        $query->where('national_code', 'like', "%$searchText%")
                            ->orWhere('first_name', 'like', "%$searchText%")
                            ->orWhere('last_name', 'like', "%$searchText%");
                    });
            })
            ->when(!empty($limit), function ($query) use ($limit) {
                $query->limit($limit);
            })
            ->when(!empty($offset), function ($query) use ($offset) {
                $query->offset($offset);
            })
            ->when($user->last_project_update, function ($query) use ($user) {
                $query->where('projects.sync_update', '>', $user->last_project_update);
            })
            ->orderBy('projects.sync_update', 'asc')
            ->limit(500)
            ->get(['projects.*']);

        $total = Project::withTrashed()
            ->when($user->last_project_update, function ($query) use ($user) {
                $query->where('projects.sync_update', '>', $user->last_project_update);
            })
            ->count();

        return apiResponse()
            ->withMessage('پروژه ها با موفقیت دریافت شدند')
            ->withData([
                'total' => $total,
                'filtered' => $projects->count(),
                'projects' => $projects,
            ]);
    }

    // public function get(Request $request)
    // {
    //     $project = Project::with('owner')->where('id', $request->project_id)->first();
    //     if (empty($project))
    //         return apiResponse()
    //             ->withMessage('پروژه یافت نشد!');

    //     return apiResponse()
    //         ->withMessage('پروژه با موفقیت دریافت شدند')
    //         ->withData([
    //             'project' => $project
    //         ]);
    // }

    // public function recent(Request $request)
    // {
    //     $user = auth()->user();
    //     $projects = Project::with('owner')
    //         ->where('updated_at', '>', $user->last_project_update)
    //         ->get();

    //     return apiResponse()
    //         ->withMessage('پروژه ها با موفقیت دریافت شدند')
    //         ->withData([
    //             'projects' => $projects
    //         ]);
    // }

    // public function createOrUpdate(CreateOrUpdateProjectRequest $request)
    // {
    //     $project = Project::updateOrCreate(
    //         ['id' => $request->input('id')],
    //         [
    //             'project_code' => $request->input('project_code'),
    //             'user_id' => $request->input('user_id'),
    //             'electricity_country' => $request->input('electricity_country'),
    //             'electricity_affairs_code' => $request->input('electricity_affairs_code'),
    //             'electricity_bill_id' => $request->input('electricity_bill_id'),
    //             'mehrsan_file_number' => $request->input('mehrsan_file_number'),
    //             'postal_code' => $request->input('postal_code'),
    //             'power_plant_capacity' => $request->input('power_plant_capacity'),
    //             'support_organization' => $request->input('support_organization'),
    //             'province_id' => $request->input('province_id'),
    //             'county_id' => $request->input('county_id'),
    //             'city_id' => $request->input('city_id'),
    //             'payment_bank' => $request->input('payment_bank'),
    //             'address' => $request->input('address'),
    //             'payment_status' => $request->input('payment_status'),
    //             'remaining_deposit' => $request->input('remaining_deposit'),
    //             'paid_amount' => $request->input('paid_amount'),
    //             'payment_date' => $request->input('payment_date'),
    //             'balance' => $request->input('balance'),
    //             'payment_until_today' => $request->input('payment_until_today'),
    //             'inspection_status' => $request->input('inspection_status'),
    //             'inspection_date' => $request->input('inspection_date'),
    //             'inspector' => $request->input('inspector'),
    //             'inspector_comment' => $request->input('inspector_comment'),
    //             'design_file_result' => $request->input('design_file_result'),
    //             'panel_installation_date' => $request->input('panel_installation_date'),
    //             'meter_installation_date' => $request->input('meter_installation_date'),
    //             'payment_to_execution_duration' => $request->input('payment_to_execution_duration'),
    //             'payment_to_network_connection_duration' => $request->input('payment_to_network_connection_duration'),
    //             'installation_to_today_duration' => $request->input('installation_to_today_duration'),

    //             'installer_id' => $request->input('installer_id'),
    //             'designer_id' => $request->input('designer_id'),

    //             'structure_type_image' => $request->input('structure_type_image'),
    //             'current_phase' => $request->input('current_phase'),
    //             'send_list_comments' => $request->input('send_list_comments'),
    //             'materials' => $request->input('materials'),
    //             'additional_costs_and_dorm_rent' => $request->input('additional_costs_and_dorm_rent'),
    //             'panel_type_power_and_quantity' => $request->input('panel_type_power_and_quantity'),
    //             'panel_serial_numbers_image' => $request->input('panel_serial_numbers_image'),
    //             'inverter_model' => $request->input('inverter_model'),
    //             'inverter_serial_number_image' => $request->input('inverter_serial_number_image'),
    //             'meter_serial_number_image' => $request->input('meter_serial_number_image'),
    //             'sim_card_number' => $request->input('sim_card_number'),
    //             'utm_y' => $request->input('utm_y'),
    //             'utm_x' => $request->input('utm_x'),
    //             'longitude' => $request->input('longitude'),
    //             'latitude' => $request->input('latitude'),
    //             'google_maps_link' => $request->input('google_maps_link'),
    //             'gis_code' => $request->input('gis_code'),
    //             'monitoring' => $request->input('monitoring'),
    //             'mehrsan_status' => $request->input('mehrsan_status'),
    //             'support_image_and_comments' => $request->input('support_image_and_comments'),
    //             'insurance' => $request->input('insurance'),
    //             'support_needed' => $request->input('support_needed'),
    //             'type' => $request->input('type'),
    //             'status' => $request->input('status'),
    //         ]
    //     );

    //     return apiResponse()
    //         ->withStatusCode(201)
    //         ->withData([
    //             'project' => $project
    //         ])->withMessage('پروژه با موفقتیت ایجاد شد');
    // }


}
