<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\auth\LoginRequest;
use App\Http\Requests\auth\SendSmsRequest;
use App\Models\User;
use App\Responses\ApiResponse;
use App\Responses\ServiceResponse;
use App\Services\SmsService;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Symfony\Component\HttpFoundation\Response as ResponseAlias;
use Tymon\JWTAuth\Facades\JWTAuth;

class AuthController extends Controller
{

    public function verifyToken()
    {
        if (!auth()->check())
            return apiResponse()
                ->withError('unauthorized')
                ->withStatusCode(401)
                ->withMessage('توکن نامعتبر.');


        $user = Auth::guard('api')->user();
        $userArray = $user->toArray();
        $rolesWithPermissions = [];

        foreach ($user->roles as $role) {
            if($role->guard_name!='api') continue;

            $permissionsArray = $role->permissions->pluck('name')->toArray();

            $rolesWithPermissions[] = [
                $role->name => $permissionsArray
            ];
        }

        $user = array_merge($userArray, ['roles' => $rolesWithPermissions]);


        $userWithRole = auth()->user()->load('roles', 'permissions');


        $hashData = [
            'permissions' => $userWithRole->getAllPermissions()->pluck('name'),
            'roles' => $userWithRole->roles->pluck('name'),
        ];
        $user['hash'] = encryptString(json_encode($hashData));


        return apiResponse()
            ->withMessage('ورود با موفقیت انجام شد.')
            ->withData([
                'user' => $user,
            ]);
    }

    public function login($phone, $password)
    {
        if (!$token = auth()->attempt(['phone' => $phone, 'password' => $password])) {
            return apiResponse()
                ->withError([
                    'Unauthorized'
                ])
                ->withMessage(['شماره همراه یا کلمه عبور اشتباه است'])
                ->withStatusCode(401);
        }

        $user = Auth::guard('api')->user();
        $userArray = $user->toArray();
        $rolesWithPermissions = [];

        // foreach ($user->roles as $role) {
        //     if($role->guard_name!='api') continue;
        //     $permissionsArray = $role->permissions->pluck('name')->toArray();

        //     $rolesWithPermissions[] = [
        //         $role->name => $permissionsArray
        //     ];
        // }

        // $user = array_merge($userArray, ['roles' => $rolesWithPermissions]);

        $permissions = $user->getAllPermissions()
        ->where('guard_name', 'api')
        ->pluck('name')->toArray();
        $user = array_merge($userArray, ['permissions' => $permissions]);

        return apiResponse()
            ->withMessage('ورود با موفقیت انجام شد.')
            ->withData([
                'apiToken' => $token,
                'user' => $user,
            ]);
    }

    public function handleSignIn(Request $request)
    {
        $phone = $request->phone;
        $password = $request->password;
        $hash = $request->hash;
        if ($request->authType == 'credentials')
            return $this->login($phone, $password);
        else if ($request->authType == 'otp')
            return $this->handleSms($phone, $hash);
        else
            return apiResponse()
                ->withError('Auth Type is not correct')
                ->withMessage('روش ورود اشتباه است !')
                ->withStatusCode(406);
    }

    private function handleSms($phone, $hash)
    {
        DB::beginTransaction();
        try {
            $user = User::where('phone', $phone)->first();
            if (!$user)
                return apiResponse()
                    ->withError('َUser not found')
                    ->withMessage('کاربر پیدا نشد')
                    ->withStatusCode(404);

            if (isset($user->otp_code_sent) && now() < $user->otp_code_sent->addMinutes(3))
                return apiResponse()
                    ->withData(['expireTime' => ceil(now()->diffInSeconds($user->otp_code_sent->addMinutes(3)))])
                    ->withMessage('رمز عبور یکبارمصرف به شماره همراه شما ارسال شد');

            $code = random_int(100000, 999999);
            if (app()->environment('local')) {
                $user->otp_code = "123456";
                $user->save();
            }

            if (!app()->environment('local')) {
                $user->otp_code = $code;
                $user->otp_code_sent = now();
                $user->save();
                (new SmsService())->send($user->phone, ['otp' => $code, 'hash' => $hash]);
            }

            DB::commit();
            return apiResponse()
                ->withData(['expireTime' => 180])
                ->withMessage('رمز عبور یکبارمصرف به شماره همراه شما ارسال شد');
        } catch (Exception $exception) {
            DB::rollBack();

            return apiResponse()
                ->withStatusCode(500)
                ->withData(['exceptionMessage' => $exception->getMessage()])
                ->withMessage('رمز عبور یکبارمصرف به شماره همراه شما ارسال شد');
        }


    }

    public function checkPhoneCode(Request $request)
    {
        $phone = $request->phone;
        $code = $request->code;

        $user = User::where('phone', $phone)->first();
        if (empty($user)) {
            return apiResponse()
                ->withStatusCode(404)
                ->withMessage('کاربری با این مشخصات پیدا نشد!');
        }

        if ($user->otp_code != $code || now() > $user->otp_code_sent->addMinutes(3)) {
            return apiResponse()
                ->withStatusCode(422)
                ->withMessage('کد ورود نامعتبر است!');
        }
        auth()->login($user);
        $token = JWTAuth::fromUser($user);

        $user = Auth::guard('api')->user();
        $userArray = $user->toArray();
        // $rolesWithPermissions = [];

        // foreach ($user->roles as $role) {
        //     $permissionsArray = $role->permissions->pluck('name')->toArray();

        //     $rolesWithPermissions[] = [
        //         $role->name => $permissionsArray
        //     ];
        // }

        // $user = array_merge($userArray, ['roles' => $rolesWithPermissions]);
        $permissions = $user->getAllPermissions()
        ->where('guard_name', 'api')
        ->pluck('name')->toArray();
        $user = array_merge($userArray, ['permissions' => $permissions]);
        return apiResponse()
            ->withMessage('ورود با موفقیت انجام شد.')
            ->withData([
                'apiToken' => $token,
                'user' => $user,
            ]);
    }


    /*  public function signup(SignupRequest $request)
      {
          $user = User::create([
              'email' => $request->email,
              'password' => $request->password,
          ]);

          $user->merchants()->create([
              'userId' => $user->id,
              'accountNumber' => $request->accountNumber,
              'transitNumber' => $request->transitNumber,
              'institutionNumber' => $request->institutionNumber,
              'mid' => $request->mid,

          ]);
      }*/
}
