<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Project;
use App\Models\TodolistCategory;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;

class TodolistCategoryController extends Controller
{

    public function index(Request $request)
    {
        $type = $request->input('type');
        $limit = $request->input('limit');
        $offset = $request->input('offset');

        $user = auth()->user()->userUpdate;

        $todolist = TodolistCategory::When(!empty($type), function ($query) use ($type) {
            $query->where('todolist_categories.type', 'like', "%$type%");
        })
        ->when(!empty($limit), function ($query) use ($limit) {
            $query->limit($limit);
        })
        ->when(!empty($offset), function ($query) use ($offset) {
            $query->offset($offset);
        })
        ->when($user->last_todo_update, fn(Builder $query) => $query->where('sync_update', '>', $user->last_todo_update))
        ->orderBy('sync_update', 'asc')
        ->get(['todolist_categories.*']); // Select all columns from todolist_categories
   
        
        $total = TodolistCategory::When($user->last_todo_update, fn(Builder $query) => $query->where('sync_update', '>', $user->last_todo_update))->count();

        return apiResponse()
            ->withMessage('لیست کار ها با موفقیت دریافت شدند')
            ->withData([
                'total' => $total,
                'filtered' => $todolist->count(),
                'todolists' => $todolist
            ]);
    }


}
