array (
    'changes' => '{
        "changes": [
            {"user_id": 1, "table": "projects", "table_id": 1, "field": "electricity_affairs_code", "id": 1, "value": "451234", "is_new": 0},
            
            {"user_id": 1, "table": "mediables", "table_id": 1, "field": "media", "id": 2, "value": "/data/user/0/ir.yektadg.khorshid_taban/app_flutter/1718436186495.jpg", "is_media": 1, "is_new": 1},
            {"user_id": 1, "table": "mediables", "table_id": 1, "field": "mediable_type", "id": 3, "value": "serial_panels", "is_new": 1},
            {"user_id": 1, "table": "mediables", "table_id": 1, "field": "mediable_id", "id": 4, "value": "7", "is_new": 1},
            {"user_id": 1, "table": "mediables", "table_id": 1, "field": "tag", "id": 5, "value": "description", "is_new": 1},
            {"user_id": 1, "table": "mediables", "table_id": 2, "field": "media", "id": 6, "value": "/data/user/0/ir.yektadg.khorshid_taban/app_flutter/1718437998055.jpg", "is_media": 1, "is_new": 1},
            
            {"user_id": 1, "table": "mediables", "table_id": 2, "field": "mediable_type", "id": 7, "value": "serial_panels", "is_new": 1},
            {"user_id": 1, "table": "mediables", "table_id": 2, "field": "mediable_id", "id": 8, "value": "7", "is_new": 1},
            {"user_id": 1, "table": "mediables", "table_id": 2, "field": "tag", "id": 9, "value": "description", "is_new": 1},
            
            {"user_id": 1, "table": "serial_panels", "table_id": 11, "field": "project_id", "id": 10, "value": "1", "is_new": 1},
            {"user_id": 1, "table": "serial_panels", "table_id": 11, "field": "serial", "id": 11, "value": "test", "is_new": 1},
            {"user_id": 1, "table": "serial_panels", "table_id": 11, "field": "description", "id": 12, "value": "", "is_new": 1},
            
            {"user_id": 1, "table": "serial_panels", "table_id": 12, "field": "project_id", "id": 13, "value": "1", "is_new": 1},
            {"user_id": 1, "table": "serial_panels", "table_id": 12, "field": "serial", "id": 14, "value": "test2", "is_new": 1},
            {"user_id": 1, "table": "serial_panels", "table_id": 12, "field": "description", "id": 15, "value": "", "is_new": 1},
            
            {"user_id": 1, "table": "mediables", "table_id": 3, "field": "media", "id": 16, "value": "/data/user/0/ir.yektadg.khorshid_taban/app_flutter/1718438170127.jpg", "is_media": 1, "is_new": 1},
            {"user_id": 1, "table": "mediables", "table_id": 3, "field": "mediable_type", "id": 17, "value": "projects", "is_new": 1},
            {"user_id": 1, "table": "mediables", "table_id": 3, "field": "mediable_id", "id": 18, "value": "1", "is_new": 1},
            {"user_id": 1, "table": "mediables", "table_id": 3, "field": "tag", "id": 19, "value": "inverter_serial_number_image", "is_new": 1},
            {"user_id": 1, "table": "mediables", "table_id": 4, "field": "media", "id": 20, "value": "/data/user/0/ir.yektadg.khorshid_taban/app_flutter/1718438173321.jpg", "is_media": 1, "is_new": 1},
            
            {"user_id": 1, "table": "mediables", "table_id": 4, "field": "mediable_type", "id": 21, "value": "projects", "is_new": 1},
            {"user_id": 1, "table": "mediables", "table_id": 4, "field": "mediable_id", "id": 22, "value": "1", "is_new": 1},
            {"user_id": 1, "table": "mediables", "table_id": 4, "field": "tag", "id": 23, "value": "inverter_serial_number_image", "is_new": 1},
            
            {"user_id": 1, "table": "projects", "table_id": 1, "field": "sim_card_number", "id": 24, "value": "123", "is_new": 0},
            {"user_id": 1, "table": "mediables", "table_id": 5, "field": "media", "id": 25, "value": "/data/user/0/ir.yektadg.khorshid_taban/app_flutter/1718467953704.jpg", "is_media": 1, "is_new": 1},
            
            {"user_id": 1, "table": "mediables", "table_id": 5, "field": "mediable_type", "id": 26, "value": "serial_panels", "is_new": 1},
            {"user_id": 1, "table": "mediables", "table_id": 5, "field": "mediable_id", "id": 27, "value": "3", "is_new": 1},
            {"user_id": 1, "table": "mediables", "table_id": 5, "field": "tag", "id": 28, "value": "description", "is_new": 1},
            {"user_id": 1, "table": "serial_panels", "table_id": 13, "field": "project_id", "id": 29, "value": "2", "is_new": 1},
            {"user_id": 1, "table": "serial_panels", "table_id": 13, "field": "serial", "id": 30, "value": "test", "is_new": 1},
            
            {"user_id": 1, "table": "serial_panels", "table_id": 13, "field": "description", "id": 31, "value": "test test", "is_new": 1},
            {"user_id": 1, "table": "mediables", "table_id": 6, "field": "media", "id": 32, "value": "/data/user/0/ir.yektadg.khorshid_taban/app_flutter/1718467976456.jpg", "is_media": 1, "is_new": 1},
            
            {"user_id": 1, "table": "mediables", "table_id": 6, "field": "mediable_type", "id": 33, "value": "serial_panels", "is_new": 1},
            {"user_id": 1, "table": "mediables", "table_id": 6, "field": "mediable_id", "id": 34, "value": "13", "is_new": 1},
            {"user_id": 1, "table": "mediables", "table_id": 6, "field": "tag", "id": 35, "value": "description", "is_new": 1}
        ]
    }',
    'mediables-1-media-2' => 
        \Illuminate\Http\UploadedFile::__set_state(array(
            'test' => false,
            'originalName' => '1718436186495.jpg',
            'mimeType' => 'image/jpeg',
            'error' => 0,
            'hashName' => NULL,
        )),
    'mediables-2-media-6' => 
        \Illuminate\Http\UploadedFile::__set_state(array(
            'test' => false,
            'originalName' => '1718437998055.jpg',
            'mimeType' => 'image/jpeg',
            'error' =>        0,
        'hashName' => NULL,
    )),
    'mediables-3-media-16' => 
        \Illuminate\Http\UploadedFile::__set_state(array(
            'test' => false,
            'originalName' => '1718438170127.jpg',
            'mimeType' => 'image/jpeg',
            'error' => 0,
            'hashName' => NULL,
        )),
    'mediables-4-media-20' => 
        \Illuminate\Http\UploadedFile::__set_state(array(
            'test' => false,
            'originalName' => '1718438173321.jpg',
            'mimeType' => 'image/jpeg',
            'error' => 0,
            'hashName' => NULL,
        )),
    'mediables-5-media-25' => 
        \Illuminate\Http\UploadedFile::__set_state(array(
            'test' => false,
            'originalName' => '1718467953704.jpg',
            'mimeType' => 'image/jpeg',
            'error' => 0,
            'hashName' => NULL,
        )),
    'mediables-6-media-32' => 
        \Illuminate\Http\UploadedFile::__set_state(array(
            'test' => false,
            'originalName' => '1718467976456.jpg',
            'mimeType' => 'image/jpeg',
            'error' => 0,
            'hashName' => NULL,
        )),
);

