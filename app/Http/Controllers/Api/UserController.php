<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\User\CreateOrUpdateUserRequest;
use App\Http\Requests\Api\User\ModifyLastUpdateRequest;
use App\Models\SerialPanel;
use App\Models\User;
use App\Models\UserUpdate;
use App\Services\SyncDataService;
use Carbon\Carbon;
use DateTime;
use Hek<PERSON>inasser\Verta\Verta;
use Illuminate\Http\Request;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Exception;
use Illuminate\Support\Facades\Log;
use Spatie\Permission\Models\Role;
use Illuminate\Validation\ValidationException;
use MediaUploader;
use PhpOffice\PhpSpreadsheet\Shared\Trend\Trend;

class UserController extends Controller
{

    public function index(Request $request)
    {
        $request->validate([
            'searchText' => 'nullable|string',
            'limit' => 'nullable|integer|min:1',
            'offset' => 'nullable|integer|min:0',
        ], [
            'limit.required_with' => 'The limit field is required when offset is present.',
        ]);

        // Additional validation rule to check that limit is set if offset is set
        $validator = Validator::make($request->all(), [
            'limit' => 'required_with:offset'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation Error',
                'errors' => $validator->errors(),
            ], 422);
        }

        $searchText = $request->input('searchText');
        $limit = $request->input('limit');
        $offset = $request->input('offset');

        $user = auth()->user()->userUpdate;
        $users = User::withTrashed() // Include soft-deleted users
        // ->whereHas('roles', function ($query) {
        //     $query->where('name','!=', 'admin');
        // })
            ->when(!empty($searchText), fn(Builder $query) => $query
                ->where(function ($query) use ($searchText) {
                    $query->where('national_code', 'like', "%$searchText%")
                        ->orWhere('first_name', 'like', "%$searchText%")
                        ->orWhere('last_name', 'like', "%$searchText%");
                }))
            ->when(!empty($limit), fn(Builder $query) => $query->limit($limit))
            ->when(!empty($offset), fn(Builder $query) => $query->offset($offset))
            ->when($user->last_user_update, fn(Builder $query) => $query->where('sync_update', '>', $user->last_user_update))
            ->orderBy('sync_update', 'asc')
            ->limit(700)
            ->get();

        $count = User::withTrashed()
        // ->whereHas('roles', function ($query) {
        //     $query->where('name','!=', 'admin');
        // })
            ->when($user->last_user_update, fn(Builder $query) => $query->where('sync_update', '>', $user->last_user_update))
            ->count();


        return apiResponse()
            ->withMessage('کاربران با موفقیت دریافت شدند')
            ->withData([
                'total' => $count,
                'filtered' => $users->count(),
                'users' => $users,
            ]);
    }

    public function show(Request $request)
    {
        $user = User::find($request->input('id'));
        return apiResponse()
            ->withStatusCode(200)
            ->withMessage('کاربر با موفقیت دریافت شد')
            ->withData([
                'user' => $user
            ]);
    }


    public function recent(Request $request)
    {
        $user = auth()->user();
        $users = User::where('updated_at', '>', $user->last_user_update)
            ->get();

        return apiResponse()
            ->withMessage('کاربران با موفقیت دریافت شدند')
            ->withData([
                'users' => $users
            ]);
    }


    public function createOrUpdate(Request $request, SyncDataService $syncDataService)
    {
        // TODO: creator_id and updater_id
        return $syncDataService->handle($request);
    }


    public function modifyLastUserUpdate(ModifyLastUpdateRequest $request)
    {
        $user = auth()->user();

        // $utc_time = Carbon::parse($request->last_update, 'UTC');
        // $tehran_time = $utc_time->setTimezone('Asia/Tehran');
        $userUpdate = UserUpdate::firstOrCreate(
            ['user_id' => $user->id],
            [
                'last_project_update' => null,
                'last_user_update' => null,
                'last_todo_update' => null,
                'last_media_update' => null
            ]
        );
        $userUpdate->last_user_update = $request->sync_update;
        $userUpdate->save();

        return apiResponse()
            ->withMessage('کاربر با موفقیت بروزرسانی شد')
            ->withData(['user' => $user]);
    }

    public function modifyLastProjectUpdate(ModifyLastUpdateRequest $request)
    {
        $user = auth()->user();
        // $utc_time = Carbon::parse($request->last_update, 'UTC');
        // $tehran_time = $utc_time->setTimezone('Asia/Tehran');
        $userUpdate = UserUpdate::firstOrCreate(
            ['user_id' => $user->id],
            [
                'last_project_update' => null,
                'last_user_update' => null,
                'last_todo_update' => null,
                'last_media_update' => null
            ]
        );
        $userUpdate->last_project_update = $request->sync_update;
        $userUpdate->save();
      

        return apiResponse()->withMessage('کاربر با موفقیت بروزرسانی شد')->withData(['user' => $user]);
    }

    public function modifyLastMediaUpdate(ModifyLastUpdateRequest $request)
    {
        $user = auth()->user();
        // $utc_time = Carbon::parse($request->last_update, 'UTC');
        // $tehran_time = $utc_time->setTimezone('Asia/Tehran');
        $userUpdate = UserUpdate::firstOrCreate(
            ['user_id' => $user->id],
            [
                'last_project_update' => null,
                'last_user_update' => null,
                'last_todo_update' => null,
                'last_media_update' => null
            ]
        );
        $userUpdate->last_media_update = $request->sync_update;
        $userUpdate->save();

     

        return apiResponse()->withMessage('کاربر با موفقیت بروزرسانی شد')->withData(['user' => $user]);
    }

    public function modifyLastTodoUpdate(ModifyLastUpdateRequest $request)
    {
        $user = auth()->user();

        // $utc_time = Carbon::parse($request->last_update, 'UTC');
        // $tehran_time = $utc_time->setTimezone('Asia/Tehran');

        $userUpdate = UserUpdate::firstOrCreate(
            ['user_id' => $user->id],
            [
                'last_project_update' => null,
                'last_user_update' => null,
                'last_todo_update' => null,
                'last_media_update' => null
            ]
        );
        $userUpdate->last_todo_update = $request->sync_update;
        $userUpdate->save();
      
        return apiResponse()
            ->withMessage('کاربر با موفقیت بروزرسانی شد')
            ->withData(['user' => $user]);
    }


}