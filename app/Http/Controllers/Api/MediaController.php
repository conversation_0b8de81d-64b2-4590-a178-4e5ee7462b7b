<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Project;
use Carbon\Carbon;
use Illuminate\Database\Query\Builder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class MediaController extends Controller
{

    public function index(Request $request)
    {
        $url = config('app.url') . '/storage';

        $user = auth()->user()->userUpdate;
        $limit = $request->input('limit');
        $offset = $request->input('offset');


        $medias = DB::table('media as m')->select(
            DB::raw("CONCAT('" . $url . '/' . "', m.directory, '/', m.filename, '.', m.extension) as media")
            ,'md.sync_update as sync_update', 'm.id', 'md.mediable_type', 'md.mediable_id', 'md.tag', 'md.order',
            DB::raw("CONVERT_TZ(md.created_at, '+03:30', '+00:00') as created_at"),
            DB::raw("CONVERT_TZ(md.updated_at, '+03:30', '+00:00') as updated_at"),
            DB::raw("CONVERT_TZ(md.deleted_at, '+03:30', '+00:00') as deleted_at"),
            DB::raw('m.updated_at as mediaupdated')
        )
            ->join('mediables as md', 'md.media_id', '=', 'm.id')
            ->when($user->last_media_update, fn($query) => $query->where(fn(Builder $query) => $query->where('md.sync_update', '>', $user->last_media_update)
                ->orWhere('md.sync_update', '>', $user->last_media_update)))
            ->when(!empty($limit), fn($query) => $query->limit($limit))
            ->when(!empty($offset), fn($query) => $query->offset($offset))
            ->orderBy('sync_update', 'asc')
            ->get()->map(function ($media) {
                $media->mediable_type = app($media->mediable_type)->getTable();

                return $media;
            });

        $total = DB::table('media as m')->select(
            DB::raw("CONCAT('" . $url . '/' . "', m.directory, '/', m.filename, '.', m.extension) as media")
            ,'md.sync_update as sync_update', 'm.id', 'md.mediable_type', 'md.mediable_id', 'md.tag', 'md.order',
            DB::raw("CONVERT_TZ(md.created_at, '+03:30', '+00:00') as created_at"),
            DB::raw("CONVERT_TZ(md.updated_at, '+03:30', '+00:00') as updated_at"),
            DB::raw("CONVERT_TZ(md.deleted_at, '+03:30', '+00:00') as deleted_at"),
            DB::raw('m.updated_at as mediaupdated')
        )
            ->join('mediables as md', 'md.media_id', '=', 'm.id')
            ->when($user->last_media_update, fn($query) => $query->where(fn(Builder $query) => $query->where('md.sync_update', '>', $user->last_media_update)
                ->orWhere('md.sync_update', '>', $user->last_media_update)))->count();

        return apiResponse()
            ->withMessage('تصاویر با موفقیت دریافت شدند')
            ->withData([
                'total' => $total,
                'filtered' => $medias->count(),
                'medias' => $medias
            ]);
    }


    public function recent(Request $request)
    {

    }
}
