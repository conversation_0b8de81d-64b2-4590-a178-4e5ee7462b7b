<?php

namespace App\Http\Controllers;

use App\Http\Requests\auth\LoginRequest;
use App\Http\Requests\auth\SendSmsRequest;
use App\Models\User;
use App\Responses\ApiResponse;
use App\Responses\ServiceResponse;
use App\Services\SmsService;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpFoundation\Response as ResponseAlias;
use Tymon\JWTAuth\Facades\JWTAuth;

class AuthController extends Controller
{

    public function loginPage()
    {
        // $list=[247,205 , 254, 260, 264, 269, 282, 283, 285, 286, 287, 289, 290, 292,
        // 293, 297, 298, 300, 301, 302, 304, 305, 307, 310, 311, 313, 314, 316,
        // 318, 319, 321, 324, 328, 329, 331, 334, 336, 338, 339, 341, 343, 344,
        // 346, 347, 349, 351, 352, 353, 354, 355, 356, 357, 358, 359, 360, 367,
        // 464, 480, 482, 1598, 1601, 1606, 1636, 1644, 1649, 2002, 2205, 2242,
        // 2360, 2605,251,371,274,2580,280,295,308];

        // $list2=[2401,2118,800,2116,793,2114,790,984,2111,993];
        // $projects=\App\Models\Project::whereIn('id',$list2)->get();

        // foreach($projects as $project){
        //     $existingDepositIds = $project->deposit->pluck('id')->toArray();
        //     // if($project->id!=247) continue;
        //     $deposits=\App\Models\Deposit::whereIn('id', $existingDepositIds)->get();
        //     // dd($deposits->count());

        //     foreach($deposits as $deposit){
        //         $deposit->updated_at=now();
        //         $deposit->save();
        //         // dd($deposit);
        //     }
           
            
        //     $project->updated_at=now();
        //     $project->save();
           
        // //   dd($project->id);
        // }
        // $projects=\App\Models\Project::where('id',280)->get();
        // foreach($projects as $project){
        //     $existingDepositIds = $project->deposit->pluck('id')->toArray();
        //     $deposits=\App\Models\Deposit::whereIn('id', $existingDepositIds)->get();
        //     foreach($deposits as $deposit){
        //         $deposit->updated_at=now();
        //         $deposit->save();
        //     }
        //     $project->updated_at=now();
        //     $project->save();
        // }
        if (!auth()->guard('web')->check())
            return view('auth.login');
        else
            return redirect()->route('dashboard');
    }

    public function otpPage()
    {
        if (!auth()->guard('web')->check())
            return view('auth.otp');
        else
            return redirect()->route('dashboard');
    }

    public function loginWeb(LoginRequest $request)
    {
        $credentials = $request->only('phone', 'password');

        // Attempt to authenticate the user using the 'email' field
        if (!auth()->guard('web')->attempt($credentials)) {
            throw ValidationException::withMessages([
                'phone' => 'اطلاعات وارد شده معتبر نیست !',
            ]);
        }

        $user = auth()->guard('web')->user();

        return redirect()->route('dashboard');
    }

    public function logoutWeb()
    {
        auth()->guard('web')->logout();
        return redirect()->route('auth.login');
    }


    public function sendSms(SendSmsRequest $request)
    {

        $phone = $request->phone;
        $user = User::where('phone', $phone)->first();
        abort_if(!$user, 404, 'کاربر پیدا نشد');

        DB::beginTransaction();
        try {
            $code = random_int(100000, 999999);
            if (isset($user->otp_code_sent) && now() < $user->otp_code_sent->addMinutes(3))
                return response()->json(['message' => 'کد ارسال شد', 'time' => now()->diffInSeconds($user->otp_code_sent->addMinutes(3))]);

            if (app()->environment('local') || $user->phone == "091234admin") {
                $user->otp_code = "654321";
                $user->save();
                $code = "123456";
            }
            $user->otp_code = $code;
            $user->otp_code_sent = now();
            $hash = "";
            $user->save();
            if (!app()->environment('local')) {
                (new SmsService())->send($user->phone, ['otp' => $code, 'hash' => $hash]);
            }
            DB::commit();
        } catch (Exception $exception) {
            DB::rollBack();
            return response()->json(['message' => $exception], 500);
        }
        return response()->json(['message' => 'کد ارسال شد', 'time' => 180]);
    }

    public function checkPhoneCode(Request $request)
    {
        $phone = $request->phone;
        $code = $request->otp_code;

        $user = User::where('phone', $phone)->first();
        if (!$user) {
            throw ValidationException::withMessages([
                'phone' => 'کاربری با این شماره همراه پیدا نشد !',
            ]);
        }

        if ($user->otp_code != $code || now() > $user->otp_code_sent->addMinutes(3)) {
            throw ValidationException::withMessages([
                'code' => 'کد وارد شده معتبر نیست !',
            ]);
        }

        auth()->login($user);
        return redirect()->route('dashboard');
    }


}
