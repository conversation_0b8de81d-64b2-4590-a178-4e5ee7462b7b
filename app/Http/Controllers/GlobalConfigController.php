<?php

namespace App\Http\Controllers;

use App\Models\GlobalConfig;
use Illuminate\Http\Request;

class GlobalConfigController extends Controller
{
    function checkUpdate(Request $request)
    {
        $currentVersion = GlobalConfig::where('label', GlobalConfig::APP_VERSION)->first();
        if (empty($currentVersion))
            return apiResponse()->withError('no app version')->withStatusCode(500);
        $value = json_decode($currentVersion->value, true);
        if ($request->version_code < $value['version_code'] ?? 0) {
            return apiResponse()
                ->withMessage('successful....')
                ->withData($value);
        }
        return apiResponse()
            ->withMessage('successful....')
            ->withStatusCode(400);
    }
}
