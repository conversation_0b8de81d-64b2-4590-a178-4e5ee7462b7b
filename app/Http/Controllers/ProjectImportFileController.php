<?php

namespace App\Http\Controllers;

use App\Http\Requests\User\ImportUserByFileRequest;
use App\Models\Project;
use App\Models\User;
use App\Traits\UsesXlsxToArray;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\HttpFoundation\Response as ResponseAlias;
use Illuminate\Support\Str;

class ProjectImportFileController extends Controller
{
    use UsesXlsxToArray;

    public function changeMehrsanWithImportFile(Request $request)
    {

        return view('projects.import-file.create');
    }

  

    function convertPersianDigitsToEnglish($input) {
        if (!$input)
            return null;
        $persianDigits = ['۰', '۱', '۲', '۳', '۴', '۵', '۶', '۷', '۸', '۹'];
        $englishDigits = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
        
        return str_replace($persianDigits, $englishDigits, $input);
    }

    public function storeMehrsanWithImportFile(ImportUserByFileRequest $request)
    {
        ini_set('memory_limit', '-1');
        ini_set('max_execution_time', -1);
        try {
            DB::beginTransaction();
            $filename = $this->uploadFile();

            $rows = $this->checkFileAndGetRows($filename);

            $processed = 0;
            $errors = 0;
            $noProjectUsers = 0;

            $mehrsanStatusMap = $this->getMehrsanStatusMap();

            foreach ($rows as $row) {
                $collect = new \stdClass();
                try {
                    if ($row['مرحله_جاری'] && $row['کد_ملی']) {

                        $statusLabel = $this->convertPersianDigitsToEnglish($row['مرحله_جاری']);
                        $national_code = $this->convertPersianDigitsToEnglish($row['کد_ملی']);

                        $mappedStatus = $mehrsanStatusMap[$statusLabel] ?? '';

                        if (empty($mappedStatus)) {
                            $errors++;
                            session()->flash('error_toast', __('وضعیت مهرسان نامعتبر است: :status', ['status' => $statusLabel]));
                            continue;
                        }

                        $collect->status = $mappedStatus;
                        $collect->national_code = empty($national_code) ? '' : $national_code;
                        $result = $this->processInvitation(
                            $collect
                        );
                        if ($result === 'no_project') {
                            $noProjectUsers++;
                        } else {
                            $processed++;
                        }
                    }
                } catch (Exception $exception) {
                    logger($exception->getMessage());
                    $errors++;
                    continue;
                }
            }
            //handel show processed success and errors
            $message = __('وضعیت مهرسان با موفقیت ویرایش شد.');
            if ($processed > 0) {
                $message .= " " . __('تعداد موفق: :count', ['count' => $processed]);
            }
            if ($errors > 0) {
                $message .= " " . __('تعداد خطا: :count', ['count' => $errors]);
            }
            if ($noProjectUsers > 0) {
                $message .= " " . __('تعداد بدون پروژه: :count', ['count' => $noProjectUsers]);
            }
            DB::commit();
            session()->flash('created_toast', $message);
            return redirect()->route('projects.create-import-file');
        } catch (\Exception $e) {
            DB::rollBack();
            logger($e->getMessage());
            session()->flash('error_toast', __('مشکلی رخ داده است !'));
            return redirect()->route('projects.create-import-file');
        }


    }

    private function getMehrsanStatusMap(): array
    {
        $mapping = config('sun.params.mehrsanStatus', []);
        $map = [];
        foreach ($mapping as $item) {
            $map[$item['label']] = $item['value'];
        }
        return $map;
    }

    private function uploadFile(): string
    {
        $uuid = now()->timestamp;

        $file = request()->file('file');

        $extension = $file->extension();
        $filename = "$uuid.$extension";

        $file->storeAs('', $filename, 'uploads');

        return $filename;
    }

    private function checkFileAndGetRows(string $filename): array
    {
        list($rows, $columns) = $this->xlsxToArray(storage_path('app/uploads/') . $filename);

        $this->checkColumns($columns);

        return $rows;
    }

    private function checkColumns($columns)
    {
        $requiredColumns = [
            'مرحله_جاری',
            'کد_ملی'
        ];

        foreach ($requiredColumns as $column) {
            $actualColumn = Str::title(str_replace('_', ' ', $column));
            abort_if(!in_array($column, $requiredColumns), ResponseAlias::HTTP_FORBIDDEN, str_replace(':column', $actualColumn, __(":column is required")));
        }
    }

    private function processInvitation($request)
    {
        // $request=normalizeRequestCharacter($request);
        $user = User::where('national_code', $request->national_code)->first();
       
        if ($user && $user->projects) {
            foreach ($user->projects as $project) { // TODO: check 
                if($project->mehrsan_status!=$request->status){
                    $project->mehrsan_status = $request->status;
                    $project->save();
                }
                
            }
            return 'processed';
        } else {
           //handel not exist project for user
           return 'no_project';
        }
    }

}
