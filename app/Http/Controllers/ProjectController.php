<?php

namespace App\Http\Controllers;

use App\Models\Bank;
use App\Models\Checking;
use App\Models\Defect;
use App\Models\Deposit;
use App\Models\ExecutionDefect;
use App\Models\Project;
use App\Models\User;
use Illuminate\Http\Request;
use App\Models\IranCity;
use App\Models\IranCounty;
use App\Models\IranProvince;
use App\Models\Mediable;
use App\Models\ProjectSupport;
use App\Models\SerialPanel;
use App\Models\TodolistCategory;
use App\Models\Village;
use App\Models\Visitor;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;
use Plank\Mediable\Media;
use MediaUploader;

class ProjectController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //on lack of permission
        if (!auth()->user()->can('view-projects-list')) return abort(403);

        $statuses = config('sun.params.TranslateStatuses');

        $supportOrganizations = config('sun.params.supportOrganization');
        $accessedSupportOrganizationValues = auth()->user()->supportOrganizations()->pluck('support_organization_code')->toArray() ?? [];

        $support_organizations = array_filter($supportOrganizations, function ($organization) use ($accessedSupportOrganizationValues) {
            return in_array($organization['value'], $accessedSupportOrganizationValues);
        });

        $userAccessedProvinceIds = auth()->user()->accessedProvinces()->pluck('province_id')->toArray() ?? [];
        $provinces = IranProvince::whereIn('id', $userAccessedProvinceIds)->get();
        $county = IranCounty::get();
        $city = IranCity::get();

        $payment_statuses = config('sun.params.paymentStatus');
        $accounting_status = config('sun.params.accountingStatus');
        return view('projects.index', compact(['statuses' , 'support_organizations' , 'provinces' ,'county','city', 'payment_statuses','accounting_status']));
    }



    public function getProjects(Request $request)
    {
        $supportOrganization = isset($request['supportOrganization']) ? $request['supportOrganization'] : [] ;
        $province_id = isset($request['province']) ? $request['province'] : [] ;
        $city_id = isset($request['city']) ? $request['city'] : [] ;
        $county_id = isset($request['county']) ? $request['county'] : [] ;
        $paymentStatus = isset($request['paymentStatus']) ? $request['paymentStatus'] : [] ;
        $accounting_status = isset($request['accounting_status']) ? $request['accounting_status'] : [] ;
        $accessedSupportOrganizationValues = auth()->user()->supportOrganizations()->pluck('support_organization_code')->toArray() ?? [];
        $userAccessedProvinceIds = auth()->user()->accessedProvinces()->pluck('province_id')->toArray() ?? [];
        // Base query to fetch projects with necessary relationships
        $projects = Project::select([
                'projects.id',
                DB::raw('(CASE
                    WHEN owners.id IS NOT NULL
                        THEN CONCAT(COALESCE(owners.first_name, ""), " ", COALESCE(owners.last_name, ""))
                    ELSE "__"
                END) as owner_details'),
                'support_organization',
                'provinces.name as province_name',
                'city.name as city_name',
                'county.name as county_name',
                'current_phase',
                'payment_status',
                'payment_date',
                'inspection_status',
                'mehrsan_status'
            ])
            ->join('users as owners', 'projects.user_id', '=', 'owners.id')
            ->join('iran_provinces as provinces', 'projects.province_id', '=', 'provinces.id')
            ->join('iran_cities as city', 'projects.city_id', '=', 'city.id')
            ->join('iran_counties as county', 'projects.county_id', '=', 'county.id')
            ;

        if (!empty($supportOrganization)) {
            $projects->whereIn('support_organization', $supportOrganization);
        }else{
            $projects->whereIn('support_organization', $accessedSupportOrganizationValues);

        }

        if (!empty($province_id)) {
            $projects->whereIn('projects.province_id', $province_id);
        }else{
            $projects->whereIn('projects.province_id', $userAccessedProvinceIds);

        }
        if (!empty($city_id)) {
            $projects->whereIn('projects.city_id', $city_id);
        }
        if (!empty($county_id)) {
            $projects->whereIn('projects.county_id', $county_id);
        }

        if (!empty($paymentStatus)) {
            $projects->whereIn('payment_status', $paymentStatus);
        }
        if (!empty($accounting_status)) {
            if(in_array('null',$accounting_status)){
                $projects->whereNull('accounting_status');
            }else{
                $projects->whereIn('accounting_status', $accounting_status);
            }

        }

        // Search filter
        if ($request->search['value'] != '' && $request->search['value']!=null) {
            $projects->where(function($query) use ($request) {
                $query->where('owners.first_name', 'like', '%' . $request->search['value'] . '%')
                      ->orWhere('owners.last_name', 'like', '%' . $request->search['value'] . '%')
                      ->orWhere('owners.phone', 'like', '%' . $request->search['value'] . '%')
                      ->orWhere('owners.national_code', 'like', '%' . $request->search['value'] . '%')
                      ->orWhere('provinces.name', 'like', '%' . $request->search['value'] . '%')
                      ->orWhere('city.name', 'like', '%' . $request->search['value'] . '%')
                      ->orWhere('county.name', 'like', '%' . $request->search['value'] . '%')
                      ->orWhere('mehrsan_status', 'like', '%' . $request->search['value'] . '%')
                      ->orWhere('current_phase', 'like', '%' . $request->search['value'] . '%')

                      ->orWhere('support_organization', 'like', '%' . $request->search['value'] . '%');
            });
        }

        // Apply ordering
        if (isset($request->order[0]['column'])) {
            $orderableColumns = [
                1 => 'owner_details',
                2 => 'support_organization',
                3 => 'province_name',
                4 => 'county_name',
                5 => 'city_name',
                6 => 'current_phase',
                7 => 'payment_date',
                8 => 'inspection_status',
                9 => 'mehrsan_status'
            ];

            $orderBy = $orderableColumns[$request->order[0]['column']] ?? 'projects.updated_at';
            $direction = $request->order[0]['dir'] == 'asc' ? 'asc' : 'desc';

            $projects->orderBy($orderBy, $direction);
        } else {
            $projects->orderBy('projects.updated_at', 'desc');
        }

        // Clone the query for counting records
        $projectQuery = clone $projects;
        $count = $projectQuery->distinct('projects.id')->count();

        // Paginate results
        $projects = $projects->groupBy('projects.id')
            ->offset($request->start)
            ->limit($request->length)
            ->get();

        foreach($projects as $project){
            $project->support_organization = $project->getPersianSupportOrganizationAttribute();
            $project->current_phase = $project->getPersianCurrentPhaseAttribute();
            $project->payment_status = $project->getPersianPaymentStatusAttribute();
            $project->payment_date = isset($project->payment_date) ? convertDate($project->payment_date, 0) : "__";
            $project->inspection_status = $project->getPersianInspectionStatusAttribute();
            $project->mehrsan_status = $project->getPersianMehrsanAttribute();
            $project->actions = "
            <div class='d-flex'>
                <div class='ms-2'>
                    <a href='". route('projects.show', $project->id) ."'
                        class='btn btn-sm btn-icon btn-light btn-active-light-primary'
                        data-kt-menu-trigger='click'
                        data-kt-menu-placement='bottom-end'>
                        <!--begin::Svg Icon | path: icons/duotune/coding/cod007.svg-->
                        <span class='svg-icon svg-icon-5 m-0'>
                            <i class='far fa-edit fs-4'></i>
                        </span>
                        <!--end::Svg Icon-->
                    </a>
                </div>
                " . (auth()->user()->can('delete-projects') ? "
                <div class='ms-2'>
                    <form class='delete-form'
                            action='" . route('projects.destroy', $project) ."'
                            method='post'
                            style='display: inline-block;'
                            id='". $project->id ."'>
                            " . method_field('DELETE') . "
                            ". csrf_field() ."
                        <a id='delete'
                            class='btn btn-sm btn-icon btn-light btn-active-light-danger'>
                            <span class='svg-icon svg-icon-5 m-0'>
                                <i class='text-dark-50 fonticon-trash fs-2'></i>
                            </span>
                        </a>
                    </form>
                </div>

                  " : "") . "
                  " . (auth()->user()->can('update-project_supports') ? "
                <div class='ms-2'>
                    <a class='btn btn-sm btn-icon btn-light btn-active-light-primary'
                        title='پشتیبانی ها'
                        href='". route('third-party-support.index' , [$project]). "'
                    >
                        <i class='icon fas fa-podcast'></i>
                    </a>
                </div>
                 " : "") . "
            </div>
            ";

        }


        // Return the data formatted for DataTables
        $toReturn = [
            'data' => $projects,
            'recordsTotal' => $projects->count(),
            'recordsFiltered' => $count,
            'draw' => (int)$request->draw
        ];

        return response()->json($toReturn);
    }




    public function search(Request $request)
    {

        $supportOrganization = isset($request['supportOrganization']) ? $request['supportOrganization'] : [] ;
        $province_id = isset($request['province_id']) ? $request['province_id'] : [] ;
        $paymentStatus = isset($request['paymentStatus']) ? $request['paymentStatus'] : [] ;

        // Initialize the query
        $query = Project::query();

        // Apply filters
        if (!empty($supportOrganization)) {
            $query->whereIn('support_organization', $supportOrganization);
        }

        if (!empty($province_id)) {
            $query->whereIn('province_id', $province_id);
        }

        if (!empty($paymentStatus)) {
            $query->whereIn('payment_status', $paymentStatus);
        }

        // Execute the query
        $projects = $query->orderBy('updated_at', 'desc')->get();

        $statuses = config('sun.params.TranslateStatuses');
        // $projects = Project::orderBy('updated_at', 'desc')->get();
        $support_organizations = config('sun.params.supportOrganization');
        $provinces = IranProvince::all();
        $payment_statuses = config('sun.params.paymentStatus');
        $search = $request->all();
        return view('projects.index', compact(['projects', 'statuses' , 'support_organizations' , 'provinces' , 'payment_statuses' , 'search' ]));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        ini_set('memory_limit', '-1');
        ini_set('max_execution_time', -1);
        //on lack of permission
        if (!auth()->user()->can('create-projects')) return abort(403);

        $user = auth()->user();

        $roles = $user->roles->pluck('name')->toArray();
        if (in_array('admin' , $roles ))
        {
            $role = 'admin';
        }
        elseif(in_array('accounting' , $roles ))
        {
            $role = 'accounting';
        }
        elseif(in_array('support' , $roles ))
        {
            $role = 'support';
        }
        elseif(in_array('design' , $roles ))
        {
            $role = 'design';
        }
        elseif(in_array('warehouse' , $roles ))
        {
            $role = 'warehouse';
        }
        elseif(in_array('execution' , $roles ))
        {
            $role = 'execution';
        }
        else
            $role = $roles[0];
        $visitors = User::withoutRole('customer')->get();
        $users = User::all();
        $payment_statuses = config('sun.params.paymentStatus');
        $mehrsan_statuses = config('sun.params.mehrsanStatus');
        $insurance_statuses = config('sun.params.insuranceStatus');
        $meter_assignment_statuses = config('sun.params.meterAssignmentStatus');
        $support_statuses = config('sun.params.supportStatus');
        $supports = User::withoutRole('customer')->get();
        $inspection_statuses = config('sun.params.inspectionStatus');
        $design_results = config('sun.params.designResults');
        $current_phases = config('sun.params.currentPhases');
        $inverter_models = config('sun.params.inverterModels');
        $panel_powers = config('sun.params.panelPowers');
        $panel_types = config('sun.params.paneltypes');
        // $support_organizations = config('sun.params.supportOrganization');
        $power_plant_capacities = config('sun.params.powerPlantCapacity');
        $project = null;
        $customers = User::permission('access-customer-dashboard')->get();

        // $provinces = IranProvince::all();
        $counties = IranCounty::all();
        $cities = IranCity::all();
        $designers = User::withoutRole('customer')->get();
        $installers = User::withoutRole('customer')->get();

        $checkings = TodolistCategory::where('type', 'checking')->where('status', 'active')->get();
        $defects = TodolistCategory::where('type', 'defects')->where('status', 'active')->get();
        $execution_defects = TodolistCategory::where('type', 'execution-defects')->where('status', 'active')->get();
        $project_serial_panels = Collection::make([]);
        $project_supports = Collection::make([]);
        $project_deposits = Collection::make([]);
        $project_checkings = Collection::make([]);
        $project_defects = Collection::make([]);
        $project_visitors = Collection::make([]);
        $project_media_items = Collection::make([]);
        $project_execution_defects = Collection::make([]);


        $supportOrganizations = config('sun.params.supportOrganization');
        $accessedSupportOrganizationValues = auth()->user()->supportOrganizations()->pluck('support_organization_code')->toArray() ?? [];

        $support_organizations = array_filter($supportOrganizations, function ($organization) use ($accessedSupportOrganizationValues) {
            return in_array($organization['value'], $accessedSupportOrganizationValues);
        });

        $userAccessedProvinceIds = auth()->user()->accessedProvinces()->pluck('province_id')->toArray() ?? [];
        $provinces = IranProvince::whereIn('id', $userAccessedProvinceIds)->get();


        return view('projects.create',
            compact([
                'designers',
                'installers',
                'role',
                'visitors',
                'users',
                'payment_statuses',
                'mehrsan_statuses',
                'insurance_statuses',
                'meter_assignment_statuses',
                'inspection_statuses',
                'design_results',
                'current_phases',
                'inverter_models',
                'panel_powers',
                'panel_types',
                'support_organizations',
                'project',
                'customers',
                'provinces',
                'counties',
                'cities',
                'power_plant_capacities',
                'support_statuses',
                'supports',
                'checkings',
                'defects',
                'project_visitors',
                'project_serial_panels',
                'project_supports',
                'project_deposits',
                'project_checkings',
                'project_defects',
                'project_media_items',
                'project_execution_defects',
                'execution_defects',
            ]));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {

    }




    public function updateOrCreateProjectDetail(Request $request, Project $project = null)
    {
        // dd($request);
        ini_set('memory_limit', '-1');
        ini_set('max_execution_time', -1);
        $request=normalizeRequestCharacter($request);
        $request->validate([
            'postal_code' => ['nullable', 'regex:/^\d{10}$/'],
            'support_organization' => ['required'],
            'province_id' => ['required'],
            'county_id' => ['required'],
            'city_id' => ['required'],
            'meter_serial_number_image'=>['nullable',$project==null ? Rule::unique('projects', 'meter_serial_number_image')->whereNull('deleted_at') : Rule::unique('projects', 'meter_serial_number_image')->ignore($project->id)->whereNull('deleted_at')],
            'sim_card_number'=>['nullable',$project==null ? Rule::unique('projects', 'sim_card_number')->whereNull('deleted_at') : Rule::unique('projects', 'sim_card_number')->ignore($project->id)->whereNull('deleted_at')],
            'project_visit_section.*.visit_date' => [
                function ($attribute, $value, $fail) use ($request) {
                    // Extract index from the attribute name (e.g., project_visit_section.0.visit_date)
                    $index = explode('.', $attribute)[1];

                    // Check if user_id exists for the corresponding index
                    $user_id = $request->input("project_visit_section.$index.user_id");

                    if (!empty($user_id) && empty($value)) {
                        $fail("تاریخ بازدید اجباری است وقتی بازدید کننده انتخاب شده است.");
                    }
                },
            ],
            'ware_postage_date' => ['required_with:ware_sender_id'],
            'meter_installation_date' => ['required_with:simban_id'],
            'panel_installation_date' => ['required_with:installer_id'],

        ]);
        $data =  $request->all();

        // dd($data);
        if (isset( $data["balance"]))
        {
            $data["balance"] = cleanData($data["balance"]);
        }

        if (isset( $data["discount"]))
        {
            $data["discount"] = cleanData($data["discount"]);
        }
        if (isset( $data["cost_of_materials"]))
        {
            $data["cost_of_materials"] = cleanData($data["cost_of_materials"]);
        }
        if (isset( $data["additional_costs_and_dorm_rent"]))
        {
            $data["additional_costs_and_dorm_rent"] = cleanData($data["additional_costs_and_dorm_rent"]);
        }
        $user_id = auth()->id();

        if (isset($data['village_id'])) {
            $villages = json_decode($data['village_id'], false, 512, JSON_THROW_ON_ERROR);
            foreach ($villages as $village) {
                if(intval($village->value) != 0){
                    $data['village_id'] = $village->value;
                }
                else{
                    $newVillage=Village::create([
                        'name' => $village->value,
                    ]);
                    $data['village_id'] = $newVillage->id;

                }
            }
        }

        if (isset($data['bank_id'])) {
            $banks = json_decode($data['bank_id'], false, 512, JSON_THROW_ON_ERROR);
            foreach ($banks as $bank) {
                if(intval($bank->value) != 0){
                    $data['bank_id'] = $bank->value;
                }
                else{
                    $newBank=Bank::create([
                        'name' => $bank->value,
                    ]);
                    $data['bank_id'] = $newBank->id;
                }
            }
        }
        if ($project) {
            return $this->updateProject($project, $data, $user_id);
        } else
            return $this->storeProject($user_id, $data);


    }

    public function storeProject($user_id, $data)
    {
        //on lack of permission
        if (!auth()->user()->can('create-projects')) return abort(403);
        //create
        // $data['creator_id'] = $user_id;
        // $data['updater_id'] = $user_id;
        $project = Project::create($data);

        if (isset($data['medias'])) {
            foreach (json_decode($data['medias'], true) as $media_id) {
                $media = Media::find($media_id);
                $project->attachMedia($media, 'send_list_comments');  // TODO: Mr Fouladi
            }
        }
        if (isset($data['material_cost_medias'])) {
            foreach (json_decode($data['material_cost_medias'], true) as $media_id) {
                $media = Media::find($media_id);
                $project->attachMedia($media, 'cost_of_materials'); // TODO: Mr Fouladi
            }
        }

        if (isset($data['additional_cost_medias'])) {
            foreach (json_decode($data['additional_cost_medias'], true) as $media_id) {
                $media = Media::find($media_id);
                $project->attachMedia($media, 'additional_costs_and_dorm_rent'); // TODO: Mr Fouladi
            }
        }


        if (isset($data['checking_medias'])) {
            foreach (json_decode($data['checking_medias'], true) as $media_id) {
                $media = Media::find($media_id);
                $project->attachMedia($media, 'checking_comment');
            }
        }

        if (isset($data['defect_medias'])) {
            foreach (json_decode($data['defect_medias'], true) as $media_id) {
                $media = Media::find($media_id);
                $project->attachMedia($media, 'defect_comment');
            }
        }

        if (isset($data['execution_defect_medias'])) {
            foreach (json_decode($data['execution_defect_medias'], true) as $media_id) {
                $media = Media::find($media_id);
                $project->attachMedia($media, 'execution_defect_comment'); // TODO: Mr Fouladi
            }
        }
        if (isset($data['design_medias'])) {
            foreach (json_decode($data['design_medias'], true) as $media_id) {
                $media = Media::find($media_id);
                $project->attachMedia($media, 'design_file_result'); // TODO: Mr Fouladi
            }
        }


        if (isset($data['inverter_medias'])) {
            $project->syncMedia(json_decode($data['inverter_medias'], true), 'inverter_serial_number_image');
        }

        if (isset($data['monitoring_medias'])) {
            $project->syncMedia(json_decode($data['monitoring_medias'], true), 'monitoring_code');
        }

        if (isset($data['meter_medias'])) {
            $project->syncMedia(json_decode($data['meter_medias'], true), 'meter_serial_number_image');
        }
        if (isset($data['structure_medias'])) {
            $project->syncMedia(json_decode($data['structure_medias'], true), 'structure_type_image');
        }


        // project_visit ------------------------------------------------
        if (isset($data['project_visit_section'])) {
            foreach ($data['project_visit_section'] as $visit_details) {
                if ($visit_details['user_id'])
                {
                    // Extract and remove the 'medias' key from the visitor details
                    $medias = json_decode($visit_details['medias'], true);
                    unset($visit_details['medias']);

                    // $visit_details['creator_id'] = $user_id;
                    // $visit_details['updater_id'] = $user_id;
                    $visit_details['project_id'] = $project->id;
                    $visitor = Visitor::create($visit_details);

                    // Convert $medias to an array if it's a string
                    $medias = is_array($medias) ? $medias : explode(',', $medias);
                    foreach ($medias as $mediaId) {
                        $media = Media::find($mediaId);
                        $visitor->attachMedia($media, 'comment');
                    }
                }
            }
        }

        // project_serial_panel ---------------------------------------
        if (isset($data['project_serial_panel'])) {
            foreach ($data['project_serial_panel'] as $panel_details) {
                if ($panel_details['serial'])
                {
                    $medias = json_decode($panel_details['medias'], true);
                    unset($panel_details['medias']);

                    // $panel_details['creator_id'] = $user_id;
                    // $panel_details['updater_id'] = $user_id;
                    $panel_details['project_id'] = $project->id;
                    $panel = SerialPanel::create($panel_details);

                    $panel->syncMedia($medias, 'description');
                }
            }
        }
        // project_support --------------------------------------------
        if (isset($data['project_support_section'])) {
            foreach ($data['project_support_section'] as $project_support) {
                if($project_support['status'])
                {
                    $creator_medias = json_decode($project_support['creator_medias'], true);
                    unset($project_support['creator_medias']);

                    $creator_medias = json_decode($project_support['supporter_medias'], true);
                    unset($project_support['supporter_medias']);

                    // $project_support['creator_id'] = $user_id;
                    // $project_support['updater_id'] = $user_id;
                    $project_support['project_id'] = $project->id;
                    $projectSupport = ProjectSupport::create($project_support);

                    $projectSupport->syncMedia($creator_medias, 'creator_comment');
                    $projectSupport->syncMedia($creator_medias, 'supporter_comment');
                }

            }
        }
        // project_deposit --------------------------------------------
        if (isset($data['project_deposit']) and $data['project_deposit']) {
            foreach ($data['project_deposit'] as $project_deposit) {
                if ($project_deposit['amount'])
                {
                    $medias = json_decode($project_deposit['medias'], true);
                    unset($project_deposit['medias']);

                    $cleanedString = preg_replace('/[^0-9,]/', '', $project_deposit['amount']);
                    $cleanedString = str_replace(',', '', $cleanedString);
                    $project_deposit['amount'] = (int)$cleanedString;

                    // Handle bank_id from Tagify
                    if (isset($project_deposit['bank_id']) && !empty($project_deposit['bank_id'])) {
                        try {
                            $banks = json_decode($project_deposit['bank_id'], true, 512, JSON_THROW_ON_ERROR);
                            if (is_array($banks) && count($banks) > 0) {
                                $bank = $banks[0];
                                if (isset($bank['value']) && intval($bank['value']) != 0) {
                                    $project_deposit['bank_id'] = $bank['value'];
                                } elseif (isset($bank['name']) && !empty($bank['name'])) {
                                    $newBank = Bank::create([
                                        'name' => $bank['name'],
                                    ]);
                                    $project_deposit['bank_id'] = $newBank->id;
                                }
                            }
                        } catch (\Exception $e) {
                            // If JSON decode fails, try to create a new bank with the string value
                            if (!empty($project_deposit['bank_id'])) {
                                $newBank = Bank::create([
                                    'name' => $project_deposit['bank_id'],
                                ]);
                                $project_deposit['bank_id'] = $newBank->id;
                            }
                        }
                    }

                    // $project_deposit['creator_id'] = $user_id;
                    // $project_deposit['updater_id'] = $user_id;
                    $project_deposit['project_id'] = $project->id;
                    $project_deposit['user_id'] = $data['user_id'];
                    $deposit = Deposit::create($project_deposit);

                    $medias = is_array($medias) ? $medias : explode(',', $medias);
                    foreach ($medias as $mediaId) {
                        $media = Media::find($mediaId);
                        $deposit->attachMedia($media, 'payment_date');  // TODO: tell to Mr Fouladi
                    }
                }
            }
        }
        // checking ----------------------------------------------------
        if (isset($data['checking'])) {
            foreach ($data['checking'] as $todolist_category_id) {
                if ($todolist_category_id) {
                    $project_check['project_id'] = $project->id;
                    $project_check['todolist_category_id'] = $todolist_category_id;
                    // $project_check['creator_id'] = $user_id;
                    // $project_check['updater_id'] = $user_id;
                    $checking = Checking::create($project_check);
                }

            }
        }
        // defect ------------------------------------------------------
        if (isset($data['defect'])) {
            foreach ($data['defect'] as $todolist_category_id) {
                if ($todolist_category_id) {
                    $project_defect['project_id'] = $project->id;
                    $project_defect['todolist_category_id'] = $todolist_category_id;
                    $defect = Defect::create($project_defect);
                }
            }
        }


        // execution defect ------------------------------------------------------
        if (isset($data['execution_defect'])) {
            $execution_defect_user_id=$data['execution_defect_user_id'];
            $execution_defect_date=$data['execution_defect_user_id'];
            foreach ($data['execution_defect'] as $todolist_category_id) {
                if ($todolist_category_id) {
                    $project_execution_defect['project_id'] = $project->id;
                    $project_execution_defect['todolist_category_id'] = $todolist_category_id;
                    $project_execution_defect['execution_defect_user_id'] =$execution_defect_user_id;
                    $project_execution_defect['execution_defect_date'] = $execution_defect_date;
                    $defect = ExecutionDefect::create($project_execution_defect);
                }
            }
        }

        $project->save();

        session()->flash('created_toast', 'پروژه با موفقیت ایجاد شد');
        if ($data['action_save'] == 'save_and_return') {
            return redirect()->route('projects.index');
        }
        return redirect()->route('projects.show', ['project' => $project->id]);

    }

    public function updateProject($project, $data, $user_id)
    {
        //on lack of permission
        if (!auth()->user()->can('update-projects')) return abort(403);

        $project->update($data);
        if (isset($data['medias'])) {
            $project->syncMedia(json_decode($data['medias'], true), 'send_list_comments');
        }
        if (isset($data['design_medias'])) {
            $project->syncMedia(json_decode($data['design_medias'], true), 'design_file_result');
        }
        if (isset($data['material_cost_medias'])) {
            $project->syncMedia(json_decode($data['material_cost_medias'], true), 'cost_of_materials');
        }

        if (isset($data['additional_cost_medias'])) {
            $project->syncMedia(json_decode($data['additional_cost_medias'], true), 'additional_costs_and_dorm_rent');
        }



        if (isset($data['checking_medias'])) {
            $project->syncMedia(json_decode($data['checking_medias'], true), 'checking_comment');
        }

        if (isset($data['defect_medias'])) {
            foreach (json_decode($data['defect_medias'], true) as $media_id) {
                $media = Media::find($media_id);
                $project->attachMedia($media, 'defect_comment');
            }
        }

        if (isset($data['execution_defect_medias'])) {
            foreach (json_decode($data['execution_defect_medias'], true) as $media_id) {
                $media = Media::find($media_id);
                $project->attachMedia($media, 'execution_defect_comment');
            }
        }

        if (isset($data['inverter_medias'])) {
            $project->syncMedia(json_decode($data['inverter_medias'], true), 'inverter_serial_number_image');
        }

        if (isset($data['monitoring_medias'])) {
            $project->syncMedia(json_decode($data['monitoring_medias'], true), 'monitoring_code');
        }

        if (isset($data['meter_medias'])) {
            $project->syncMedia(json_decode($data['meter_medias'], true), 'meter_serial_number_image');
        }

        if (isset($data['structure_medias'])) {
            $project->syncMedia(json_decode($data['structure_medias'], true), 'structure_type_image');
        }

         // sync defect -----------------------------------
         if (isset($data['defect']) && auth()->user()->can('update-projects-defect_comment')) {
            $defectData = [];
            foreach ($data['defect'] as $categoryId) {
                $defectData[$categoryId] = [
                    // 'creator_id' => $user_id,
                    // 'updater_id' => $user_id,
                ];
            }
            $existingDefects = $project->defect()->get();

            $existingIds = $existingDefects->pluck('todolist_category_id')->toArray();
            $newIds = array_keys($defectData);
            // Determine which IDs need to be soft-deleted
            $idsToDelete = array_diff($existingIds, $newIds);
            // Soft delete the records
            foreach ($idsToDelete as $id) {
                $project->defect()->where('todolist_category_id', $id)->delete();
            }

            // Add or update the new records
            foreach ($defectData as $id => $additional_data) {
                $project->defect()->updateOrCreate(['todolist_category_id' => $id], $additional_data);
            }
        } else {
            if(auth()->user()->can('update-projects-defect_comment')){
                foreach($project->defect()->get() as $value){
                    $value->delete();
                }
            }
        }

        // sync execution defect -----------------------------------
        if (isset($data['execution_defect']) && auth()->user()->can('update-projects-execution_defect_comment')) {
            // $executionDefectData = [];
            // foreach ($data['execution_defect'] as $categoryId) {
            //     $executionDefectData[$categoryId] = [
            //     ];
            // }

            // $existingExecutionDefects = $project->executionDefect()->get();

            // $existingIds = $existingExecutionDefects->pluck('todolist_category_id')->toArray();
            // $newIds = array_keys($executionDefectData);
            // // Determine which IDs need to be soft-deleted
            // $idsToDelete = array_diff($existingIds, $newIds);
            // // Soft delete the records
            // foreach ($idsToDelete as $id) {
            //     $project->executionDefect()->where('todolist_category_id', $id)->delete();
            // }

            // // Add or update the new records
            // foreach ($executionDefectData as $id => $additional_data) {
            //     $project->executionDefect()->updateOrCreate(['todolist_category_id' => $id], $additional_data);
            // }
            $execution_defect_user_id=$data['execution_defect_user_id'];
            $execution_defect_date=$data['execution_defect_date'] ?? now();
            foreach ($data['execution_defect'] as $todolist_category_id) {
                if ($todolist_category_id) {
                    $project_execution_defect['project_id'] = $project->id;
                    $project_execution_defect['todolist_category_id'] = $todolist_category_id;
                    $project_execution_defect['execution_defect_user_id'] =$execution_defect_user_id;
                    $project_execution_defect['execution_defect_date'] = $execution_defect_date;
                    $defect = ExecutionDefect::create($project_execution_defect);
                }
            }
        } else {
            // if(auth()->user()->can('update-projects-execution_defect_comment')){
            //     $project->executionDefect()->update(['deleted_at' => now()]);
            // }
        }

        if (isset($data['fix_execution_defect_id']) && auth()->user()->can('update-projects-execution_defect_comment')) {

            $fix_execution_defect_user_id=$data['fix_execution_defect_user_id'] ?? null;
            $fix_execution_defect_date=$data['fix_execution_defect_date'] ?? null;


            $defect = ExecutionDefect::find($data['fix_execution_defect_id']);
            $defect->fix_execution_defect_user_id=$fix_execution_defect_user_id;
            $defect->fix_execution_defect_date=$fix_execution_defect_date;
            $defect->save();
        }




        // sync checking -----------------------------------
        if (isset($data['checking'])  && auth()->user()->can('update-projects-checking_comment')) {
            $checkingData = [];
            foreach ($data['checking'] as $categoryId) {
                $checkingData[$categoryId] = [
                    // 'creator_id' => $user_id,
                    // 'updater_id' => $user_id,
                ];
            }

            $existingCheckings = $project->checking()->get();

            $existingIds = $existingCheckings->pluck('todolist_category_id')->toArray();
            $newIds = array_keys($checkingData);
            // Determine which IDs need to be soft-deleted
            $idsToDelete = array_diff($existingIds, $newIds);
            // Soft delete the records
            foreach ($idsToDelete as $id) {
                $project->checking()->where('todolist_category_id', $id)->delete();
            }
            // Add or update the new records
            foreach ($checkingData as $id => $additional_data) {
                $project->checking()->updateOrCreate(['todolist_category_id' => $id], $additional_data);
            }
        }
        else
        {
            if(auth()->user()->can('update-projects-checking_comment')){
                foreach($project->checking()->get() as $value){
                    $value->delete();
                }
            }
        }

        // sync project_visit ------------------------------------------------
        if (isset($data['project_visit_section']) && auth()->user()->can('update-visitors-visit_date')) {
            $existingVisitorsIds = $project->visitor()->pluck('id')->toArray();
            $newVisitorsIds = [];
            foreach ($data['project_visit_section'] as $visitor_details) {
                if ($visitor_details['user_id'])
                {
                    $medias = json_decode($visitor_details['medias'], true);
                    unset($visitor_details['medias']);

                    // $visitor_details['creator_id'] = $user_id;
                    // $visitor_details['updater_id'] = $user_id;

                    // $visitor = $project->visitor()->updateOrCreate($visitor_details);
                    if (isset($visitor_details['id']) and $visitor_details['id']) {
                        $visitor = $project->visitor()->updateOrCreate(['id' => $visitor_details['id']], $visitor_details);
                    } else {
                        $visitor = $project->visitor()->create($visitor_details);
                    }
                    $newVisitorsIds[] = $visitor->id;

                    $visitor->syncMedia($medias, 'comment');

                }
            }
            $visitorsToDelete = array_diff($existingVisitorsIds, $newVisitorsIds);
            if (!empty($visitorsToDelete) && auth()->user()->can('update-visitors-visit_date')) {
                Visitor::whereIn('id', $visitorsToDelete)->each(function ($visitor) {
                    $visitor->delete();
                });
            }
        }
        else
        {
            if(auth()->user()->can('update-visitors-visit_date')){
                $existingVisitorIds = $project->visitor->pluck('id')->toArray();
                // dd($existingVisitorIds);
                if (!empty($existingVisitorIds)) {
                    Visitor::whereIn('id', $existingVisitorIds)->each(function ($visitor) {
                        $visitor->delete();
                    });
                }
            }
        }



        // sync project_serial_panel ---------------------------------------
        if (!empty($data['project_serial_panel']) && auth()->user()->can('update-serial_panels-serial')) {
            $existingPanelIds = $project->serialPanels()->pluck('id')->toArray();
            $newPanelIds = [];
            foreach ($data['project_serial_panel'] as $panel_details) {
                if ($panel_details['serial'])
                {
                    $medias = json_decode($panel_details['medias'], true);
                    unset($panel_details['medias']);

                    // $panel_details['creator_id'] = $user_id;
                    // $panel_details['updater_id'] = $user_id;
                    // $panel = $project->serialPanels()->updateOrCreate($panel_details);
                    if (isset($panel_details['id']) and $panel_details['id']) {
                        $panel = $project->serialPanels()->updateOrCreate(['id' => $panel_details['id']], $panel_details);
                    } else {
                        $panel = $project->serialPanels()->create($panel_details);
                    }
                    $newPanelIds[] = $panel->id;

                    $panel->syncMedia($medias, 'description');
                }

            }
            // Delete existing records that are not present in the new data
            if(auth()->user()->can('update-serial_panels-serial')){
                $panelsToDelete = array_diff($existingPanelIds, $newPanelIds);
                if (!empty($panelsToDelete)) {
                    SerialPanel::destroy($panelsToDelete);
                }
            }
        }

        // project_support --------------------------------------------
        if (!empty($data['project_support_section']) && auth()->user()->can('update-project_supports-title') ) {
            $existingSupportIds = $project->support->pluck('id')->toArray();
            $newSupportIds = [];
            foreach ($data['project_support_section'] as $support) {
                if($support['status'])
                {
                    $creator_medias = '';
                    $supporter_medias = '';
                    Log::info(['support data : ', $support]);

                    // $support['creator_id'] = $user_id;
                    // $support['updater_id'] = $user_id;
                    $support['project_id'] = $project->id;

                    if (isset($support['creator_medias'])) {
                        $creator_medias = json_decode($support['creator_medias'], true);
                        unset($support['creator_medias']);
                    }

                    if (isset($support['supporter_medias'])) {
                        $supporter_medias = json_decode($support['supporter_medias'], true);

                    }
                    unset($support['supporter_medias']);

                    Log::info(['creator_medias data : ', $creator_medias]);
                    if (isset($support['id']) and $support['id']) {
                        $projectSupport = $project->support()->updateOrCreate(['id' => $support['id']], $support);
                    } else {
                        $projectSupport = $project->support()->create($support);
                    }
                    $newSupportIds[] = $projectSupport->id;

                    if ($creator_medias) {
                        Log::info(['creator_medias data 2 : ', $creator_medias]);
                        $projectSupport->syncMedia($creator_medias, 'creator_comment');
                    }

                    if ($supporter_medias) {
                        $projectSupport->syncMedia($supporter_medias, 'supporter_comment');
                    }

                }


            }
            // Delete existing records that are not present in the new data
            $supportsToDelete = array_diff($existingSupportIds, $newSupportIds);
            if (!empty($supportsToDelete) && auth()->user()->can('update-project_supports-title')) {
                ProjectSupport::whereIn('id', $supportsToDelete)->each(function ($support) {
                    $support->delete();
                });
            }
        }
        else
        {
            if(auth()->user()->can('update-project_supports-title')){
                $existingSupportIds = $project->support->pluck('id')->toArray();
                if (!empty($existingSupportIds)) {
                    ProjectSupport::whereIn('id', $existingSupportIds)->each(function ($support) {
                        $support->delete();
                    });
                }
            }
        }
        // sync project_deposit --------------------------------------------
        if (!empty($data['project_deposit'])  && auth()->user()->can('update-deposits-amount')) {
            $existingDepositIds = $project->deposit->pluck('id')->toArray();
            $newDepositIds = [];
            $paid_amount = 0;
            $payment_date = null;
            foreach ($data['project_deposit'] as $depositData) {
                if ($depositData['amount'])
                {
                    $medias = json_decode($depositData['medias'], true);
                    unset($depositData['medias']);

                    $cleanedString = preg_replace('/[^0-9,]/', '', $depositData['amount']);
                    $cleanedString = str_replace(',', '', $cleanedString);
                    $depositData['amount'] = (int)$cleanedString;

                    // Handle bank_id from Tagify
                    if (isset($depositData['bank_id']) && !empty($depositData['bank_id'])) {
                        try {
                            $banks = json_decode($depositData['bank_id'], true, 512, JSON_THROW_ON_ERROR);
                            if (is_array($banks) && count($banks) > 0) {
                                $bank = $banks[0];
                                if (isset($bank['value']) && intval($bank['value']) != 0) {
                                    $depositData['bank_id'] = $bank['value'];
                                } elseif (isset($bank['name']) && !empty($bank['name'])) {
                                    $newBank = Bank::create([
                                        'name' => $bank['name'],
                                    ]);
                                    $depositData['bank_id'] = $newBank->id;
                                }
                            }
                        } catch (\Exception $e) {
                            // If JSON decode fails, try to create a new bank with the string value
                            if (!empty($depositData['bank_id'])) {
                                $newBank = Bank::create([
                                    'name' => $depositData['bank_id'],
                                ]);
                                $depositData['bank_id'] = $newBank->id;
                            }
                        }
                    }

                    $depositData['project_id'] = $project->id;
                    $depositData['user_id'] = $data['user_id'];
                    // dd($depositData);
                    if (isset($depositData['id']) and $depositData['id']) {
                        $deposit = Deposit::updateOrCreate(['id' => $depositData['id']], $depositData);
                    } else {
                        $deposit = Deposit::create($depositData);
                    }
                    $newDepositIds[] = $deposit->id;

                    $deposit->syncMedia($medias, 'payment_date');
                }

            }
            // Delete existing records that are not present in the new data
            $depositsToDelete = array_diff($existingDepositIds, $newDepositIds);

            if (!empty($depositsToDelete) && auth()->user()->can('update-deposits-amount')) {
                Deposit::whereIn('id', $depositsToDelete)->each(function ($deposit) {
                    $deposit->delete();
                });
            }
        }
        else
        {
            if(auth()->user()->can('update-deposits-amount')){


                $existingDepositIds = $project->deposit->pluck('id')->toArray();
                if (!empty($existingDepositIds)) {
                    Deposit::whereIn('id', $existingDepositIds)->each(function ($deposit) {
                        $deposit->delete();
                    });
                }
            }
        }
        // ---------------------------------------------------------------
        // $project->save();
        // $project= Project::find($project->id);
        $project->save();

        session()->flash('created_toast', 'پروژه با موفقیت ویرایش شد');
         // Check if the action was "save_and_return"
        if ($data['action_save'] == 'save_and_return') {
            return redirect()->route('projects.index');
        }
        return redirect()->route('projects.show', ['project' => $project->id]);
        }

    /**
     * Display the specified resource.
     */
    public function show(Project $project)
    {
        ini_set('memory_limit', '-1');
        ini_set('max_execution_time', -1);
        //on lack of permission
        if (!auth()->user()->can('view-projects')) return abort(403);

        // TODO: change the relational function names in project model and project api
        $user = auth()->user();
        $roles = $user->roles->pluck('name')->toArray();
        if (in_array('admin' , $roles ))
        {
            $role = 'admin';
        }
        elseif(in_array('accounting' , $roles ))
        {
            $role = 'accounting';
        }
        elseif(in_array('visitor' , $roles ))
        {
            $role = 'visitor';
        }
        elseif(in_array('design' , $roles ))
        {
            $role = 'design';
        }
        elseif(in_array('warehouse' , $roles ))
        {
            $role = 'warehouse';
        }
        elseif(in_array('execution' , $roles ))
        {
            $role = 'execution';
        }
        else
            $role = $roles[0];

        $project_defects = $project->defect()->get();
        $project_checkings = $project->checking()->get();
        $project_execution_defects = $project->executionDefect()->get();
        $project_deposits = $project->deposit()->get();
        $project_supports = $project->support()->get();
        $project_serial_panels = $project->serialPanels()->get();
        $project_visitors = $project->visitor()->get();
        $visitors = User::withoutRole('customer')->get();
        $users = User::all();
        $payment_statuses = config('sun.params.paymentStatus');
        $mehrsan_statuses = config('sun.params.mehrsanStatus');
        $insurance_statuses = config('sun.params.insuranceStatus');
        $meter_assignment_statuses = config('sun.params.meterAssignmentStatus');
        $support_statuses = config('sun.params.supportStatus');
        $supports = User::withoutRole('customer')->get();
        $inspection_statuses = config('sun.params.inspectionStatus');
        $design_results = config('sun.params.designResults');
        $current_phases = config('sun.params.currentPhases');
        $inverter_models = config('sun.params.inverterModels');
        $panel_powers = config('sun.params.panelPowers');
        $panel_types = config('sun.params.paneltypes');
        // $support_organizations = config('sun.params.supportOrganization');
        $power_plant_capacities = config('sun.params.powerPlantCapacity');
        $customers = User::permission('access-customer-dashboard')->get();
        // $provinces = IranProvince::all();
        $counties = IranCounty::all();
        $cities = IranCity::all();
        $checkings = TodolistCategory::where('type', 'checking')->where('status', 'active')->get();
        $defects = TodolistCategory::where('type', 'defects')->where('status', 'active')->get();
        $execution_defects = TodolistCategory::where('type', 'execution-defects')->where('status', 'active')->get();
        $payment_until_today = $project->payment_date ? (new Carbon($project->payment_date))->diffInDays((Carbon::now()->format('Y-m-d'))): '';
        $installation_to_today_duration = $project->panel_installation_date ? (new Carbon($project->panel_installation_date))->diffInDays((Carbon::now()->format('Y-m-d'))): '';
        $project_media_items = $project->getMedia('media');
        $designers = User::withoutRole('customer')->get();
        $installers = User::withoutRole('customer')->get();

        $supportOrganizations = config('sun.params.supportOrganization');
        $accessedSupportOrganizationValues = auth()->user()->supportOrganizations()->pluck('support_organization_code')->toArray() ?? [];

        $support_organizations = array_filter($supportOrganizations, function ($organization) use ($accessedSupportOrganizationValues) {
            return in_array($organization['value'], $accessedSupportOrganizationValues);
        });

        $userAccessedProvinceIds = auth()->user()->accessedProvinces()->pluck('province_id')->toArray() ?? [];
        $provinces = IranProvince::whereIn('id', $userAccessedProvinceIds)->get();

        return view('projects.create',
            compact([
                'designers',
                'installers',
                'project',
                'project_defects',
                'project_checkings',
                'project_execution_defects',
                'project_deposits',
                'project_supports',
                'project_serial_panels',
                'installation_to_today_duration',
                'visitors',
                'project_visitors',
                'role',
                'users',
                'payment_statuses',
                'mehrsan_statuses',
                'insurance_statuses',
                'meter_assignment_statuses',
                'inspection_statuses',
                'design_results',
                'current_phases',
                'inverter_models',
                'panel_powers',
                'panel_types',
                'support_organizations',
                'project',
                'customers',
                'provinces',
                'counties',
                'cities',
                'power_plant_capacities',
                'support_statuses',
                'supports',
                'checkings',
                'defects',
                'execution_defects',
                'payment_until_today',
                'project_media_items',
            ]));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //on lack of permission
        if (!auth()->user()->can('delete-projects')) return abort(403);

        $project = Project::find($id);
        $project->delete();

        session()->flash('destroyed_toast', 'پروژه با موفقیت حذف شد');
        return redirect()->route('projects.index');
    }




    public function upload(Request $request)
    {
        $request->validate([
            'file' => 'required|file|mimes:jpg,jpeg,png,pdf,heic|max:5024'
        ]);

        // -------------------------------------------------------------------
        $file = $request->file('file');
        // Check if a file was uploaded
        if ($file) {
            // Upload the file using MediaUploader
            $media = MediaUploader::fromSource($file)
                ->toDirectory('panels/serial') // Specify the directory path
                ->upload();
            
            // Return the media ID
            return response()->json(['status' => 'success', 'media_id' => $media->id]);
        } else {
            // Handle the case where no file was uploaded
            return response()->json(['status' => 'error', 'message' => 'No file uploaded']);
        }


    }

    public function downloadMedia($id)
    {
        $media = Media::findOrFail($id);
        $filePath = storage_path('app/public/' . $media->directory . '/' . $media->filename . '.' . $media->extension);

        return response()->download($filePath, $media->filename . '.' . $media->extension);
    }

    public function destroyMedia($id)
    {
        $object=Mediable::where('media_id', $id)
        ->first();
        $object->delete();

        
        // $media = Media::findOrFail($id);
        // $filePath = 'panels/serial/' . $media->filename . '.' . $media->extension;

        // Delete the file from storage
        // Storage::disk('public')->delete($filePath);

        // Delete the media record from the database
        // $media->delete();

        return response()->json(['status' => 'success']);
    }

    public function getProjectByCustomer($id)
    {
        $project = Project::where('user_id', $id)->first();
        if ($project) {
            return response()->json([
                'status' => true,
                'message' => "پروژه‌ای مرتبط با این مشتری پیدا شد. آیا می‌خواهید ساخت پروژه جدید را ادامه دهید؟
                ",
                'project' => $project,
            ], 200);
        } else {
            return response()->json([
                'status' => false,
                'message' => 'Project by this customer not found',
            ], 200);
        }
    }

}
