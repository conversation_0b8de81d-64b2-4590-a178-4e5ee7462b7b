<?php

namespace App\Http\Controllers;

use App\Models\TodolistCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class TodolistCategoryController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //on lack of permission
        if (!auth()->user()->can('view-todolist_categories')) return abort(403);

        $categories = TodolistCategory::all();
        return view('todolist_categories.index', compact('categories'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //on lack of permission
        if (!auth()->user()->can('create-todolist_categories')) return abort(403);

        $types = config('sun.params.todolistCategoryTypes');
        $statuses = config('sun.params.statuses');
        $categories = TodolistCategory::all();
        return view('todolist_categories.create', compact('categories', 'types', 'statuses'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //on lack of permission
        if (!auth()->user()->can('create-todolist_categories')) return abort(403);

        $types = array_column(config('sun.params.todolistCategoryTypes'), 'value');

        $request->validate([
            'parent_id' => 'nullable|exists:todolist_categories,id',
            'title' => 'required|string|max:255',
            'type' => 'required|string|in:' . implode(',',  $types ),
            'status' => 'required|string',
            'creator_id' => 'nullable|exists:users,id',
            'updater_id' => 'nullable|exists:users,id',

        ]);

        $data = $request->all();
        $data['creator_id'] = Auth::id();
        $data['updater_id'] = Auth::id();
        TodolistCategory::create($data);

        return redirect()->route('todolist-categories.index')->with('success', 'Category created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(TodolistCategory $todolistCategory)
    {
        //on lack of permission
        if (!auth()->user()->can('view-todolist_categories')) return abort(403);

        return view('todolist_categories.show', compact('todolistCategory'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(TodolistCategory $todolistCategory)
    {
        
        //on lack of permission
        if (!auth()->user()->can('update-todolist_categories')) return abort(403);

        $categories = TodolistCategory::all();
        $types = config('sun.params.todolistCategoryTypes');
        $statuses = config('sun.params.statuses');  

        return view('todolist_categories.edit', compact('todolistCategory', 'categories', 'types', 'statuses'));
    }


    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, TodolistCategory $todolistCategory)
    {
        //on lack of permission
        if (!auth()->user()->can('update-todolist_categories')) return abort(403);

        $types = array_column(config('sun.params.todolistCategoryTypes'), 'value');

        $request->validate([
            'parent_id' => 'nullable|exists:todolist_categories,id',
            'title' => 'required|string|max:255',
            'type' => 'required|string|in:' . implode(',', $types),
            'status' => 'required|string',
            'updater_id' => 'nullable|exists:users,id',
        ]);
        $data = $request->all();
        $data['updater_id'] = Auth::id();
        $todolistCategory->update($data);

        return redirect()->route('todolist-categories.index')->with('success', 'Category updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(TodolistCategory $todolistCategory)
    {
        //on lack of permission
        if (!auth()->user()->can('delete-todolist_categories')) return abort(403);

        $todolistCategory->delete();

        return redirect()->route('todolist-categories.index')->with('success', 'Category deleted successfully.');
    }
}
