<?php

namespace App\Http\Controllers;

use App\Http\Requests\Web\ThirdPartySupport\ThirdPartySupportCreateRequest;
use App\Http\Requests\Web\ThirdPartySupport\ThirdPartySupportUpdateRequest;
use App\Models\Project;
use App\Models\ThirdPartySupport;
use App\Models\User;
use Illuminate\Http\Request;

class ThirdPartySupportsController extends Controller
{
    public function index(Project $project)
    {
        //on lack of permission
        if (!auth()->user()->can('view-thirdparty-support')) return abort(403);

        $supports = $project->thirdParty;


        return view('third-party-support.index', compact(['supports', 'project']));
    }

    public function create(Request $request, Project $project)
    {
        //on lack of permission
        if (!auth()->user()->can('create-thirdparty-support')) return abort(403);

        $users = User::withoutRole('customer')->get();

        return view('third-party-support.create', compact(['project', 'users']));
    }

    public function store(ThirdPartySupportCreateRequest $request, Project $project)
    {
        //on lack of permission
        if (!auth()->user()->can('create-thirdparty-support')) return abort(403);

        $thirdPartySupport = new ThirdPartySupport();

        $data = $request->all();
        $data['support_date'] = \Verta::parse($request->support_date)->toCarbon();
        $data['support_start_date'] = \Verta::parse($request->support_start_date)->toCarbon();
        $data['support_end_date'] = \Verta::parse($request->support_end_date)->toCarbon();
        $thirdPartySupport->fill($data);

        $thirdPartySupport->save();

        return redirect()->route('third-party-support.index', $project)->with(['success' => 'پشتیبانی با موفقیت ساخته شد']);
    }

    public function edit(Request $request, Project $project, ThirdPartySupport $support)
    {
        //on lack of permission
        if (!auth()->user()->can('update-thirdparty-support')) return abort(403);

        $users = User::withoutRole('customer')->get();

        return view('third-party-support.edit', compact(['project', 'users', 'support']));
    }

    public function update(ThirdPartySupportUpdateRequest $request, Project $project,ThirdPartySupport $support)
    {
        //on lack of permission
        if (!auth()->user()->can('update-thirdparty-support')) return abort(403);
        
        $data = $request->all();
        $data['support_date'] = \Verta::parse($request->support_date)->toCarbon();
        $data['support_start_date'] = \Verta::parse($request->support_start_date)->toCarbon();
        $data['support_end_date'] = \Verta::parse($request->support_end_date)->toCarbon();
        $support->fill($data);

        $support->save();

        return redirect()->route('third-party-support.index', $project)->with(['success' => 'پشتیبانی با موفقیت ساخته شد']);
    }

}
