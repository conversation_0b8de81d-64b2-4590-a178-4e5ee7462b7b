<?php

namespace App\Http\Controllers;

use App\Events\UserImportEvent;
use App\Http\Requests\User\ImportUserByFileRequest;
use App\Models\IranCity;
use App\Models\IranCounty;
use App\Models\IranProvince;
use App\Models\Project;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use App\Models\User;
use App\Traits\UsesXlsxToArray;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Spatie\Permission\Models\Role;
use Symfony\Component\HttpFoundation\Response as ResponseAlias;
use Illuminate\Support\Str;
use Exception;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\UsersExport;
use App\Imports\UsersImport;
use App\Models\Bank;
use App\Models\Village;

class UserController extends Controller
{
    use UsesXlsxToArray;

    /**
     * Display a listing of the resource.
     *
     */
    public function index($roles =  '')
    {
        //on lack of permission
        if (!auth()->user()->can('view-users-list')) return abort(403);

        $roles = Role::where('guard_name', 'web')->get()->pluck('name');
        $users = User::orderBy('created_at', 'desc')
        ->select('id','first_name','last_name','phone','father_name','national_code')
        ->get();
        return view('users.index', compact('users' , 'roles'));
    }

    public function getUsers(Request $request)
    {

        // Base query to fetch users with necessary relationships
        $users = User::select([
                'users.id',
                'first_name',
                'last_name',
                'phone as phone_number',
                'father_name',
                'national_code',
                'roles.name as role_name'
            ])
            ->join('model_has_roles', 'users.id', '=', 'model_has_roles.model_id')
            ->join('roles', 'model_has_roles.role_id', '=', 'roles.id');

        // Search filter
        if ($request->search['value'] != '') {
            $users->where(function($query) use ($request) {
                $query->where('first_name', 'like', '%' . $request->search['value'] . '%')
                      ->orWhere('last_name', 'like', '%' . $request->search['value'] . '%')
                      ->orWhere('roles.name', 'like', '%' . $request->search['value'] . '%')
                      ->orWhere('phone', 'like', '%' . $request->search['value'] . '%')
                      ->orWhere('national_code', 'like', '%' . $request->search['value'] . '%')
                      ->orWhere('father_name', 'like', '%' . $request->search['value'] . '%');
            });
        }
        if ($request->roles) {
            // dd($request->roles);
            $users->role($request->roles);
        }
        if (!auth()->user()->can('update-users-roles')){
            $users->role('customer');
        }
        // Apply ordering
        if (isset($request->order[0]['column'])) {
            $orderableColumns = [
                1 => 'name',
                2 => 'phone_number',
                3 => 'father_name',
                4 => 'national_code',
                5 => 'role_name',

            ];

            $orderBy = $orderableColumns[$request->order[0]['column']] ?? 'projects.updated_at';
            $direction = $request->order[0]['dir'] == 'asc' ? 'asc' : 'desc';

            $users->orderBy($orderBy, $direction);
        } else {
            $users->orderBy('users.updated_at', 'desc');
        }

        // Clone the query for counting records
        $userQuery = clone $users;
        $count = $userQuery->distinct('users.id')->count();

        // Paginate results
        $users = $users->groupBy('users.id')
            ->offset($request->start)
            ->limit($request->length)
            ->get();

        foreach($users as $user){
            // $users->role_name = $user->getPersianMehrsanAttribute();
            $user->name = ($user->first_name) ? $user->first_name . " " . $user->last_name : $user->last_name;
            $user->actions = "
            <div class='d-flex'>
              " . (auth()->user()->can('update-users') ? "
                <div class='ms-2'>
                    <a href='" .route('users.edit', $user->id) ."'
                        class='btn btn-sm btn-icon btn-light btn-active-light-primary'
                        data-kt-menu-trigger='click'
                        data-kt-menu-placement='bottom-end'>
                        <!--begin::Svg Icon | path: icons/duotune/coding/cod007.svg-->
                        <span class='svg-icon svg-icon-5 m-0'>
                            <i class='far fa-edit fs-4'></i>
                        </span>
                        <!--end::Svg Icon-->
                    </a>
                </div>
                 " : "") . "
                " . (auth()->user()->can('delete-users') ? "
                <div class='ms-2'>
                    <form class='delete-form'
                            action='". route('users.destroy', $user) ."'
                            method='post'
                            style='display: inline-block;'
                            id='" . $user->id ."'>
                            " . method_field('DELETE') . "
                            ". csrf_field() ."
                        <a id='delete'
                            class='btn btn-sm btn-icon btn-light btn-active-light-danger'>
                            <span class='svg-icon svg-icon-5 m-0'>
                                <i class='text-dark-50 fonticon-trash fs-2'></i>
                            </span>
                        </a>
                    </form>
                </div>
                " : "") . "
                 " . (auth()->user()->can('view-users') ? "
                <div class='ms-2'>
                    <a class='btn btn-sm btn-icon btn-light btn-active-light-primary'
                        title='مشاهده' href='" . route('users.show' , [$user->id]) . "'>
                        <i class='fa fa-cog fs-2' aria-hidden='true'></i>
                    </a>
                </div>
                " : "") . "
            </div>
            ";

        }


        // Return the data formatted for DataTables
        $toReturn = [
            'data' => $users,
            'recordsTotal' => $users->count(),
            'recordsFiltered' => $count,
            'draw' => (int)$request->draw
        ];

        return response()->json($toReturn);
    }

    public function filteredIndex(Request $request)
    {
        // dd($request->input('roles'));
        $roles = $request->input('roles');
        $search = $roles;
        if ($roles)
        {
            $users = User::role($roles)->orderBy('created_at', 'desc')->get(); // Get users with the roles
        }
        else
        {
            $users = User::orderBy('created_at', 'desc')->get();

        }

        $roles = Role::where('guard_name', 'web')->get()->pluck('name');
        return view('users.index', compact('users', 'roles' , 'search'));

    }

    /**
     * Show the form for creating a new resource.
     *
     */
    public function create()
    {
        //on lack of permission
        if (!auth()->user()->can('create-users')) return abort(403);

        $roles = Role::where('guard_name', 'web')->get()->pluck('name');

        $provinces = IranProvince::all();
        $counties = IranCounty::all();
        $cities = IranCity::all();
        $support_organizations = config('sun.params.supportOrganization');

        return view('users.create', compact(['roles', 'provinces', 'counties', 'cities','support_organizations']));
    }

    /**
     * Store a newly created resource in storage.
     *
     */
    public function store(Request $request)
    {
        //on lack of permission
        if (!auth()->user()->can('create-users')) return abort(403);

        $request=normalizeRequestCharacter($request);
        $bankAccountIban = $request->input('bank_account_iban');
        if ($bankAccountIban) {
            $request->merge(['bank_account_iban' => 'IR' . $bankAccountIban]);
        }
        $request->validate([
            'first_name' => 'nullable|string',
            'last_name' => 'nullable|string',
            'phone' => ['required', 'regex:/^09\d{9}$/',
            // Rule::unique('users', 'phone')->whereNull('deleted_at')

            // 'unique:\App\Models\User,phone,NULL,id,deleted_at,NULL'
            ],
            'phone2' => ['nullable', 'numeric'],
            'father_name' => 'nullable|string',
            'national_code' => [
                'nullable',
                'numeric',
                Rule::unique('users', 'national_code')->whereNull('deleted_at')

                // 'unique:\App\Models\User,national_code,NULL,id,deleted_at,NULL'
            ],
            'bank' => 'nullable|string',
            'bank_account_number' => 'nullable|string',
            'bank_account_iban' => ['nullable' , 'string' , 'regex:/^(?:IR)(?=.{24}$)[0-9]*$/' ,Rule::unique('users', 'bank_account_iban')->whereNull('deleted_at')],
            'email' => 'nullable|email|unique:users',
            'password' => 'nullable|string',
            'type' => 'nullable|string|in:personal,legal',
            'user_access_province_ids' => 'array',
            'user_access_support_organizations' => 'array',
        ]);

        if($request->input('type') == 'personal' ){
            if (! $request->input('national_code'))
            {
                throw ValidationException::withMessages(['national_code' => ' فیلد شماره ملی الزامی است']);
            }

            // if (! $this->checkMeliCode($request->input('national_code')))
            // {
            //     throw ValidationException::withMessages(['national_code' => 'مقدار فیلد شماره ملی معتبر نیست ']);
            // }
        }



        $user = new User();
        $user->first_name = $request->input('first_name');
        $user->last_name = $request->input('last_name');
        $user->phone = $request->input('phone');
        $user->phone2 = $request->input('phone2');
        $user->father_name = $request->input('father_name');
        $user->national_code = $request->input('national_code');
        $user->address = $request->input('address');
        $user->postal_code = $request->input('postal_code');
        $user->province_id = $request->input('province_id');
        $user->county_id = $request->input('county_id');
        $user->city_id = $request->input('city_id');
        $user->bank = $request->input('bank');
        $user->bank_account_number = $request->input('bank_account_number');
        $user->bank_account_iban = $request->input('bank_account_iban');
        $user->type = $request->input('type');
        $user->birth_date = $request->input('birth_date');
        $user->bank_branch = $request->input('bank_branch');
        $user->password = bcrypt($request->input('password'));
        if (isset($request->village_id)) {
            $villages = json_decode($request->village_id, false, 512, JSON_THROW_ON_ERROR);
            foreach ($villages as $village) {
                if(intval($village->value) != 0){
                    $user->village_id = $village->value;
                }
                else{
                    $newVillage=Village::create([
                        'name' => $village->value,
                    ]);
                    $user->village_id = $newVillage->id;

                }
            }
        }

        if (isset($request->bank_id)) {
            $banks = json_decode($request->bank_id, false, 512, JSON_THROW_ON_ERROR);
            foreach ($banks as $bank) {
                if(intval($bank->value) != 0){
                    $user->bank_id = $bank->value;
                }
                else{
                    $newBank=Bank::create([
                        'name' => $bank->value,
                    ]);
                    $user->bank_id = $newBank->id;
                }
            }
        }

        $user->save();
        if (auth()->user()->can('update-users-roles')){
            $roles = Role::whereIn('name', $request->roles ?? [])->get();
            $user->syncRoles($roles);
        }else{
            $roles = Role::whereIn('name', ['customer'])->get();
            $user->syncRoles($roles);
        }


        //adding role
        // foreach ($request->roles as $role) {
        //     //adding role if exists
        //     if ($role) {
        //         $user->assignRole($role);
        //     }
        // }

        // Save user access to support organizations
        if ($request->has('user_access_support_organizations')) {
            foreach ($request->input('user_access_support_organizations') as $organizationCode) {
                if($organizationCode != 'select_all_support_organizations'){
                    $user->supportOrganizations()->create([
                        'support_organization_code' => $organizationCode,
                    ]);
                }

            }
        }

        // Save user access to provinces
        if ($request->has('user_access_province_ids')) {
            foreach ($request->input('user_access_province_ids') as $provinceId) {
                if($provinceId != 'select_all_provinces'){

                    $user->accessedProvinces()->create([
                        'province_id' => $provinceId,
                    ]);
                }
            }
        }

        session()->flash('created_toast', 'کاربر با موفقیت اضافه شد.');
        return redirect()->route('users.index');
    }

    /**
     * Display the specified resource.
     *
     * @param int $id
     */
    public function show($id)
    {
        //on lack of permission
        if (!auth()->user()->can('view-users')) return abort(403);

        $user = User::findOrFail($id);
        $installers = Project::with('installer')->where('installer_id', $user->id)->get();

        $inspectors = Project::with('inspector')->where('inspector_id', $user->id)->get();

        $designers = Project::with('designer')->where('designer_id', $user->id)->get();


        $supporters = Project::join('project_supports as ps', 'ps.project_id', '=', 'projects.id')
            ->join('users as u', 'u.id', '=', 'ps.supporter_id')
            ->where('supporter_id', $user->id)->get();


        return view('users.show', compact(['user', 'installers', 'inspectors', 'designers', 'supporters']));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param int $id
     */
    public function edit($id)
    {
        //on lack of permission
        if (!auth()->user()->can('update-users')) return abort(403);

        $user = User::find($id);
        $roles = Role::where('guard_name', 'web')->get()->pluck('name');
        $provinces = IranProvince::all();
        $counties = IranCounty::all();
        $cities = IranCity::all();
        $support_organizations = config('sun.params.supportOrganization');

        $selectedAccessedSupprtOrganizations = $user->supportOrganizations?->pluck('support_organization_code')->toArray() ?? [];
        $selectedAccessedProvinces = $user->accessedProvinces?->pluck('province_id')->toArray() ?? [];




        return view('users.edit', compact('user', 'roles', 'provinces', 'counties', 'cities',
        'support_organizations','selectedAccessedSupprtOrganizations',
        'selectedAccessedProvinces'));
    }

    public function checkMeliCode($meli)
    {
        $cDigitLast = substr($meli, strlen($meli) - 1);
        $fMeli = strval(intval($meli));

        if ((str_split($fMeli))[0] == "0" && !(8 <= strlen($fMeli) && strlen($fMeli) < 10)) return false;

        $nineLeftDigits = substr($meli, 0, strlen($meli) - 1);

        $positionNumber = 10;
        $result = 0;

        foreach (str_split($nineLeftDigits) as $chr) {
            $digit = intval($chr);
            $result += $digit * $positionNumber;
            $positionNumber--;
        }

        $remain = $result % 11;

        $controllerNumber = $remain;

        if (2 <= $remain) {
            $controllerNumber = 11 - $remain;
        }

        return $cDigitLast == $controllerNumber;
    }

    /**
     * Update the specified resource in storage.
     *
     * @param int $id
     *
     */
    public function update(Request $request, User $user)
    {

        //on lack of permission
        if (!auth()->user()->can('update-users')) return abort(403);

        $request=normalizeRequestCharacter($request);
        $bankAccountIban = $request->input('bank_account_iban');
        if ($bankAccountIban) {
            $request->merge(['bank_account_iban' => 'IR' . $bankAccountIban]);
        }

        $request->validate([
            'first_name' => 'nullable|string',
            'last_name' => 'nullable|string',
            'phone' => ['required', 'regex:/^09\d{9}$/',
            // Rule::unique('users', 'phone')->ignore($user->id)->whereNull('deleted_at')
        ],
            'phone2' => ['nullable', 'numeric'],
            'father_name' => 'nullable|string',
            'national_code' => [
                'nullable',
                'numeric',
                Rule::unique('users', 'national_code')->ignore($user->id)->whereNull('deleted_at')
            ],
            'bank' => 'nullable|string',
            'bank_account_number' => 'nullable|string',
            'bank_account_iban' => ['nullable','string',Rule::unique('users', 'bank_account_iban')->ignore($user->id)->whereNull('deleted_at')],
            'email' => 'nullable|email|unique:users',
            'password' => 'nullable|string',
            'type' => 'nullable|string|in:personal,legal',
            'user_access_province_ids' => 'array',
            'user_access_support_organizations' => 'array',
        ]);

        // if($request->input('type') == 'personal' ){
        //     if (! $this->checkMeliCode($request->input('national_code')))
        //     {
        //         throw ValidationException::withMessages(['national_code' => 'مقدار فیلد کد ملی نامعتبر است']);
        //     }
        // }

        $bankAccountIban = 'IR' . $request->input('bank_account_iban');


        $user->first_name = $request->input('first_name');
        $user->last_name = $request->input('last_name');
        $user->phone = $request->input('phone');
        $user->phone2 = $request->input('phone2');
        $user->father_name = $request->input('father_name');
        $user->national_code = $request->input('national_code');
        $user->address = $request->input('address');
        $user->postal_code = $request->input('postal_code');
        $user->province_id = $request->input('province_id');
        $user->county_id = $request->input('county_id');
        $user->city_id = $request->input('city_id');
        $user->bank = $request->input('bank');
        $user->bank_account_number = $request->input('bank_account_number');
        $user->bank_account_iban = $request->input('bank_account_iban');
        $user->type = $request->input('type');
        $user->birth_date = $request->input('birth_date');
        $user->bank_branch = $request->input('bank_branch');
        if ($request->input('password')) {
            $user->password = bcrypt($request->input('password'));
        }
        if (isset($request->village_id)) {
            $villages = json_decode($request->village_id, false, 512, JSON_THROW_ON_ERROR);
            foreach ($villages as $village) {
                if(intval($village->value) != 0){
                    $user->village_id = $village->value;
                }
                else{
                    $newVillage=Village::create([
                        'name' => $village->value,
                    ]);
                    $user->village_id = $newVillage->id;

                }
            }
        }

        if (isset($request->bank_id)) {
            $banks = json_decode($request->bank_id, false, 512, JSON_THROW_ON_ERROR);
            foreach ($banks as $bank) {
                if(intval($bank->value) != 0){
                    $user->bank_id = $bank->value;
                }
                else{
                    $newBank=Bank::create([
                        'name' => $bank->value,
                    ]);
                    $user->bank_id = $newBank->id;
                }
            }
        }

        $user->save();

        //removing all user roles
        /* $userRoles = $user->getRoleNames();
         foreach ($userRoles as $userRole) {
             $user->removeRole($userRole);
         }*/
         if (auth()->user()->can('update-users-roles')){
            $roles = Role::whereIn('name', $request->roles ?? [])->get();
            $user->syncRoles($roles);
        }else{
            $roles = Role::whereIn('name', ['customer'])->get();
            $user->syncRoles($roles);
        }
        // $user->syncRoles($request->roles);
        //assign roles if the loged in user can create it
        /* foreach ($request->roles as $role) {
             $role = Role::where('name', $role)->first();
             //adding role if exists
             if ($role) {
                 $user->assignRole($role);
             }
         }*/

        // Handle support organizations
        if ($request->has('user_access_support_organizations')) {
            $user->supportOrganizations()->delete();

            if (!empty($request->input('user_access_support_organizations'))) {
                foreach ($request->input('user_access_support_organizations') as $organizationCode) {
                    if($organizationCode != 'select_all_support_organizations'){
                        $user->supportOrganizations()->create([
                            'support_organization_code' => $organizationCode,
                        ]);
                    }
                }
            }
        } else {
            $user->supportOrganizations()->delete();
        }

        // Handle accessed provinces
        if ($request->has('user_access_province_ids')) {
            $user->accessedProvinces()->delete();

            if (!empty($request->input('user_access_province_ids'))) {
                foreach ($request->input('user_access_province_ids') as $provinceId) {
                    if($provinceId != 'select_all_provinces'){

                        $user->accessedProvinces()->create([
                            'province_id' => $provinceId,
                        ]);
                    }
                }
            }
        } else {
            $user->accessedProvinces()->delete();
        }


        session()->flash('created_toast', 'کاربر با موفقیت ویرایش شد.');
        return redirect()->route('users.edit', $user->id);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     */
    public function destroy($id)
    {
        //on lack of permission
        if (!auth()->user()->can('delete-users')) return abort(403);

        $user = User::find($id);

        // Check if the user exists
        if (!$user) {
            session()->flash('destroyed_toast', 'کاربر یافت نشد');
            return redirect()->route('users.index');
        }

        // Check if the user has any associated projects
        if ($user->projects()->count() > 0) {
            session()->flash('destroyed_toast', 'امکان حذف این کاربر وجود ندارد');
            return redirect()->route('users.index');
        }

        // Delete associated support organizations and accessed provinces
        $user->supportOrganizations()->delete();
        $user->accessedProvinces()->delete();
        // If no associated projects, delete the user
        $user->delete();
        session()->flash('created_toast', ' کاربر با موفقیت حذف شد');
        return redirect()->route('users.index');
    }


    public function createUserWithImportFile(Request $request)
    {

        return view('users.import-file.create');
    }

    public function downloadSampleFile()
    {
        $filePath = 'public/user/sample-file.xlsx';

        if (Storage::exists($filePath)) {
            // Return the file as a response to trigger the download
            return Storage::download($filePath);
        } else {
            session()->flash('danger_toast', 'فایل پیدا نشد.');
            return view('users.import-file.create');
        }
    }

    function convertPersianDigitsToEnglish($input) {
        if (!$input)
            return null;
        $persianDigits = ['۰', '۱', '۲', '۳', '۴', '۵', '۶', '۷', '۸', '۹'];
        $englishDigits = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];

        return str_replace($persianDigits, $englishDigits, $input);
    }

    public function storeUserWithImportFile(ImportUserByFileRequest $request)
    {

        try {
            $filename = $this->uploadFile();

            $rows = $this->checkFileAndGetRows($filename);

            $processed = 0;
            $errors = 0;

            foreach ($rows as $row) {
                $collect = collect();
                try {
                    if ($row['تلفن_همراه_1']) {

                        $province_id = IranProvince::where('name', $row['استان'])->value('id');
                        $county_id = IranCounty::where('name', $row['شهرستان'])->value('id');
                        $city_id = IranCity::where('name', $row['شهر'])->value('id');
                        $phone = $this->convertPersianDigitsToEnglish($row['تلفن_همراه_1']);
                        $phone2 = $this->convertPersianDigitsToEnglish($row['تلفن_همراه_2']);
                        $national_code = $this->convertPersianDigitsToEnglish($row['شناسه/کد_ملی']);
                        $postal_code = $this->convertPersianDigitsToEnglish($row['کد_پستی']);
                        $bank_account_number = $this->convertPersianDigitsToEnglish($row['شماره_حساب']);
                        $bank_account_iban = $this->convertPersianDigitsToEnglish($row['شماره_شبا']);
                        $birth_date = $this->convertPersianDigitsToEnglish($row['تاریخ_تولد']);



                        $collect->first_name = empty($row['نام']) ? '' : $row['نام'];
                        $collect->last_name = empty($row['نام_خانوادگی']) ? '' : $row['نام_خانوادگی'];
                        $collect->phone = empty($phone) ? '' : (strpos($phone, '0') !== 0 ? '0' . $phone : $phone);
                        $collect->phone2 = empty($phone2) ? '' : (strpos($phone2, '0') !== 0 ? '0' . $phone2 : $phone2);
                        $collect->father_name = empty($row['نام_پدر']) ? '' : $row['نام_پدر'];
                        $collect->national_code = empty($national_code) ? '' : $national_code;
                        $collect->province_id = $province_id ?? null;
                        $collect->county_id = $county_id ?? null;
                        $collect->city_id = $city_id ?? null;
                        $collect->postal_code = empty($postal_code) ? '' : $postal_code;
                        $collect->address = empty($row['آدرس_نیروگاه']) ? '' : $row['آدرس_نیروگاه'];
                        $collect->bank = empty($row['نام_بانک']) ? '' : $row['نام_بانک'];
                        $collect->bank_account_number = empty($bank_account_number) ? '' : $bank_account_number;
                        $collect->bank_account_iban = empty($bank_account_iban) ? '' : $bank_account_iban;
                        $collect->birth_date = empty($birth_date) ? '' : $birth_date;


                        $this->processInvitation(
                            $collect
                        );
                        $processed++;
                    }
                } catch (Exception $exception) {
                    logger($exception->getMessage());
                    $errors++;
                    continue;
                }
            }
            session()->flash('created_toast', __('کاربران با موفقیت اضافه شدند.'));
            return redirect()->route('users.index');
        } catch (\Exception $e) {
            session()->flash('error_toast', __('مشکلی رخ داده است !'));
            return redirect()->route('users.index');
        }


    }

    private function uploadFile(): string
    {
        $uuid = now()->timestamp;

        $file = request()->file('file');

        $extension = $file->extension();
        $filename = "$uuid.$extension";

        $file->storeAs('', $filename, 'uploads');

        return $filename;
    }

    private function checkFileAndGetRows(string $filename): array
    {
        list($rows, $columns) = $this->xlsxToArray(storage_path('app/uploads/') . $filename);

        $this->checkColumns($columns);

        return $rows;
    }

    private function checkColumns($columns)
    {
        $requiredColumns = [
            'تلفن_همراه_1',
        ];

        foreach ($requiredColumns as $column) {
            $actualColumn = Str::title(str_replace('_', ' ', $column));
            abort_if(!in_array($column, $requiredColumns), ResponseAlias::HTTP_FORBIDDEN, str_replace(':column', $actualColumn, __(":column is required")));
        }
    }

    private function processInvitation($request): void
    {
        $request=normalizeRequestCharacter($request);
        $invitation = User::where('national_code', $request->national_code)->first();

        if ($invitation) {
            foreach (['first_name', 'last_name', 'phone', 'phone2', 'father_name',
                         'national_code', 'province_id', 'county_id', 'city_id', 'postal_code',
                         'address', 'bank', 'bank_account_number', 'bank_account_iban'] as $attribute) {
                if ($request->{$attribute}) {
                    $invitation->{$attribute} = $request->{$attribute};
                }
                $invitation->type = 'personal';
                $invitation->save();
            }
        } else {
            $invitation = User::create([
                'first_name' => $request->first_name =='' ? null :$request->first_name,
                'last_name' => $request->last_name =='' ? null :$request->last_name,
                'phone' => $request->phone =='' ? null :$request->phone,
                'phone2' => $request->phone2 =='' ? null :$request->phone2,
                'father_name' => $request->father_name =='' ? null :$request->father_name,
                'national_code' => $request->national_code =='' ? null :$request->national_code,
                'province_id' => $request->province_id =='' ? null :$request->province_id,
                'county_id' => $request->county_id =='' ? null :$request->county_id,
                'city_id' => $request->city_id =='' ? null :$request->city_id,
                'postal_code' => $request->postal_code =='' ? null :$request->postal_code,
                'address' => $request->address =='' ? null :$request->address,
                'bank' => $request->bank =='' ? null :$request->bank,
                'bank_account_number' => $request->bank_account_number =='' ? null :$request->bank_account_number,
                'bank_account_iban' => $request->bank_account_iban =='' ? null :$request->bank_account_iban,
                'birth_date' => $request->birth_date =='' ? null :$request->birth_date,
                'type' => 'personal',


            ]);
        }
        $customer_role = Role::findByName('customer');
        $invitation->syncRoles([$customer_role]);
        event(new UserImportEvent($invitation));
    }

    public function export()
    {
        return Excel::download(new UsersExport, 'users.xlsx');
    }

    /**
    * @return \Illuminate\Support\Collection
    */
    public function import(Request $request)
    {
        // Validate incoming request data
        $request->validate([
            'file' => 'required|max:2048',
        ]);

        Excel::import(new UsersImport, $request->file('file'));

        return back()->with('success', 'Users imported successfully.');
    }
}