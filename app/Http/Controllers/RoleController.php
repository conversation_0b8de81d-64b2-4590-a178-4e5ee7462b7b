<?php

namespace App\Http\Controllers;

use App\Http\Requests\Web\Role\StoreRoleRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class RoleController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //on lack of permission
        if (!auth()->user()->can('view-roles')) return abort(403);

        $roles = Role::orderByDesc('id')->where('guard_name','web')->get();

        return view('role.index', compact(['roles']));
    }

    /**
    * Show the form for creating a new resource.
    */
    public function create()
    {
        //on lack of permission
        if (!auth()->user()->can('create-roles')) return abort(403);

        //project section
        $projectSections = $this->getProjectSections();

        $permissionLabels = $this->getPermissionLabels();
        

        //project permissions
        $projectPermissions = $this->getProjectPermissions();

        //general permissions
        $generalPermissions = $this->getGeneralPermissions();
        
        //user permissions
        $userPermissions = $this->getUserPermissions();

        //third party permissions
        $thirdPartyPermissions = $this->getThirdPartyPermissions();

        //tab and section project permissions
        $tabAndSectionProjectFieldPermissions = $this->getTabAndSectionProjectFieldPermissions();

        //mobile permissions
        $mobilePermissions = $this->getMobilePermissions();

        // Fetch permissions from the database
        $permissions = Permission::all();

        // projects permissions
        $projectFieldPermissions = [];
        foreach ($projectPermissions as $section => $fieldPermissionNames) {
            
            $fieldPermissions = Permission::whereIn('name', $fieldPermissionNames)->where('guard_name','web')->get();
            $projectFieldPermissions[$section] = $fieldPermissions;
        }

        //user permissions 
        $userFieldPermissions = Permission::whereIn('name', $userPermissions)
        ->where('guard_name', 'web')
        ->get();

        //third party permissions
        $thirdPartyFieldPermissions = Permission::whereIn('name', $thirdPartyPermissions)
        ->where('guard_name', 'web')
        ->get();

        //tab and sections project permissions
        $tabAndSectionProjectPermissions = [];
        foreach ($tabAndSectionProjectFieldPermissions as $section => $fieldPermissionNames) {
            
            $fieldPermissions = Permission::whereIn('name', $fieldPermissionNames)->where('guard_name','web')->get();
            $tabAndSectionProjectPermissions[$section] = $fieldPermissions;
        }

        //mobile permissions
        $mobilePermissions = Permission::whereIn('name', $mobilePermissions)
        ->where('guard_name', 'web')
        ->get();
        
        // Passing grouped permissions to the view
        return view('role.create', compact('projectFieldPermissions',
        'generalPermissions','userFieldPermissions','mobilePermissions',
        'thirdPartyFieldPermissions','tabAndSectionProjectPermissions','permissionLabels'));
    }

    public function getProjectSections(){
        $projectSections = [
            'اطلاعات تکمیلی',
            'اطلاعات مالی',
            'اطلاعات آمار زمانی پروژه',
            'وضعیت عملیات اجرایی',
            'اطلاعات بازدید',
            'اطلاعات طراحی',
            'اطلاعات ارسال جنس',
            'اطلاعات اجرا',
            'اطلاعات اتصال به شبکه',
            'نواقص ارسالی',
            'نواقص اجرایی',
            'ارزیابی',
        ];

        return $projectSections;

    }
    public function getPermissionLabels(){
        $permissionLabels = [];

        $tableName = 'projects';


        // Fetch all columns with their comments
        $columns = DB::select("SHOW FULL COLUMNS FROM $tableName");

        // Loop through columns to generate permissions
        foreach ($columns as $column) {
            $comment = $column->Comment;
            $columnName = $column->Field;

            if (!empty($comment)) {
                // Generate permissions for each action (update, view, delete)
                $permissionLabels["update-$tableName-$columnName"] = $comment;
                $permissionLabels["view-$tableName-$columnName"] = $comment;
            }
        }

        return $permissionLabels;

    }
    public function getProjectPermissions(){

        $projectColumns = [
            // 1. اطلاعات عمومی مشتری
            'اطلاعات عمومی مشتری' => [
                //update
                'update-projects-project_code',
                'update-projects-support_organization',
                'update-projects-user_id',
                'update-projects-province_id',
                'update-projects-county_id',
                'update-projects-city_id',
                'update-projects-address',
                'update-projects-power_plant_capacity',
                'update-projects-accounting_status',
                'update-projects-village_id',

                //view
                'view-projects-project_code',
                'view-projects-support_organization',
                'view-projects-user_id',
                'view-projects-phone',
                'view-projects-phone2',
                'view-projects-father_name',
                'view-projects-national_code',
                'view-projects-province_id',
                'view-projects-county_id',
                'view-projects-city_id',
                'view-projects-address',
                'view-projects-power_plant_capacity',
                'view-projects-accounting_status',
                'view-projects-village_id',

            ],

            // 2. اطلاعات تکمیلی
            'اطلاعات تکمیلی' => [
                //update
                'update-projects-title',
                'update-projects-postal_code',
                'update-projects-electricity_county_id',
                'update-projects-electricity_affairs_code',
                'update-projects-electricity_bill_id',
                'update-projects-mehrsan_file_number',
                'update-projects-mehrsan_status',
                'update-projects-mehrsan_date',
                'update-projects-announcement_sanam',
                'update-projects-insurance',
                'update-projects-support_needed',
                'update-projects-loan_received_date',
                'update-projects-project_support_comment',

                //view
                'view-projects-title',
                'view-projects-postal_code',
                'view-projects-electricity_county_id',
                'view-projects-electricity_affairs_code',
                'view-projects-electricity_bill_id',
                'view-projects-mehrsan_file_number',
                'view-projects-mehrsan_status',
                'view-projects-mehrsan_date',
                'view-projects-announcement_sanam',
                'view-projects-insurance',
                'view-projects-support_needed',
                'view-projects-loan_received_date',
                'view-projects-bank',
                'view-projects-bank_branch',
                'view-projects-bank_account_number',
                'view-projects-bank_account_iban', 
                'view-projects-project_support_comment', 
            ],

            // 3. اطلاعات مالی
            'اطلاعات مالی' => [
                //update
                'update-projects-balance',
                'update-projects-payment_bank',
                'update-projects-payment_status',
                'update-projects-remaining_deposit',
                'update-projects-paid_amount',
                'update-projects-payment_date',
                'update-projects-discount',
                'update-projects-additional_costs_and_dorm_rent',
                'update-projects-additional_costs_and_dorm_rent_payment_date',
                'update-projects-cost_of_materials',
                'update-projects-material_cost_payment_date',

                'update-deposits-project_id',
                'update-deposits-amount',
                'update-deposits-payment_date',
                'update-deposits-user_id',
                'update-deposits-creator_id',
                'update-deposits-updater_id',
                'update-deposits-created_at',
                'update-deposits-bank',
                'update-deposits-type',
                'update-deposits-info_deposit',

                //view
                'view-projects-balance',
                'view-projects-payment_bank',
                'view-projects-payment_status',
                'view-projects-remaining_deposit',
                'view-projects-paid_amount',
                'view-projects-payment_date',
                'view-projects-discount',
                'view-projects-additional_costs_and_dorm_rent',
                'view-projects-additional_costs_and_dorm_rent_payment_date',
                'view-projects-cost_of_materials',
                'view-projects-material_cost_payment_date',

                'view-deposits-project_id',
                'view-deposits-amount',
                'view-deposits-payment_date',
                'view-deposits-user_id',
                'view-deposits-creator_id',
                'view-deposits-updater_id',
                'view-deposits-created_at',
                'view-deposits-bank',
                'view-deposits-type',
                'view-deposits-info_deposit',

            ],

            // 4. اطلاعات آمار زمانی پروژه
            'اطلاعات آمار زمانی پروژه' => [
                //update
                'update-projects-payment_to_execution_duration',
                'update-projects-payment_to_network_connection_duration',
                'update-projects-installation_to_today_duration',
                'update-projects-payment_until_today',

                //view
                'view-projects-payment_to_execution_duration',
                'view-projects-payment_to_network_connection_duration',
                'view-projects-installation_to_today_duration',
                'view-projects-payment_until_today',
            ],

            // 5. وضعیت عملیات اجرایی
            'وضعیت عملیات اجرایی' => [
                //update
                'update-projects-current_phase',
                //view
                'view-projects-current_phase',

            ],
            
            // 6. اطلاعات بازدید
            'اطلاعات بازدید' => [
                //update
                'update-projects-inspection_status',
                'update-projects-inspection_date',
                'update-projects-inspector_id',
                'update-projects-inspector_comment',
                'update-projects-inverter_to_electrical_base_distance',
                'update-projects-utm_y',
                'update-projects-utm_x',
                'update-projects-longitude',
                'update-projects-latitude',
                'update-projects-google_maps_link',
                'update-projects-inspector',

                'update-visitors-user_id',
                'update-visitors-project_id',
                'update-visitors-status',
                'update-visitors-visit_date',
                'update-visitors-comment',
                'update-visitors-creator_id',
                'update-visitors-updater_id',
                'update-visitors-created_at',
                'update-visitors-updated_at',

                //view
                'view-projects-inspection_status',
                'view-projects-inspection_date',
                'view-projects-inspector_id',
                'view-projects-inspector_comment',
                'view-projects-inverter_to_electrical_base_distance',
                'view-projects-utm_y',
                'view-projects-utm_x',
                'view-projects-longitude',
                'view-projects-latitude',
                'view-projects-google_maps_link',

                'view-visitors-user_id',
                'view-visitors-project_id',
                'view-visitors-status',
                'view-visitors-visit_date',
                'view-visitors-comment',
                'view-visitors-creator_id',
                'view-visitors-updater_id',
                'view-visitors-created_at',
                'view-visitors-updated_at',
            ],

            // 7. اطلاعات طراحی
            'اطلاعات طراحی' => [
                //update
                'update-projects-designer_id',
                'update-projects-design_file_result',

                //view
                'view-projects-designer_id',
                'view-projects-design_file_result',
            ],

            // 8. اطلاعات ارسال جنس
            'اطلاعات ارسال جنس' => [
                //update
                'update-projects-ware_postage_date',
                'update-projects-ware_sender_id',
                'update-projects-send_list_comments',

                //view
                'view-projects-ware_postage_date',
                'view-projects-ware_sender_id',
                'view-projects-send_list_comments',
            ],

            // 9. اطلاعات اجرا
            'اطلاعات اجرا' => [
                //update
                'update-projects-panel_installation_date',
                'update-projects-installer_id',
                'update-projects-structure_type_image',
                'update-projects-panel_type',
                'update-projects-panel_power',
                'update-projects-panel_quantity',
                'update-projects-inverter_model',
                'update-projects-inverter_serial_number_image',
                'update-projects-inverter_to_the_power_base_cable_used',
                'update-projects-has_monitoring',
                'update-projects-monitoring_code',
                'update-projects-monitoring_installation_date',
                'update-projects-monitoring_installer_id',
                'update-projects-meter_assignment',
                'update-serial_panels-project_id',
                'update-serial_panels-serial',
                'update-serial_panels-creator_id',
                'update-serial_panels-updater_id',
                'update-serial_panels-created_at',
                'update-serial_panels-updated_at',
                'update-serial_panels-description',

                'update-projects-earth_resistance',
                'update-projects-execution_type',
                'update-projects-measurement_date',

                //view
                'view-projects-panel_installation_date',
                'view-projects-installer_id',
                'view-projects-structure_type_image',
                'view-projects-panel_type',
                'view-projects-panel_power',
                'view-projects-panel_quantity',
                'view-projects-inverter_model',
                'view-projects-inverter_serial_number_image',
                'view-projects-inverter_to_the_power_base_cable_used',
                'view-projects-has_monitoring',
                'view-projects-monitoring_code',
                'view-projects-monitoring_installation_date',
                'view-projects-monitoring_installer_id',
                'view-projects-meter_assignment',
                'view-serial_panels-project_id',
                'view-serial_panels-serial',
                'view-serial_panels-creator_id',
                'view-serial_panels-updater_id',
                'view-serial_panels-created_at',
                'view-serial_panels-updated_at',
                'view-serial_panels-description',


                'view-projects-earth_resistance',
                'view-projects-execution_type',
                'view-projects-measurement_date',
                
            ],

            // 10. اطلاعات اتصال به شبکه
            'اطلاعات اتصال به شبکه' => [
                //update
                'update-projects-simban_id',
                'update-projects-meter_installation_date',
                'update-projects-meter_serial_number_image',
                'update-projects-network_connection_comment',
                'update-projects-sim_card_number',
                'update-projects-gis_code',

                //view
                'view-projects-simban_id',
                'view-projects-meter_installation_date',
                'view-projects-meter_serial_number_image',
                'view-projects-network_connection_comment',
                'view-projects-sim_card_number',
                'view-projects-gis_code',
            ],

            //11. پشتیبانی و تعمیرات
            'پشتیبانی و تعمیرات' => [
                //update
                'update-project_supports-project_id',
                'update-project_supports-status',
                'update-project_supports-title',
                'update-project_supports-start_date',
                'update-project_supports-end_date',
                'update-project_supports-supporter_comment',
                'update-project_supports-creator_id',
                'update-project_supports-creator_comment',
                'update-project_supports-updater_id',
                'update-project_supports-supporter_id',
                'update-project_supports-support_implementation_duration',

                //view
                'view-project_supports-project_id',
                'view-project_supports-status',
                'view-project_supports-title',
                'view-project_supports-start_date',
                'view-project_supports-end_date',
                'view-project_supports-supporter_comment',
                'view-project_supports-creator_id',
                'view-project_supports-creator_comment',
                'view-project_supports-updater_id',
                'view-project_supports-supporter_id',
                'view-project_supports-support_implementation_duration',
            ],


            // 12. ارزیابی
            'ارزیابی' => [
                //update
                'update-projects-checking_comment',
                'update-checkings-project_id',
                'update-checkings-user_id',
                'update-checkings-creator_id',
                'update-checkings-updater_id',
                'update-checkings-created_at',
                // 'update-checkings-todolist_category_id',


                //view
                'view-projects-checking_comment',
                'view-checkings-project_id',
                'view-checkings-user_id',
                'view-checkings-creator_id',
                'view-checkings-updater_id',
                'view-checkings-created_at',
                // 'view-checkings-todolist_category_id',
            ],


            // 13. نواقص ارسالی
            'نواقص ارسالی' => [
                //update
                'update-projects-defect_comment',
                // 'defect', //Todo:

                //view
                'view-projects-defect_comment',

            ],

            // 14. نواقص اجرایی
            'نواقص اجرایی' => [
                //update
                // 'execution_defect', //Todo:
                'update-projects-execution_defect_comment',

                //view
                'view-projects-execution_defect_comment',

            ],

            //other
            '' => [
            //update
            
           
            // 'update-projects-type',
            // 'update-projects-status',
            'update-projects-creator_id',
            'update-projects-updater_id',
            // 'update-projects-send_execution_defect',
            // 'update-projects-panel_serial_numbers_image',
            

            //view

            // 'view-projects-type',
            // 'view-projects-status',
            // 'view-projects-creator_id',
            // 'view-projects-updater_id',
            'view-projects-send_execution_defect',
            'view-projects-panel_serial_numbers_image',
            ]

        ];
        return $projectColumns;
    }

    public function getGeneralPermissions(){

        $generalPermissions = [
            'مدیریت کاربران' => [
                Permission::whereIn('name', [
                    'view-users', 'create-users', 'update-users', 'delete-users','view-users-list' , 'access-customer-dashboard'
                ])->where('guard_name','web')->get(),
            ],
            'مدیریت پروژه‌ها' => [
                Permission::whereIn('name', [
                    'view-projects', 'create-projects', 'update-projects', 'delete-projects','view-projects-list','create-change-status-mehrsan-with-file'
                ])->where('guard_name','web')->get(),
            ],
            // Project Support Management Permissions
            'مدیریت پشتیبانی پروژه' => [
                Permission::whereIn('name', [
                'view-project_supports', 'create-project_supports', 'update-project_supports', 'delete-project_supports'
            ])->where('guard_name','web')->get()
            ],

            // Logs and Reports
            'دسترسی به داشبورد و لاگ‌ها' => [
            Permission::whereIn('name', [
                'view-logs', 'view-dashboard',
            ])->where('guard_name','web')->get()
            ],

            // Reports Section
            'دسترسی به گزارش‌ها' => [
                Permission::whereIn('name', [
                    'view-reports-financials', 
                    'view-reports-projects', 
                    'view-reports-users',
                    'view-reports'
                    
                ])->where('guard_name','web')->get(),  
            ],

            // Third-party Support Management Section
            'مدیریت پشتیبانی ثالث' => [
                Permission::whereIn('name', [
                    'view-thirdparty-support', 
                    'create-thirdparty-support', 
                    'update-thirdparty-support', 
                    'delete-thirdparty-support'
                ])->where('guard_name','web')->get(),
            ],
            // Database Management
            'مدیریت پایگاه داده' => [
            Permission::whereIn('name', [
                'view-database', 'create-database', 'update-database', 'delete-database'
            ])->where('guard_name','web')->get()
            ],

            // To do List Category Management
            'مدیریت دسته‌بندی لیست وظایف' => [
            Permission::whereIn('name', [
                'view-todolist_categories', 'create-todolist_categories', 
                'update-todolist_categories', 'delete-todolist_categories'
            ])->where('guard_name','web')->get()
            ],

            // Role Management

            'مدیریت نقش ها' => [
                Permission::whereIn('name', [
                    'view-roles','create-roles',
                    'update-roles','delete-roles',
                ])->where('guard_name','web')->get()
            ]
        ];

        return $generalPermissions;
    }

    public function getUserPermissions(){
        $userPermissions = [
            //update
            'update-users-first_name',
            'update-users-birth_date',
            'update-users-last_name',
            'update-users-phone',
            'update-users-phone2',
            'update-users-father_name',
            'update-users-national_code',
            'update-users-city_id',
            'update-users-county_id',
            'update-users-province_id',
            'update-users-postal_code',
            'update-users-address',
            'update-users-bank',
            'update-users-bank_account_number',
            'update-users-bank_account_iban',
            'update-users-type',
            'update-users-otp_code',
            'update-users-otp_code_sent',
            'update-users-password',
            'update-users-creator_id',
            'update-users-updater_id',
            'update-users-remember_token',
            'update-users-bank_branch',
            'update-users-roles',
            'update-users-access_province_ids',
            'update-users-access_support_organizations',
            'update-users-village_id',

            //view
            'view-users-first_name',
            'view-users-birth_date',
            'view-users-last_name',
            'view-users-phone',
            'view-users-phone2',
            'view-users-father_name',
            'view-users-national_code',
            'view-users-city_id',
            'view-users-county_id',
            'view-users-province_id',
            'view-users-postal_code',
            'view-users-address',
            'view-users-bank',
            'view-users-bank_account_number',
            'view-users-bank_account_iban',
            'view-users-type',
            'view-users-otp_code',
            'view-users-otp_code_sent',
            'view-users-password',
            'view-users-creator_id',
            'view-users-updater_id',
            'view-users-remember_token',
            'view-users-bank_branch',
            'view-users-roles',
            'view-users-access_province_ids',
            'view-users-access_support_organizations',
            'view-users-village_id',

        ];

        return $userPermissions;
    }

    public function getThirdPartyPermissions(){
        $thirdPartyPermissions = [

            //update
            'update-third_party_supports-project_id',
            'update-third_party_supports-title',
            'update-third_party_supports-support_type',
            'update-third_party_supports-price',
            'update-third_party_supports-support_date',
            'update-third_party_supports-support_start_date',
            'update-third_party_supports-support_end_date',
            'update-third_party_supports-support_comments',
            'update-third_party_supports-support_completed',
            'update-third_party_supports-support_performer',
            'update-third_party_supports-creator_id',
            'update-third_party_supports-updater_id',
            'update-third_party_supports-created_at',
            'update-third_party_supports-updated_at',

            //view
            'view-third_party_supports-project_id',
            'view-third_party_supports-title',
            'view-third_party_supports-support_type',
            'view-third_party_supports-price',
            'view-third_party_supports-support_date',
            'view-third_party_supports-support_start_date',
            'view-third_party_supports-support_end_date',
            'view-third_party_supports-support_comments',
            'view-third_party_supports-support_completed',
            'view-third_party_supports-support_performer',
            'view-third_party_supports-creator_id',
            'view-third_party_supports-updater_id',
            'view-third_party_supports-created_at',
            'view-third_party_supports-updated_at',

        ];

        return $thirdPartyPermissions;
    }

    public function getTabAndSectionProjectFieldPermissions(){
        $tabAndSectionProjectFieldPermissions = [

            "تب های پروژه" => [

            'view-project-admin_tab',
            'view-project-accounting_tab',
            'view-project-visitor_tab',
            'view-project-design_tab',
            'view-project-warehouse_tab',
            'view-project-execution_tab',
            'view-project-supporting_tab',
            'view-project-checking_tab',
            'view-project-defect_tab',
            'view-project-execution_defect_tab',

            ],

            'بخش های پروژه' => [

                'view-project-general_section',
                'view-project-more_information_section',           
                'view-project-deposit_section',            
                'view-project-time_statistics_information_section',            
                'view-project-executive_operation_status_section',           
                'view-project-visit_section',           
                'view-project-design_information_section',            
                'view-project-shipping_information_section',
                'view-project-executive_information_section',            
                'view-project-network_connection_information_section',
            ]

        ];

        return $tabAndSectionProjectFieldPermissions;
    }

    public function getMobilePermissions(){
        $mobilePermissions = [
            'mobile-access-admin',
            'mobile-access-accounting',
            'mobile-access-visitor',
            'mobile-access-design',
            'mobile-access-warehouse',
            'mobile-access-execution',
            'mobile-access-supporting',
            'mobile-access-checking',
            'mobile-access-defect',
            'mobile-access-execution_defect',
        ];

        return $mobilePermissions;
    }



    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreRoleRequest $request)
    {
        //on lack of permission
        if (!auth()->user()->can('create-roles')) return abort(403);

        // Create the role for the web guard
        $webRole = Role::create([
            'name' => $request->name,
            'guard_name' => 'web', // Role for web guard
        ]);

        // Create the role for the api guard
        $apiRole = Role::create([
            'name' => $request->name,
            'guard_name' => 'api', // Role for api guard
        ]);

        // Retrieve `web` guard permissions based on the provided IDs
        $webPermissions = Permission::whereIn('id', $request->permissions)
        ->where('guard_name', 'web')
        ->get();

        // Retrieve `api` guard permissions based on the same permission names
        $apiPermissions = Permission::whereIn('name', $webPermissions->pluck('name'))
            ->where('guard_name', 'api')
            ->get();

        // Assign web permissions to the web role
        $webRole->givePermissionTo($webPermissions);

        // Assign api permissions to the api role
        $apiRole->givePermissionTo($apiPermissions);

        //to show message in index toast
        session()->flash('created_toast', 'نقش با موفقیت اضافه شد.');
        return redirect()->route('role.index');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    public function edit($id)
    {
        //on lack of permission
        if (!auth()->user()->can('update-roles')) return abort(403);

        $role = Role::find($id);
        if (!$role) return abort(404); // If role wasn't found

        //project section
        $projectSections = $this->getProjectSections();
        
        $permissionLabels = $this->getPermissionLabels();

        //project permissions
        $projectPermissions = $this->getProjectPermissions();

        //general permissions
        $generalPermissions = $this->getGeneralPermissions();
        
        //user permissions
        $userPermissions = $this->getUserPermissions();

        //third party permissions
        $thirdPartyPermissions = $this->getThirdPartyPermissions();

        //tab and section project permissions
        $tabAndSectionProjectFieldPermissions = $this->getTabAndSectionProjectFieldPermissions();

        //mobile permissions
        $mobilePermissions = $this->getMobilePermissions();

        // Fetch permissions from the database
        $permissions = Permission::all();

        // projects permissions
        $projectFieldPermissions = [];
        foreach ($projectPermissions as $section => $fieldPermissionNames) {
            
            $fieldPermissions = Permission::whereIn('name', $fieldPermissionNames)->where('guard_name','web')->get();
            $projectFieldPermissions[$section] = $fieldPermissions;
        }

        //user permissions 
        $userFieldPermissions = Permission::whereIn('name', $userPermissions)
        ->where('guard_name', 'web')
        ->get();

        //third party permissions
        $thirdPartyFieldPermissions = Permission::whereIn('name', $thirdPartyPermissions)
        ->where('guard_name', 'web')
        ->get();

        //tab and sections project permissions
        $tabAndSectionProjectPermissions = [];
        foreach ($tabAndSectionProjectFieldPermissions as $section => $fieldPermissionNames) {
            
            $fieldPermissions = Permission::whereIn('name', $fieldPermissionNames)->where('guard_name','web')->get();
            $tabAndSectionProjectPermissions[$section] = $fieldPermissions;
        }

        //mobile permissions
        $mobilePermissions = Permission::whereIn('name', $mobilePermissions)
        ->where('guard_name', 'web')
        ->get();

        // Get the permissions assigned to the role
        $rolePermissions = $role->permissions->pluck('id')->toArray(); // Get permission IDs instead of names

        // Passing grouped permissions to the view
        return view('role.edit', compact('role','projectFieldPermissions',
        'generalPermissions','userFieldPermissions','mobilePermissions',
        'thirdPartyFieldPermissions','tabAndSectionProjectPermissions', 'rolePermissions','permissionLabels'));
        
    }



    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //on lack of permission
        if (!auth()->user()->can('update-roles')) return abort(403);

        $webRole = Role::find($id);
        if(!isset($webRole)) return abort(404);// if role wasn't found
        
        $apiRole = Role::where('name', $webRole->name)->where('guard_name','api')->first();
        //remove all permissions from the role
        $webRole->revokePermissionTo($webRole->permissions);
        $apiRole->revokePermissionTo($apiRole->permissions);

        
        // Retrieve `web` guard permissions based on the provided IDs
        $webPermissions = Permission::whereIn('id', $request->permissions)
        ->where('guard_name', 'web')
        ->get();

        // Retrieve `api` guard permissions based on the same permission names
        $apiPermissions = Permission::whereIn('name', $webPermissions->pluck('name'))
            ->where('guard_name', 'api')
            ->get();

        $webRole->givePermissionTo($webPermissions);
        $apiRole->givePermissionTo($apiPermissions);

        //to show message in index toast
        session()->flash('updated_toast', 'نقش با موفقیت تغییر یافت.');
        return redirect()->route('role.index');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {

        //on lack of permission
        if (!auth()->user()->can('delete-roles')) return abort(403);

        $webRole = Role::find($id);
        if(!isset($webRole)) return abort(404);// if role wasn't found
        $apiRole = Role::where('name', $webRole->name)->where('guard_name','api')->first();

        $webRole->delete();
        $apiRole->delete();

        //to show message in index toast
        session()->flash('destroyed_toast', 'نقش با موفقیت حذف شد.');
        return redirect()->route('role.index');
    }
}
