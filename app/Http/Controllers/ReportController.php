<?php

namespace App\Http\Controllers;

use App\Exports\CustomProjectsExport;
use App\Exports\ProjectsExport;
use App\Models\Bank;
use App\Models\Defect;
use App\Models\IranCity;
use App\Models\IranCounty;
use App\Models\IranProvince;
use App\Models\Project;
use App\Models\ProjectSupport;
use App\Models\User;
use App\Models\Village;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;
use Verta;

class ReportController extends Controller
{

    private function getDepositRows($project){
        $export="";
        $i=1;
        foreach ($project->deposit as $value) {
            $export.="واریزی #".$i." | ".$value->persian_type." | ".$value->bankId?->name." | ".convertDate($value->payment_date)." | ".$value->amount." | ";
            // dd($value);
            $i++;
        }
       
        return $export;
    }
    public function projects()
    {
        //on lack of permission
        if (!auth()->user()->can('view-reports-projects')) return abort(403);
        
        $projectColumns = [
    
            'اطلاعات تکمیلی' => [
                'electricity_county_id' => 'شهرستان برق',
                'electricity_affairs_code' => 'کد امور برق',
                'electricity_bill_id' => 'شناسه قبض برق',
                'mehrsan_file_number' => 'شماره پرونده مهرسان',
                'mehrsan_status' => 'وضعیت مهرسان',
                'mehrsan_date'=>'اخرین تغییر مهرسان',
                'announcement_sanam'=>'ابلاغ در سنم',
                'insurance' => 'بیمه',
                'support_needed' => 'نیاز به پشتیبانی',
                'loan_received_date' => 'تاریخ دریافت وام',
                'project_code' => 'کد پروژه',
                'title' => 'عنوان',
                'postal_code' => 'کد پستی',
                'power_plant_capacity' => 'قدرت نیروگاه کیلو وات',
                'support_organization' => 'سازمان حمایتی',
                'province_id' => 'استان',
                'county_id' => 'شهرستان',
                'city_id' => 'شهر',
                'village_id' => 'روستا',
                'address' => 'آدرس',
                'type' => 'نوع',
                'status' => 'وضعیت',
                'accounting_status' => 'وضعیت حسابداری',
                'checking_title' => 'بررسی ها',
                'project_support_comment' => 'توضیحات کلی پروژه و پشتیبانی',

            ],
            'اطلاعات مالی' => [
                'balance' => 'مبلغ کلی که باید واریز شود',
                'payment_bank' => 'بانک واریزی',
                'payment_status' => 'وضعیت واریزی',
                'remaining_deposit' => 'مانده',
                'paid_amount' => 'مبلغ واریزی',
                'payment_date' => 'تاریخ واریز',
                // 'type' => 'نوع انتقال',
                // 'info_deposit' => 'توضیحات واریزی',
                'additional_costs_and_dorm_rent' => 'هزینه جانبی و کرایه خوابگاه',
                'additional_costs_and_dorm_rent_payment_date' => 'تاریخ واریز هزینه های جانبی',
                'cost_of_materials' => 'هزینه مصالح',
                'material_cost_payment_date' => 'تاریخ واریز هزینه مصالح',
                'deposit_rows'=>'تمامی واریزی ها',


            ],
            'اطلاعات آمار زمانی پروژه' => [
            'panel_to_meter_duration' => 'زمان انتظار تا اتصال به شبکه',
            'payment_to_execution_duration' => 'مدت زمان واریز تا اجرا',
            'payment_to_network_connection_duration' => 'مدت زمان واریز تا اتصال به شبکه',
            'installation_to_today_duration' => 'مدت زمان نصب تا امروز ',
            'payment_until_today' => 'مدت زمان واریز تا امروز',

            ],
            'وضعیت عملیات اجرایی' => [
                'current_phase' => 'مرحله اجرایی',

            ],
            'اطلاعات بازدید' => [
                'inspection_status' => 'وضعیت بازدید',
                'inspection_date' => 'تاریخ بازدید',
                'inspector_id' => 'بازدید کننده',
                'inspector_comment' => 'کامنت بازدیدکننده',
                'inverter_to_electrical_base_distance' => 'فاصله اینورتر تا پایه ی برق',
                'utm_y' => 'UTM Y',
                'utm_x' => 'UTM x',
                'longitude' => 'long',
                'latitude' => 'latitude',
                'google_maps_link' => 'لینک کامل موقعیت گوگل',
            ],
            'اطلاعات طراحی' => [
                'designer_id' => 'طراح',
                'design_file_result' => 'نتیجه طراحی فایل',

            ],
            'اطلاعات ارسال جنس' => [
                'ware_postage_date' => 'تاریخ ارسال جنس',
                'ware_sender_id' => 'شخص ارسال کننده جنس',
                'send_list_comments' => 'لیست ارسال ها + توضیحات',

            ],
            'اطلاعات اجرا' => [
                'panel_installation_date' => 'تاریخ نصب پنل',
                'installer_id' => 'نصاب',
                'earth_resistance' => 'مقدار مقاومت ارت',
                'measurement_date' => 'تاریخ اندازه گیری ارت',
                'execution_type' => 'نوع اجرا ارت',
                'structure_type_image' => 'نوع سازه عکس',
                'panel_type' => 'نوع پنل',
                'panel_power' => 'توان پنل',
                'panel_quantity' => 'تعداد پنل',
                'inverter_model' => 'مدل اینورتر',
                'inverter_serial_number_image' => 'سریال اینورتر عکس',
                'inverter_to_the_power_base_cable_used' => 'مقدار کابل مصرفی تا پایه برق',
                'has_monitoring' => 'مانیتورینگ',
                'monitoring_code' => 'بارکد مانیتورینگ',
                'monitoring_installer_id' => 'نصاب مانیتورینگ',
            ],
            'اطلاعات اتصال به شبکه' => [
                'simban_id' => 'مسئول اتصال به شبکه',
                'network_connection_comment' => 'توضیحات اتصال به شبکه',
                'meter_serial_number_image' => 'سریال کنتور عکس',
                'meter_assignment' => 'اختصاص کنتور',
                'sim_card_number' => 'شماره سیم کارت',
                'gis_code' => 'کد GIS',
                'meter_installation_date' => 'تاریخ نصب کنتور و اتصال به شبکه',

            ],
            'نواقص ارسالی' => [
                'defect_comment' => 'توضیحات نواقص ارسالی',
                'defects_title' => 'نواقص',

            ],
            'نواقص اجرایی' => [
                'execution_defects_title' => 'نواقص اجرایی',
                'fix_execution_defects_title' => 'نواقص اجرایی برطرف شده',
                'not_fix_execution_defects_title' => 'نواقص اجرایی مانده',

                'execution_defect_comment' => 'توضیحات نواقص اجرایی',

            ],
            'ارزیابی' => [
                'checking_comment' => 'توضیحات بازررسی',

            ]
        ];
        $userColumns = [
            'اطلاعات عمومی مشتری' => [
                'last_name' => 'نام',
                'phone' => 'تلفن',
                'phone2' => 'تلفن دوم',
                'father_name' => 'نام پدر',
                'national_code' => 'کد ملی',
                'birth_date' => 'تاریخ تولد',
                'bank' => 'بانک عامل',
                'bank_branch' => 'شعبه بانک',
                'bank_account_number' => 'شماره حساب بانک',
                'bank_account_iban' => 'شماره شبا بانک مشترک',
            ]
        ];

        $mergedColumns = $userColumns + $projectColumns;
        $groupedColumns = $userColumns + $projectColumns;

        $userAccessedProvinceIds = auth()->user()->accessedProvinces()->pluck('province_id')->toArray() ?? [];
        $provinces = IranProvince::whereIn('id', $userAccessedProvinceIds)->get();
        $villages = Village::get();
        $banks = Bank::get();

        $supportOrganizations = config('sun.params.supportOrganization');
        $accessedSupportOrganizationValues = auth()->user()->supportOrganizations()->pluck('support_organization_code')->toArray() ?? [];

        $support_organizations = array_filter($supportOrganizations, function ($organization) use ($accessedSupportOrganizationValues) {
            return in_array($organization['value'], $accessedSupportOrganizationValues);
        });


        $projects = Project::whereIn('support_organization', $accessedSupportOrganizationValues)
        ->whereIn('projects.province_id', $userAccessedProvinceIds)->get(); 
       
        $search = [];
        $params = config('sun.params');
       
        $cities = IranCity::all();


        $counties = IranCounty::all();
        return view('report.projects.index', [
            'params' => $params, 'cities' => $cities, 'mergedColumns' => $mergedColumns, 'groupedColumns' => $groupedColumns,
            'provinces' => $provinces, 'counties' => $counties, 'projects' => $projects, 'search' => $search, 'support_organizations' => $support_organizations,
            'villages'=>$villages,'banks'=>$banks
        ]);
    }

    public function projectSearch(Request $request)
    {
        if (!auth()->user()->can('view-reports-projects')) return abort(403);

        ini_set('memory_limit', '-1');
        ini_set('max_execution_time', -1);

        $userAccessedProvinceIds = auth()->user()->accessedProvinces()->pluck('province_id')->toArray() ?? [];
        $accessedSupportOrganizationValues = auth()->user()->supportOrganizations()->pluck('support_organization_code')->toArray() ?? [];

        $query = Project::whereHas('user')
        ->with(['user:id,last_name,phone,phone2,father_name,national_code,birth_date,bank,bank_branch,bank_account_number,bank_account_iban',
        'country:id,name', 'city:id,name', 'province:id,name','deposit'])
        ->whereIn('support_organization', $accessedSupportOrganizationValues)
        ->whereIn('projects.province_id', $userAccessedProvinceIds)
        ;
        $villages = Village::get();

        $banks = Bank::get();
        $filterableFields = [
            'statuses' => 'status',
            //'todolistCategoryTypes' => 'type', // Assuming 'type' refers to todolistCategoryTypes
            'projectTypes' => 'type', // Assuming 'type' refers to projectTypes
            'paymentStatus' => 'payment_status',
            'mehrsanStatus' => 'mehrsan_status',
            // 'snnouncementSanam'=>'announcement_sanam',
            // 'meterAssignment' => 'meter_assignment',
            
            'inspectionStatus' => 'inspection_status',
            'supportStatus' => 'support_needed', // Assuming 'support_needed' is the relevant column
            'insuranceStatus' => 'insurance',
            'designResults' => 'design_file_result',
            'currentPhases' => 'current_phase',
            'inverterModels' => 'inverter_model',
            'supportOrganization' => 'support_organization',
            'powerPlantCapacity' => 'power_plant_capacity',
            'structureType' => 'structure_type_image',
            'province_id' => 'province_id',
            'county_id' => 'county_id',
            'city_id' => 'city_id',
            'village_id' => 'village_id',
        ];

    


        $todos = [
            'defects' => 'defect',
            'checking' => 'checking',
            'execution-defects'=>'executionDefect'
        ];


    
        foreach ($filterableFields as $key => $field) {
            $query->when(isset($request->$key), function ($q) use ($request, $key, $field) {

                if (is_array($request->$key)) {
                    $q->whereIn("projects.$field", $request->$key);
                } elseif($request->$key!="") {

                    $q->where("projects.$field", '=', $request->$key);
                }
                
            });
        }



        $this->handleDateFilter($request, $query);

        foreach ($todos as $key => $value) {
            if ($request->has('todolistCategoryTypes') && in_array($key, $request['todolistCategoryTypes'] ?? []))
                $query->whereHas($value, fn ($q) => $q->with('todolist_categories'));
        }
        if ($request->has('typePaymentStatus'))
        {
            $query->whereHas('deposit', function ($q) use ($request) {
                $q->whereIn('type', $request->typePaymentStatus);
            });
        }
        if ($request->has('typePaymentStatus'))
        {
            $query->whereHas('deposit', function ($q) use ($request) {
                $q->whereIn('type', $request->typePaymentStatus);
            });
        }
        $user = User::first();
        $user->attributesToArray();



        $columns = [
            'project_code' => 'کد پروژه',
            'title' => 'عنوان',
            'electricity_county_id' => 'شهرستان برق',
            'electricity_affairs_code' => 'کد امور برق',
            'electricity_bill_id' => 'شناسه قبض برق',
            'mehrsan_file_number' => 'شماره پرونده مهرسان',
            'loan_received_date' => 'تاریخ دریافت وام',
            'postal_code' => 'کد پستی',
            'power_plant_capacity' => 'قدرت نیروگاه کیلو وات',
            'support_organization' => 'سازمان حمایتی',
            'project_support_comment' => 'توضیحات کلی پروژه و پشتیبانی',

            
            'province_id' => 'استان',
            'county_id' => 'شهرستان',
            'city_id' => 'شهر',
            'village_id' => 'روستا',

            'address' => 'آدرس',
            'accounting_status' => 'وضعیت حسابداری',
            'discount' => 'تخفیف',
            'payment_bank' => 'بانک واریزی',
            'payment_status' => 'وضعیت واریزی',
            'remaining_deposit' => 'مانده',
            'paid_amount' => 'مبلغ واریزی',
            'payment_date' => 'تاریخ واریز',
            'balance' => 'مبلغ کلی که باید واریز شود',
            'deposit_rows'=>'تمامی واریزی ها',
            'inspection_status' => 'وضعیت بازدید',
            'inspection_date' => 'تاریخ بازدید',
            'inspector_id' => 'بازدید کننده',
            'inspector_comment' => 'کامنت بازدیدکننده',
            'design_file_result' => 'نتیجه طراحی فایل',
            'panel_installation_date' => 'تاریخ نصب پنل',
            
            'meter_installation_date' => 'تاریخ نصب کنتور و اتصال به شبکه',
            'simban_id' => 'مسئول اتصال به شبکه',
            'panel_to_meter_duration' => 'زمان انتظار تا اتصال به شبکه',
            'payment_to_execution_duration' => 'مدت زمان واریز تا اجرا',
            'payment_to_network_connection_duration' => 'مدت زمان واریز تا اتصال به شبکه',
            'installation_to_today_duration' => 'مدت زمان نصب تا امروز ',
            'payment_until_today' => 'مدت زمان واریز تا امروز',
            'network_connection_comment' => 'توضیحات اتصال به شبکه',
            'installer_id' => 'نصاب',
            'earth_resistance' => 'مقدار مقاومت ارت',
            'measurement_date' => 'تاریخ اندازه گیری ارت',
            'execution_type' => 'نوع اجرا ارت',
            'designer_id' => 'طراح',
            'structure_type_image' => 'نوع سازه عکس',
            'current_phase' => 'مرحله اجرایی',
            'send_list_comments' => 'لیست ارسال ها + توضیحات',
            'additional_costs_and_dorm_rent' => 'هزینه جانبی و کرایه خوابگاه',
            'additional_costs_and_dorm_rent_payment_date' => 'تاریخ واریز هزینه های جانبی',
            'cost_of_materials' => 'هزینه مصالح',
            'material_cost_payment_date' => 'تاریخ واریز هزینه مصالح',
            'panel_type' => 'نوع پنل',
            'panel_power' => 'توان پنل',
            'panel_quantity' => 'تعداد پنل',
            'inverter_model' => 'مدل اینورتر',
            'inverter_serial_number_image' => 'سریال اینورتر عکس',
            'inverter_to_electrical_base_distance' => 'فاصله اینورتر تا پایه ی برق',
            'inverter_to_the_power_base_cable_used' => 'مقدار کابل مصرفی تا پایه برق',
            'meter_serial_number_image' => 'سریال کنتور عکس',
            'sim_card_number' => 'شماره سیم کارت',
            'meter_assignment' => 'اختصاص کنتور',
            'utm_y' => 'UTM Y',
            'utm_x' => 'UTM x',
            'longitude' => 'long',
            'latitude' => 'latitude',
            'google_maps_link' => 'لینک کامل موقعیت گوگل',
            'gis_code' => 'کد GIS',
            'has_monitoring' => 'مانیتورینگ',
            'monitoring_code' => 'بارکد مانیتورینگ',
            'monitoring_installer_id' => 'نصاب مانیتورینگ',
            'mehrsan_status' => 'وضعیت مهرسان',
            'mehrsan_date'=>'اخرین تغییر مهرسان',
            'announcement_sanam'=>'ابلاغ در سنم',
            'insurance' => 'بیمه',
            'support_needed' => 'نیاز به پشتیبانی',
            'defect_comment' => 'توضیحات نواقص ارسالی',
            'checking_comment' => 'توضیحات بازررسی',
            'ware_postage_date' => 'تاریخ ارسال جنس',
            'ware_sender_id' => 'شخص ارسال کننده جنس',
            'type' => 'نوع',
            'status' => 'وضعیت',
            'defects_title' => 'نواقص ارسالی',
            'checking_title' => 'بررسی ها',
            'execution_defects_title' => 'نواقص اجرایی',
            'fix_execution_defects_title' => 'نواقص اجرایی برطرف شده',
            'not_fix_execution_defects_title' => 'نواقص اجرایی مانده',
            'execution_defect_comment' => 'توضیحات نواقص اجرایی',

            
        ];
        $userColumns = [
            // 'first_name' => 'نام',
            'last_name' => 'نام',
            'phone' => 'تلفن',
            'phone2' => 'تلفن دوم',
            'father_name' => 'نام پدر',
            'national_code' => 'کد ملی',
            'birth_date' => 'تاریخ تولد',
            'bank' => 'بانک عامل',
            'bank_branch' => 'شعبه بانک',
            'bank_account_number' => 'شماره حساب بانک',
            'bank_account_iban' => 'شماره شبا بانک مشترک',
        ];
        if ($request->action == 'export') {
            $filename = 'projects_' . date('YmdHis') . '.csv';
            $path = storage_path('app/private/reports' . $filename);
            $handle = fopen($path, 'w+');
            fwrite($handle, "\xEF\xBB\xBF");
            if (isset($request->columns)) {
                if ($request->columns == 'all') {
                    // do nothing
                } elseif ($request->columns == 'none') {
                    $columns = [];
                    $userColumns = [];
                } elseif (is_array($request->columns)) {
                    $flippedColumnsToKeep = array_flip($request->columns);
                    $columns = array_intersect_key($columns, $flippedColumnsToKeep);
                    $userColumns = array_intersect_key($userColumns, $flippedColumnsToKeep);
                } else {
                    // handel error ??
                }
            }
            $headers = array_values(array_merge($userColumns, $columns));

            fputcsv($handle, $headers, ';'); // header row
        }
        // $projects = $query->get();
        $exportData = [];
        $projects = [];

        $query->chunk(200, function ($chunk) use (&$exportData,&$projects,&$request,&$columns,&$userColumns) {
            foreach ($chunk as $project) {
                $exportData[] = $this->processProject($project,$request,$columns,$userColumns);
                $projects[] = $project;

            }
        });
      
        if ($request->action == 'export')
            fclose($handle);
  

        if ($request->action == 'export')
          
            return Excel::download(new CustomProjectsExport($exportData, $headers), 'projects_' . date('YmdHis') . '.xlsx');
        else{

        
        // return redirect()->back()->with(['projects' => collect($projects), 'search' => $request->all()]);
        if (!auth()->user()->can('view-reports-projects')) return abort(403);
        
        $projectColumns = [
    
            'اطلاعات تکمیلی' => [
                'electricity_county_id' => 'شهرستان برق',
                'electricity_affairs_code' => 'کد امور برق',
                'electricity_bill_id' => 'شناسه قبض برق',
                'mehrsan_file_number' => 'شماره پرونده مهرسان',
                'mehrsan_status' => 'وضعیت مهرسان',
                'mehrsan_date'=>'اخرین تغییر مهرسان',
                'announcement_sanam'=>'ابلاغ در سنم',
                'insurance' => 'بیمه',
                'support_needed' => 'نیاز به پشتیبانی',
                'loan_received_date' => 'تاریخ دریافت وام',
                'project_code' => 'کد پروژه',
                'title' => 'عنوان',
                'postal_code' => 'کد پستی',
                'power_plant_capacity' => 'قدرت نیروگاه کیلو وات',
                'support_organization' => 'سازمان حمایتی',
                'province_id' => 'استان',
                'county_id' => 'شهرستان',
                'city_id' => 'شهر',
                'village_id' => 'روستا',
                'address' => 'آدرس',
                'type' => 'نوع',
                'status' => 'وضعیت',
                'accounting_status' => 'وضعیت حسابداری',
                'checking_title' => 'بررسی ها',
                'project_support_comment' => 'توضیحات کلی پروژه و پشتیبانی',

            ],
            'اطلاعات مالی' => [
                'balance' => 'مبلغ کلی که باید واریز شود',
                'payment_bank' => 'بانک واریزی',
                'payment_status' => 'وضعیت واریزی',
                'remaining_deposit' => 'مانده',
                'paid_amount' => 'مبلغ واریزی',
                'payment_date' => 'تاریخ واریز',
                // 'type' => 'نوع انتقال',
                // 'info_deposit' => 'توضیحات واریزی',
                'additional_costs_and_dorm_rent' => 'هزینه جانبی و کرایه خوابگاه',
                'additional_costs_and_dorm_rent_payment_date' => 'تاریخ واریز هزینه های جانبی',
                'cost_of_materials' => 'هزینه مصالح',
                'material_cost_payment_date' => 'تاریخ واریز هزینه مصالح',
                'deposit_rows'=>'تمامی واریزی ها',


            ],
            'اطلاعات آمار زمانی پروژه' => [
            'panel_to_meter_duration' => 'زمان انتظار تا اتصال به شبکه',
            'payment_to_execution_duration' => 'مدت زمان واریز تا اجرا',
            'payment_to_network_connection_duration' => 'مدت زمان واریز تا اتصال به شبکه',
            'installation_to_today_duration' => 'مدت زمان نصب تا امروز ',
            'payment_until_today' => 'مدت زمان واریز تا امروز',

            ],
            'وضعیت عملیات اجرایی' => [
                'current_phase' => 'مرحله اجرایی',

            ],
            'اطلاعات بازدید' => [
                'inspection_status' => 'وضعیت بازدید',
                'inspection_date' => 'تاریخ بازدید',
                'inspector_id' => 'بازدید کننده',
                'inspector_comment' => 'کامنت بازدیدکننده',
                'inverter_to_electrical_base_distance' => 'فاصله اینورتر تا پایه ی برق',
                'utm_y' => 'UTM Y',
                'utm_x' => 'UTM x',
                'longitude' => 'long',
                'latitude' => 'latitude',
                'google_maps_link' => 'لینک کامل موقعیت گوگل',
            ],
            'اطلاعات طراحی' => [
                'designer_id' => 'طراح',
                'design_file_result' => 'نتیجه طراحی فایل',

            ],
            'اطلاعات ارسال جنس' => [
                'ware_postage_date' => 'تاریخ ارسال جنس',
                'ware_sender_id' => 'شخص ارسال کننده جنس',
                'send_list_comments' => 'لیست ارسال ها + توضیحات',

            ],
            'اطلاعات اجرا' => [
                'panel_installation_date' => 'تاریخ نصب پنل',
                'installer_id' => 'نصاب',
                'earth_resistance' => 'مقدار مقاومت ارت',
                'measurement_date' => 'تاریخ اندازه گیری ارت',
                'execution_type' => 'نوع اجرا ارت',
                'structure_type_image' => 'نوع سازه عکس',
                'panel_type' => 'نوع پنل',
                'panel_power' => 'توان پنل',
                'panel_quantity' => 'تعداد پنل',
                'inverter_model' => 'مدل اینورتر',
                'inverter_serial_number_image' => 'سریال اینورتر عکس',
                'inverter_to_the_power_base_cable_used' => 'مقدار کابل مصرفی تا پایه برق',
                'has_monitoring' => 'مانیتورینگ',
                'monitoring_code' => 'بارکد مانیتورینگ',
                'monitoring_installer_id' => 'نصاب مانیتورینگ',
            ],
            'اطلاعات اتصال به شبکه' => [
                'simban_id' => 'مسئول اتصال به شبکه',
                'network_connection_comment' => 'توضیحات اتصال به شبکه',
                'meter_serial_number_image' => 'سریال کنتور عکس',
                'sim_card_number' => 'شماره سیم کارت',
                'meter_assignment' => 'اختصاص کنتور',
                'gis_code' => 'کد GIS',
                'meter_installation_date' => 'تاریخ نصب کنتور و اتصال به شبکه',

            ],
            'نواقص ارسالی' => [
                'defect_comment' => 'توضیحات نواقص ارسالی',
                'defects_title' => 'نواقص',

            ],
            'نواقص اجرایی' => [
                'execution_defects_title' => 'نواقص اجرایی',
                'fix_execution_defects_title' => 'نواقص اجرایی برطرف شده',
                'not_fix_execution_defects_title' => 'نواقص اجرایی مانده',

                'execution_defect_comment' => 'توضیحات نواقص اجرایی',

            ],
            'ارزیابی' => [
                'checking_comment' => 'توضیحات بازررسی',

            ]
        ];
        $userColumns = [
            'اطلاعات عمومی مشتری' => [
                'last_name' => 'نام',
                'phone' => 'تلفن',
                'phone2' => 'تلفن دوم',
                'father_name' => 'نام پدر',
                'national_code' => 'کد ملی',
                'birth_date' => 'تاریخ تولد',
                'bank' => 'بانک عامل',
                'bank_branch' => 'شعبه بانک',
                'bank_account_number' => 'شماره حساب بانک',
                'bank_account_iban' => 'شماره شبا بانک مشترک',
            ]
        ];

        $mergedColumns = $userColumns + $projectColumns;
        $groupedColumns = $userColumns + $projectColumns;

        $userAccessedProvinceIds = auth()->user()->accessedProvinces()->pluck('province_id')->toArray() ?? [];
        $provinces = IranProvince::whereIn('id', $userAccessedProvinceIds)->get();

        $supportOrganizations = config('sun.params.supportOrganization');
        $accessedSupportOrganizationValues = auth()->user()->supportOrganizations()->pluck('support_organization_code')->toArray() ?? [];

        $support_organizations = array_filter($supportOrganizations, function ($organization) use ($accessedSupportOrganizationValues) {
            return in_array($organization['value'], $accessedSupportOrganizationValues);
        });


        $projects = collect($projects); 
       
        $search = $request->all();
        $params = config('sun.params');
        // Clear session data after retrieving it
        // session()->forget('projects');
        // session()->forget('search');
        $cities = IranCity::all();


        $counties = IranCounty::all();
        return view('report.projects.index', [
            'params' => $params, 'cities' => $cities, 'mergedColumns' => $mergedColumns, 'groupedColumns' => $groupedColumns,
            'provinces' => $provinces, 'counties' => $counties, 'projects' => $projects, 'search' => $search, 'support_organizations' => $support_organizations,
            'villages'=>$villages,'banks'=>$banks

        ]);
        }
    }

    private function processProject($project,$request,$columns,$userColumns)
    {
        if ($request->action == 'export') {
            $project->support_organization = $project->persian_support_organization;
            $project->province_id = $project->province?->name;
            $project->county_id = $project->country?->name;
            $project->city_id = $project->city?->name;
            $project->village_id = $project->village?->name;


            $project->electricity_county_id = $project->electricCity?->name;
            $project->current_phase = $project->persian_current_phase;
            $project->inspector_id = $project->inspector?->name;
            $project->simban_id = $project->simban?->name;
            $project->payment_bank = $project->payment_bank;
            $project->payment_until_today = $project->payment_date ? (new Carbon($project->payment_date))->diffInDays((Carbon::now()->format('Y-m-d'))): '';
            $project->payment_date = convertDate($project->payment_date);
            $project->installation_to_today_duration = $project->panel_installation_date ? (new Carbon($project->panel_installation_date))->diffInDays((Carbon::now()->format('Y-m-d'))): '';
            $project->deposit_rows = $this->getDepositRows($project);
            $project->ware_postage_date = convertDate($project->ware_postage_date);
            $project->inspection_date = convertDate($project->inspection_date);
            $project->mehrsan_date = convertDate($project->mehrsan_date);
            $project->panel_installation_date = convertDate($project->panel_installation_date);
            $project->measurement_date = convertDate($project->measurement_date);
            $project->meter_installation_date = convertDate($project->meter_installation_date);
            $project->additional_costs_and_dorm_rent_payment_date = convertDate($project->additional_costs_and_dorm_rent_payment_date);
            $project->material_cost_payment_date = convertDate($project->material_cost_payment_date);

        } else {
            $project->province_id = $project->province?->id;
            $project->county_id = $project->country?->id;
            $project->city_id = $project->city?->id;
            $project->village_id = $project->village?->name;

        }

        $project->ware_sender_id = $project->wareSender?->name;
        $project->designer_id = $project->designer?->name;
        $project->installer_id = $project->installer?->name;
        $project->payment_status = $project->persian_payment_status;
        $project->inspection_status = $project->persian_inspection_status;
        $project->design_file_result = $project->persian_design_file_result;
        $project->structure_type_image = $project->persian_structure_type;
        $project->power_plant_capacity = $project->persian_power_plant_capacity;
        $project->mehrsan_status = $project->persian_mehrsan;
        $project->meter_assignment = $project->persian_meter_assignment;
        $project->announcement_sanam = $project->persian_announcement_sanam;
        $project->accounting_status = $project->persian_accounting_status;
        $project->insurance = $project->persian_insurance;
        $project->support_needed = $project->persian_support_needed;
        $project->type = $project->persian_type;
        $project->status = $project->persian_status;
        $project->defects_title = implode(',', $project->defect->pluck('todolistCategory')->pluck('title')->toArray());
        $project->checking_title = implode(',', $project->checking->pluck('todolistCategory')->pluck('title')->toArray());
        $allExecutionDefect=$project->executionDefect->pluck('todolistCategory')->pluck('title')->toArray();
        $fixExecutionDefect=$project->executionDefect->whereNotNull('fix_execution_defect_user_id')->pluck('todolistCategory')->pluck('title')->toArray();
        $notFixExecutionDefect=array_diff($allExecutionDefect,$fixExecutionDefect);
        $project->execution_defects_title = implode(',',$allExecutionDefect );
        $project->fix_execution_defects_title = implode(',', $fixExecutionDefect);
        $project->not_fix_execution_defects_title = implode(',', $notFixExecutionDefect);
      

        $projectValue = $project->attributesToArrayWithColumns($columns);
        $userValue = array_values(array_intersect_key($project->user?->attributesToArray() ?? [], $userColumns));

        $values = array_merge($userValue, $projectValue);
        // Process and return project data
        return $values;
    }

    function handleDateFilter($request, $query)
    {
        $dateFilterField = [
            'payment_date',
            'execution_date',
            'inspection_date',
            'panel_installation_date',
            'meter_installation_date',
        ];

        foreach ($dateFilterField as $field) {
            if (!$request->has($field))
                continue;
            $query->when((isset($request[$field]['first']) && $request[$field]['first'] != ''), fn ($q) => $q->where("projects.$field", '>=', Verta::parse($request[$field]['first'])->toCarbon()));
            $query->when((isset($request[$field]['second']) && $request[$field]['second'] != ''), fn ($q) => $q->where("projects.$field", '<=', Verta::parse($request[$field]['second'])->toCarbon()));
        }
    }

    public function users()
    {

        //on lack of permission
        if (!auth()->user()->can('view-reports-users')) return abort(403);

        return view('report.users.index');
    }


    public function userSearch(Request $request)
    {
        //on lack of permission
        if (!auth()->user()->can('view-reports-users')) return abort(403);

        ini_set('memory_limit', '-1');
        ini_set('max_execution_time', -1);
        $installers = Project::with('installer')->select([DB::raw('installer_id as uId'), DB::raw('Count(projects.id) as count')]);
        $this->handleDateFilter($request, $installers);
        $installers = $installers->groupBy('installer_id')->get();

        $inspectors = Project::with('inspector')->select([DB::raw('inspector_id as uId'), DB::raw('count(*) as count')]);
        $this->handleDateFilter($request, $inspectors);
        $inspectors = $inspectors->groupBy('inspector_id')->get();

        $designers = Project::with('designer')->select([DB::raw('designer_id as uId'), DB::raw('count(*) as count')]);
        $this->handleDateFilter($request, $designers);
        $designers = $designers->groupBy('designer_id')->get();


        $supporters = Project::select(['supporter_id as uId', DB::raw('COUNT(ps.id)  as count')])
            ->join('project_supports as ps', 'ps.project_id', '=', 'projects.id')
            ->join('users as u', 'u.id', '=', 'ps.supporter_id');
        $this->handleDateFilter($request, $supporters);

        $supporters = $supporters->groupBy('ps.supporter_id')
            ->get();

        $userIds = array_merge(
            $installers->pluck('uId')->toArray(),
            $inspectors->pluck('uId')->toArray(),
            $designers->pluck('uId')->toArray(),
            $supporters->pluck('uId')->toArray(),
        );
        $users = User::whereIn('id', $userIds)->get();
        if ($request->action == 'export') {
            $filename = 'users_' . date('YmdHis') . '.csv';
            $path = storage_path('app/private/reports' . $filename);
            $handle = fopen($path, 'w+');
            fwrite($handle, "\xEF\xBB\xBF");

            $headers = ['نام', 'تعداد نصب تیم اجرایی', 'تعداد خدمات پشتیبانی', 'تعداد بازدید از پروژه', 'تعداد طراحی پروژه', 'تعداد کل مشارکت'];

            fputcsv($handle, $headers, ';'); // header row
        }
        $values = [];
        foreach ($users as $user) {
            $install = $installers->where('uId', $user->id)->first()?->count ?? 0;
            $support = $supporters->where('uId', $user->id)->first()?->count ?? 0;
            $inspect = $inspectors->where('uId', $user->id)->first()?->count ?? 0;
            $design = $designers->where('uId', $user->id)->first()?->count ?? 0;
            $total = $install + $support + $inspect + $design;
            $values = [
                $user->name,
                $install,
                $support,
                $inspect,
                $design,
                $total,
            ];
            if ($request->action == 'export')
                fputcsv($handle, $values, ';');

            // Merge these values with the user object
            $user->install = $install;
            $user->support = $support;
            $user->inspect = $inspect;
            $user->design = $design;
            $user->total = $total;
        }
        if ($request->action == 'export')
            fclose($handle);



        if ($request->action == 'export')
            // Download the CSV file
            return response()->download($path, $filename, [
                'Content-Type' => 'text/csv',
                'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            ]);



        $search = $request->all();
        return view('report.users.index', compact(['users', 'search']));
    }

    public function financials()
    {
        //on lack of permission
        if (!auth()->user()->can('view-reports-financials')) return abort(403);

        $userAccessedProvinceIds = auth()->user()->accessedProvinces()->pluck('province_id')->toArray() ?? [];
        
        
        // $provinces_list = Project::all()->groupBy('province_id');
        $provinces_list = Project::whereIn('province_id', $userAccessedProvinceIds)
        ->get() // Fetch the filtered projects
        ->groupBy('province_id'); // Group them by province_id
        // Initialize an empty array to store the data
        $financials = [];

        

        // Loop through each province and calculate the required data
        foreach ($provinces_list as $province) {

            $provinceName = $province->first()->province->name;
            $provinceId = $province->first()->province->id;
            // Default team count is 3 if not provided
            $teamCount = 3;

            $configSupportOrganizations = config('sun.params.supportOrganization');

            $accessedSupportOrganizationValues = auth()->user()
                ->supportOrganizations()
                ->pluck('support_organization_code')
                ->map(fn($code) => str_pad($code, 2, '0', STR_PAD_LEFT))
                ->toArray();

            $supportOrganizations = [];

            foreach ($configSupportOrganizations as $organization) {
                if (in_array($organization['value'], $accessedSupportOrganizationValues)) {
                    $supportOrganizations[$organization['value']] = $organization['label'];
                }
            }
            

            // $supportOrganizations = [
            //     '01' => 'کمیته امداد امام خمینی',
            //     '02' => 'بسیج سازندگی',
            //     '03' => 'بهزیستی',
            //     '04' => 'معاونت توسعه روستایی استانداری',
            //     '05' => 'خصوصی',
            //     '06' => 'مجتمع اقتصادی',
            //     '07' => 'بانک رسالت',
            //     '08' => 'بنیاد علوی',
            //     '09' => 'بنیاد برکت',
            //     '10' => 'سازمان اوقاف و امور خیریه',
            //     '11' => 'حوزه علمیه',
            //     '12' => 'شرکت شهرک های صنعتی ایران',
            //     '13' => 'سازمان حفاظت محیط زیست',
            //     '14' => 'خیرین مدرسه ساز',
            //     '15' => 'سایر',

            // ];

            foreach ($supportOrganizations as $code => $organizationName) {

                // Start a fresh query for each organization
                $provinceProjects = Project::where('province_id',$provinceId)->where('support_organization', $code)
                ->whereIn('payment_status', ['deposited', 'deposited-but-has-balance','deposited-more']);

                

                $aaCount = clone $provinceProjects;
                $aaStatusDoneCount = clone $provinceProjects;
                $aaProjectsWithDateDiffs = clone $provinceProjects;
                $aaAggregatedData = clone $provinceProjects;


                // Count projects
                $projectCount = $aaCount->count();

                
                // Count status done
                $statusDoneCount = $aaStatusDoneCount->whereIn('current_phase', ['connected-to-the-network', 'run-ready-to-connect-to-the-network','executed-but-have-defect'])->count();

                // Count status not done
                $statusNotDoneCount = $projectCount - $statusDoneCount;

                // Calculate average deposit to today days
                $projectsWithDateDiffs = $aaProjectsWithDateDiffs
                    ->whereNotNull('payment_date')
                    ->whereNotIn('current_phase', ['connected-to-the-network', 'run-ready-to-connect-to-the-network','executed-but-have-defect'])
                    ->get()
                    ->map(function ($project) {
                        $project->deposit_to_today_days = $project->payment_date 
                            ? Carbon::parse($project->payment_date)->diffInDays(Carbon::now())
                            : null;
                        return $project;
                    });

                $averageDepositToTodayDays = $projectsWithDateDiffs
                    ->whereNotNull('deposit_to_today_days')  // Filter out projects where deposit_to_today_days is null
                    ->avg('deposit_to_today_days');


                // Aggregate data
                $aggregatedData = $aaAggregatedData
                    ->selectRaw('
                        SUM(power_plant_capacity) as sum_of_powers,
                        SUM(ABS(CASE WHEN payment_status = "deposited-but-has-balance" OR payment_status = "deposited" THEN remaining_deposit ELSE 0 END)) as remaining_deposit,
                        SUM(ABS(CASE WHEN payment_status = "deposited-more" THEN remaining_deposit ELSE 0 END)) as remaining_deposit_more,
                        AVG(payment_to_execution_duration) as payment_to_execution_duration,
                        AVG(payment_to_network_connection_duration) as payment_to_network_connection_duration
                    ')->first()
                    ;


              
                // Calculate the approximate end date based on the average install to network time
                $approxEndDate = Carbon::now()->addDays(($statusNotDoneCount / $teamCount))->toDateString();

                // Store the financial data
                $financials[$provinceName][$code] = [
                    'organization_name' => $organizationName,
                    'project_count' => $projectCount,
                    'status_done_count' => $statusDoneCount,
                    'status_not_done_count' => $statusNotDoneCount,
                    'sum_of_powers' => $aggregatedData->sum_of_powers ?? 0,
                    'remaining_deposit' => $aggregatedData->remaining_deposit ?? 0,
                    'remaining_deposit_more' => $aggregatedData->remaining_deposit_more ?? 0,
                    'avg_deposit_to_install' => $aggregatedData->payment_to_execution_duration ?? 0,
                    'avg_deposit_to_today' => $averageDepositToTodayDays ?? 0,
                    'avg_install_to_network' => $aggregatedData->payment_to_network_connection_duration ?? 0,
                    'approx_end_date' => $approxEndDate ?? null,
                ];
            }
        }
        return view('report.financials.index', compact('financials'));
    }


    public function financialSearch(Request $request)
    {
        //on lack of permission
        if (!auth()->user()->can('view-reports-financials')) return abort(403);

        $userAccessedProvinceIds = auth()->user()->accessedProvinces()->pluck('province_id')->toArray() ?? [];
        
        // Provinces you want to filter by
        $provinces_list = Project::whereIn('province_id', $userAccessedProvinceIds)
        ->get()->groupBy('province_id');
    
        // Initialize an empty array to store the data
        $financials = [];

        // Get team counts from the request
        $teamCounts = $request->input('team_count', []);

        // Loop through each province and calculate the required data
        foreach ($provinces_list as $province) {
            
            $provinceName = $province->first()->province->name;

            // Default team count is 4 if not provided
            $teamCount = isset($teamCounts[$provinceName]) ? (int) $teamCounts[$provinceName] : 3;

            $configSupportOrganizations = config('sun.params.supportOrganization');
            $accessedSupportOrganizationValues = auth()->user()
                ->supportOrganizations()
                ->pluck('support_organization_code')
                ->map(fn($code) => str_pad($code, 2, '0', STR_PAD_LEFT))
                ->toArray();

            $supportOrganizations = [];

            foreach ($configSupportOrganizations as $organization) {
                if (in_array($organization['value'], $accessedSupportOrganizationValues)) {
                    $supportOrganizations[$organization['value']] = $organization['label'];
                }
            }
            // $supportOrganizations = [
            //     '01' => 'کمیته امداد امام خمینی',
            //     '02' => 'بسیج سازندگی',
            //     '03' => 'بهزیستی',
            //     '04' => 'معاونت توسعه روستایی استانداری',
            //     '05' => 'خصوصی',
            //     '06' => 'مجتمع اقتصادی',
            //     '07' => 'بانک رسالت',
            //     '08' => 'بنیاد علوی',
            //     '09' => 'بنیاد برکت',
            //     '10' => 'سازمان اوقاف و امور خیریه',
            //     '11' => 'حوزه علمیه',
            //     '12' => 'شرکت شهرک های صنعتی ایران',
            //     '13' => 'سازمان حفاظت محیط زیست',
            //     '14' => 'خیرین مدرسه ساز',
            //     '15' => 'سایر',
            // ];

            foreach ($supportOrganizations as $code => $organizationName) {

                // Start a fresh query for each organization
                $provinceProjects = Project::whereHas('province', function($query) use ($provinceName) {
                    $query->where('name', $provinceName);
                })->where('support_organization', $code)
                ->whereIn('payment_status', ['deposited', 'deposited-but-has-balance','deposited-more']);

                // Apply date filter
                $this->handleDateFilter($request, $provinceProjects);

                $aaCount = clone $provinceProjects;
                // Count projects
                $projectCount = $aaCount->count();

                $aaStatusDoneCount = clone $provinceProjects;
                // Count status done
                $statusDoneCount = $aaStatusDoneCount->whereIn('current_phase', ['connected-to-the-network', 'run-ready-to-connect-to-the-network','executed-but-have-defect'])->count();

                // Count status not done
                $statusNotDoneCount = $projectCount - $statusDoneCount;

                $aaProjectsWithDateDiffs = clone $provinceProjects;
                // Calculate average deposit to today days
                $projectsWithDateDiffs = $aaProjectsWithDateDiffs
                    ->whereNotNull('payment_date')
                    ->whereNotIn('current_phase', ['connected-to-the-network', 'run-ready-to-connect-to-the-network','executed-but-have-defect'])
                    ->get()
                    ->map(function ($project) {
                        $project->deposit_to_today_days = $project->payment_date 
                            ? Carbon::parse($project->payment_date)->diffInDays(Carbon::now())
                            : null;
                        return $project;
                    });

                $averageDepositToTodayDays = $projectsWithDateDiffs
                    ->whereNotNull('deposit_to_today_days')  // Filter out projects where deposit_to_today_days is null
                    ->avg('deposit_to_today_days');


                $aaAggregatedData = clone $provinceProjects;
                // Aggregate data
                $aggregatedData = $aaAggregatedData
                    ->selectRaw('
                        SUM(power_plant_capacity) as sum_of_powers,
                        SUM(ABS(CASE WHEN payment_status = "deposited-but-has-balance" OR payment_status = "deposited" THEN remaining_deposit ELSE 0 END)) as remaining_deposit,
                        SUM(ABS(CASE WHEN payment_status = "deposited-more" THEN remaining_deposit ELSE 0 END)) as remaining_deposit_more,
                        AVG(payment_to_execution_duration) as payment_to_execution_duration,
                        AVG(payment_to_network_connection_duration) as payment_to_network_connection_duration
                    ')
                    ->first();

                

                // Calculate the approximate end date based on the average install to network time
                $approxEndDate = Carbon::now()->addDays(($statusNotDoneCount / $teamCount))->toDateString();

                // Store the financial data
                $financials[$provinceName][$code] = [
                    'organization_name' => $organizationName,
                    'project_count' => $projectCount,
                    'status_done_count' => $statusDoneCount,
                    'status_not_done_count' => $statusNotDoneCount,
                    'sum_of_powers' => $aggregatedData->sum_of_powers ?? 0,
                    'remaining_deposit' => $aggregatedData->remaining_deposit ?? 0,
                    'remaining_deposit_more' => $aggregatedData->remaining_deposit_more ?? 0,
                    'avg_deposit_to_install' => $aggregatedData->payment_to_execution_duration ?? 0,
                    'avg_deposit_to_today' => $averageDepositToTodayDays ?? 0,
                    'avg_install_to_network' => $aggregatedData->payment_to_network_connection_duration ?? 0,
                    'approx_end_date' => $approxEndDate ?? null,
                ];
            }
        }

        $search = $request->all();
        // Pass the data to the view
        return view('report.financials.index', compact('financials', 'search'));
    }



}
