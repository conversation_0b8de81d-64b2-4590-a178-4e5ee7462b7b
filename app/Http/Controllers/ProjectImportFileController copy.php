<?php

namespace App\Http\Controllers;

use App\Http\Requests\User\ImportUserByFileRequest;
use App\Models\Project;
use App\Models\User;
use App\Traits\UsesXlsxToArray;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\HttpFoundation\Response as ResponseAlias;
use Illuminate\Support\Str;
use App\Jobs\ProcessMehrsanImport;

class ProjectImportFileController extends Controller
{
    use UsesXlsxToArray;

    public function changeMehrsanWithImportFile(Request $request)
    {

        return view('projects.import-file.create');
    }

  

    function convertPersianDigitsToEnglish($input) {
        if (!$input)
            return null;
        $persianDigits = ['۰', '۱', '۲', '۳', '۴', '۵', '۶', '۷', '۸', '۹'];
        $englishDigits = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
        
        return str_replace($persianDigits, $englishDigits, $input);
    }

    public function storeMehrsanWithImportFile(ImportUserByFileRequest $request)
    {
        ini_set('memory_limit', '-1');
        ini_set('max_execution_time', -1);
        try {
            $filename = $this->uploadFile();

            // Dispatch job to process file asynchronously
            ProcessMehrsanImport::dispatch($filename);

            session()->flash('created_toast', __('فایل با موفقیت بارگذاری شد و در حال پردازش است.'));
            return redirect()->route('projects.create-import-file');
        } catch (\Exception $e) {
            logger($e->getMessage());
            session()->flash('error_toast', __('مشکلی رخ داده است !'));
            return redirect()->route('projects.create-import-file');
        }


    }

    private function getMehrsanStatusMap(): array
    {
        $mapping = config('sun.params.mehrsanStatus', []);
        $map = [];
        foreach ($mapping as $item) {
            $map[$item['label']] = $item['value'];
        }
        return $map;
    }

    private function uploadFile(): string
    {
        $uuid = now()->timestamp;

        $file = request()->file('file');

        $extension = $file->extension();
        $filename = "$uuid.$extension";

        $file->storeAs('', $filename, 'uploads');

        return $filename;
    }

    private function checkFileAndGetRows(string $filename): array
    {
        list($rows, $columns) = $this->xlsxToArray(storage_path('app/uploads/') . $filename);

        $this->checkColumns($columns);

        return $rows;
    }

    private function checkColumns($columns)
    {
        $requiredColumns = [
            'مرحله_جاری',
            'کد_ملی'
        ];

        foreach ($requiredColumns as $column) {
            $actualColumn = Str::title(str_replace('_', ' ', $column));
            abort_if(!in_array($column, $columns), ResponseAlias::HTTP_FORBIDDEN, str_replace(':column', $actualColumn, __(":column is required")));
        }
    }

    private function processInvitation($request)
    {
        // $request=normalizeRequestCharacter($request);
        $user = User::where('national_code', $request->national_code)->first();
       
        if ($user && $user->projects) {
            foreach ($user->projects as $project) {
                $project->mehrsan_status = $request->status;
                $project->save();
            }
            return 'processed';
        } else {
           //handel not exist project for user
           return 'no_project';
        }
    }

}
