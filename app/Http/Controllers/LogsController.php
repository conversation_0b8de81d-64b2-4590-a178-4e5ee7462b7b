<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;
use Spatie\Activitylog\Models\Activity;
use Illuminate\Support\Facades\Config as FacadesConfig;

class LogsController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return Application|Factory|View|Response
     */
    public function index(Request $request): Factory|View|Response|Application
    {
        //on lack of permission
        if (!auth()->user()->can('view-logs')) return abort(403);

        $all_event_logs = FacadesConfig::get('activitylog.event_logs');
        $all_name_logs = FacadesConfig::get('activitylog.name_logs');

        $logs = Activity::where('created_at', '!=', null)->orderByDesc('created_at');
        if ($request->user_id) {
            if ($request->user_id == -1) {
                $logs = $logs->where('causer_id', null);
            } else if ($request->user_id != 0) {
                $logs = $logs->where('causer_id', $request->user_id);
            }


        }
        if ($request->model_name and $request->model_name != 'All') {
            $logs->where('subject_type', 'App\\Models\\' . $request->model_name);
        }

        if ($request->agent_id and $request->agent_name != 'All') {
            $logs->where('subject_id', $request->agent_id);
        }

        if ($request->event and $request->event != 'all') {
            $logs->where('event', $request->event);
        }

        if ($request->log_name and $request->log_name != 'all') {
            $logs->where('log_name', $request->log_name);
        }


        if ($request->from && $request->to) {
            $from = Carbon::parse($request->from)->format('Y-m-d');
            $to = Carbon::parse($request->to)->format('Y-m-d');
            // $logs = $logs->whereBetween('created_at', [$from,$to]);
            $logs = $logs->whereRaw("DATE(created_at) BETWEEN ? AND ?", [$from, $to]);
            // $logs = $logs->where('created_at', '>=' , $from);
        }
        $logs = $logs->paginate(15);
        foreach ($logs as $log) {
            if ($log->data) {
                $log->data = json_decode($log->data, true);
            }
        }
        $models = Activity::select('subject_type')->distinct()->get();

        return view('logs.index',
            [
                'logs' => $logs,
                'models' => $models,
                'model_name' => $request->model_name,
                'all_name_logs' => $all_name_logs,
                'all_event_logs' => $all_event_logs
            ]);
    }

    public function getAllUsersAjax(Request $request)
    {
        $data = [];
        $search = $request->q;

        if (!$request->has('q')) {
            $data = User::orderby('created_at', 'asc')
                ->select('id', 'first_name', 'last_name')
                ->limit(10)->get();
        } else {
            $data = User::orderby('created_at', 'asc')
                ->select('id', 'first_name', 'last_name')
                ->where('first_name', 'like', '%' . $search . '%')
                ->Orwhere('last_name', 'like', '%' . $search . '%')
                ->limit(10)->get();
        }
        // Retrieve customer and vendor data

        // $vendors = Vendor::orderBy('created_at', 'asc')
        //     ->select('id', 'first_name','last_name')
        //     ->where('name', 'like', '%' . $search . '%')
        //     ->orWhere('email', 'like', '%' . $search . '%')
        //     ->limit(10)
        //     ->get();

        // Merge user, customer, and vendor data into a single collection

        foreach ($data as $d) {
            $d['name'] = $d->first_name . ' ' . $d->last_name;
        }
        return response()->json($data);
    }

    public function getUsersAjax(Request $request)
    {
        $data = [];
        $search = $request->q;

        if (!$request->has('q')) {
            $data = User::orderby('created_at', 'asc')->select('id', 'first_name', 'last_name')->limit(10)->get();
        } else {
            $data = User::orderby('created_at', 'asc')->select('id', 'first_name', 'last_name')
                ->where('first_name', 'like', '%' . $search . '%')
                ->Orwhere('last_name', 'like', '%' . $search . '%')
                ->limit(10)->get();
        }


        //->concat($vendors);
        foreach ($data as $d) {
            $d['name'] = $d->first_name . ' ' . $d->last_name;
        }
        return response()->json($data);
    }


    public function getModelsAjax(Request $request)
    {
        $data = [];
        $search = $request->q;
        if (!$request->q) {
            $data = Activity::select('subject_type')->distinct()->limit(10)->get();
        } else {

            $data = Activity::select('subject_type',)
                ->where('subject_type', 'like', '%' . $search . '%')
                ->distinct()
                ->limit(10)->get();
        }
        foreach ($data as $d) {

            $temp = explode("\\", $d->subject_type);
            $d['name'] = end($temp);
        }

        return response()->json($data);
    }


}
