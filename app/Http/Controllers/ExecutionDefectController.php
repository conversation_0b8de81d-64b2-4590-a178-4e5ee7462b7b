<?php

namespace App\Http\Controllers;

use App\Models\Checking;
use App\Models\Defect;
use App\Models\Deposit;
use App\Models\ExecutionDefect;
use App\Models\Project;
use App\Models\User;
use Illuminate\Http\Request;

use App\Models\TodolistCategory;
use App\Models\Visitor;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;
use Plank\Mediable\Media;
use MediaUploader;

class ExecutionDefectController extends Controller
{
    
    public function create(Project $project)
    {
        $execution_defects = TodolistCategory::where('type', 'execution-defects')
                            ->where('status', 'active')
                            ->get();
        $execution_defect_users=User::withoutRole('customer')->get();
        if (request()->ajax()) {
            return view('projects.execution-defects.create-form', compact('execution_defects', 'project','execution_defect_users'))->render();
        }
    
        return view('projects.execution-defects.create', compact('execution_defects', 'project','execution_defect_users'));
    }

    public function store(Request $request, Project $project)
    {
        try {
            if (isset($request['execution_defect'])) {
                foreach ($request['execution_defect'] as $todolist_category_id) {
                    if ($todolist_category_id) {
                        $project_execution_defect = [
                            'project_id' => $project->id,
                            'todolist_category_id' => $todolist_category_id,
                            'execution_defect_user_id' => $request->execution_defect_user_id,
                            'execution_defect_date' => $request->execution_defect_date,
                        ];
                        ExecutionDefect::create($project_execution_defect);
                    }
                }
            }
    
            return response()->json([
                'status' => 'success',
                'message' => 'Execution defect(s) added successfully.',
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'An error occurred while saving execution defects.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function fix(Request $request, Project $project,ExecutionDefect $execution_defect )
    {
        
        $execution_defect_users=User::withoutRole('customer')->get();
        if (request()->ajax()) {
        return view('projects.execution-defects.fix-form', compact('execution_defect', 'project','execution_defect_users'))->render();
        }

        
    }
    public function fixStore(Request $request, Project $project,ExecutionDefect $execution_defect )
    {
        //
    }





}
