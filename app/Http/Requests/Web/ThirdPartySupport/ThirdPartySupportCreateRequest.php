<?php

namespace App\Http\Requests\Web\ThirdPartySupport;

use Illuminate\Foundation\Http\FormRequest;

class ThirdPartySupportCreateRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'project_id' => ['required', 'exists:projects,id'],
            'support_performer_id' => ['nullable', 'exists:users,id'],
            'title' => ['nullable'],
            'support_type' => ['nullable'],
            'price' => ['nullable'],
            'support_date' => ['nullable', 'date'],
            'support_start_date' => ['nullable', 'date'],
            'support_end_date' => ['nullable', 'date'],
            'support_comments' => ['nullable'],
            'support_completed' => ['boolean'],
            'creator_id' => ['nullable', 'integer'],
            'updater_id' => ['nullable', 'integer'],
        ];
    }
}
