<?php

namespace App\Http\Requests\Api\User;

use App\Http\Requests\Api\ApiBaseRequest;

class ModifyLastUpdateRequest extends ApiBaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            // 'last_update' => 'required|date_format:Y-m-d H:i:s',
        ];
    }


    public function messages()
    {
        return [
            'last_update.required' => 'تاریخ بروزرسانی لازم است.',
            'last_update.date_format' => 'فرمت تاریخ معتبر نیست. فرمت صحیح Y-m-d H:i:s می‌باشد.',
        ];
    }
}
