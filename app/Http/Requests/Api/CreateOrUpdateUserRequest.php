<?php

namespace App\Http\Requests\Api\User;

use App\Http\Requests\Api\ApiBaseRequest;

class CreateOrUpdateUserRequest extends ApiBaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $userId = $this->input('id');

        return [
            'id' => 'nullable|exists:users,id',
            'first_name' => 'nullable|string|max:255',
            'last_name' => 'nullable|string|max:255',
            'phone' => 'required|string|max:255|unique:users,phone,' . $userId,
            'phone2' => 'nullable|string|max:255',
            'father_name' => 'nullable|string|max:255',
            'national_code' => 'nullable|string|max:255|unique:users,national_code,' . $userId,
            'province_id' => 'nullable|integer|exists:provinces,id',
            'county_id' => 'nullable|integer|exists:counties,id',
            'city_id' => 'nullable|integer|exists:cities,id',
            'postal_code' => 'nullable|string|max:255',
            'address' => 'nullable|string',
            'bank' => 'nullable|string|max:255',
            'bank_account_number' => 'nullable|string|max:255',
            'bank_account_iban' => 'nullable|string|max:255',
            'type' => 'nullable|string|max:255',
            'otp_code' => 'nullable|string|max:255',
            'otp_code_sent' => 'nullable|date',
            'password' => 'nullable|string|min:8|confirmed',
        ];
    }
}
