<?php

namespace App\Http\Requests\Api\Project;

use App\Http\Requests\Api\ApiBaseRequest;
use Illuminate\Foundation\Http\FormRequest;

class CreateOrUpdateProjectRequest extends ApiBaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true; // or return auth()->check(); if you want to restrict to authenticated users
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'project_code' => 'required|string|max:255',
            'user_id' => 'required|exists:users,id',
            'electricity_country' => 'nullable|string|max:255',
            'electricity_affairs_code' => 'nullable|string|max:255',
            'electricity_bill_id' => 'nullable|string|max:255',
            'mehrsan_file_number' => 'nullable|string|max:255',
            'postal_code' => 'nullable|string|max:255',
            'power_plant_capacity' => 'nullable|string|max:255',
            'support_organization' => 'nullable|string|max:255',
            'province_id' => 'nullable|exists:provinces,id',
            'county_id' => 'nullable|exists:counties,id',
            'city_id' => 'nullable|exists:cities,id',
            'payment_bank' => 'nullable|string|max:255',
            'address' => 'nullable|string|max:255',
            'payment_status' => 'nullable|string|max:255',
            'remaining_deposit' => 'nullable|string|max:255',
            'paid_amount' => 'nullable|string|max:255',
            'payment_date' => 'nullable|date',
            'balance' => 'nullable|string|max:255',
            'payment_until_today' => 'nullable|string|max:255',
            'inspection_status' => 'nullable|string|max:255',
            'inspection_date' => 'nullable|date',
            'inspector' => 'nullable|string|max:255',
            'inspector_comment' => 'nullable|string|max:255',
            'design_file_result' => 'nullable|string|max:255',
            'panel_installation_date' => 'nullable|date',
            'meter_installation_date' => 'nullable|date',
            'payment_to_execution_duration' => 'nullable|string|max:255',
            'payment_to_network_connection_duration' => 'nullable|string|max:255',
            'installation_to_today_duration' => 'nullable|string|max:255',
            'installer' => 'nullable|string|max:255',
            'structure_type_image' => 'nullable|string|max:255',
            'current_phase' => 'nullable|string|max:255',
            'send_list_comments' => 'nullable|string|max:255',
            'materials' => 'nullable|string|max:255',
            'additional_costs_and_dorm_rent' => 'nullable|string|max:255',
            'cost_of_materials' => 'nullable|string|max:255',
            'panel_type_power_and_quantity' => 'nullable|string|max:255',
            'panel_serial_numbers_image' => 'nullable|string|max:255',
            'inverter_model' => 'nullable|string|max:255',
            'inverter_serial_number_image' => 'nullable|string|max:255',
            'meter_serial_number_image' => 'nullable|string|max:255',
            'sim_card_number' => 'nullable|string|max:255',
            'utm_y' => 'nullable|string|max:255',
            'utm_x' => 'nullable|string|max:255',
            'longitude' => 'nullable|string|max:255',
            'latitude' => 'nullable|string|max:255',
            'google_maps_link' => 'nullable|string|max:255',
            'gis_code' => 'nullable|string|max:255',
            'monitoring' => 'nullable|string|max:255',
            'mehrsan_status' => 'nullable|string|max:255',
            'support_image_and_comments' => 'nullable|string|max:255',
            'insurance' => 'nullable|string|max:255',
            'support_needed' => 'nullable|string|max:255',
            'type' => 'nullable|string|max:255',
            'status' => 'nullable|string|max:255',
            'creator_id' => 'required|exists:users,id',
            'updater_id' => 'required|exists:users,id',
        ];
    }
}