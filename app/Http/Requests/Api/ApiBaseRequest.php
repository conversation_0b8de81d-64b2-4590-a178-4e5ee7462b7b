<?php

namespace App\Http\Requests\Api;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;

class ApiBaseRequest extends FormRequest
{
    public function failedValidation(Validator $validator)
    {
        $errors = $validator->errors()->all();

        throw new HttpResponseException(apiResponse()
            ->withError($errors)
            ->withStatusCode(422)
            ->withMessage('مشکل در ارسال درخواست')
            ->toResponse(request()));
    }
}
