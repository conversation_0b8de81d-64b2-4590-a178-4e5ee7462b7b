<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class HideDetection
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        if (function_exists('header_remove')) {
            header_remove('X-Powered-By');
            header_remove('Server');
            header_remove('X-Frame-Options');
        } else {
            @ini_set('expose_php', 'off');
        }
        return $next($request);
    }
}
