<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class NormalizeCharacters
{
    public function handle(Request $request, Closure $next)
    {

        $normalized = [
            'آ' => 'ا',
            'أ' => 'ا',
            'إ' => 'ا',
            'ۀ' => 'ه',
            'ة' => 'ه',
            'ؤ' => 'و',
            'ئ' => 'ی',
            'ي' => 'ی',
            'ك' => 'ک', // Arabic Kaf to Persian Kaf
            'ي' => 'ی', // Arabic Yeh to Persian Yeh
            'ة' => 'ه', // Arabic Ta Marbuta to Heh
            'ى' => 'ی', // Arabic Alef Maqsura to Yeh
            '۱' => '1', // Persian digit 1 to Latin digit 1
            '۲' => '2', // Persian digit 2 to Latin digit 2
            '۳' => '3', // Persian digit 3 to Latin digit 3
            '۴' => '4', // Persian digit 4 to Latin digit 4
            '۵' => '5', // Persian digit 5 to Latin digit 5
            '۶' => '6', // Persian digit 6 to Latin digit 6
            '۷' => '7', // Persian digit 7 to Latin digit 7
            '۸' => '8', // Persian digit 8 to Latin digit 8
            '۹' => '9', // Persian digit 9 to Latin digit 9
            '۰' => '0', // Persian digit 0 to Latin digit 0
        ];

        $request->merge(
            array_map(
                fn($value) => is_string($value) ? strtr($value, $normalized) : $value,
                $request->all()
            )
        );

        return $next($request);
    }
}
