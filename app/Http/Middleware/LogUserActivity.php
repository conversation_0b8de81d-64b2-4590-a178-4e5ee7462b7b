<?php

namespace App\Http\Middleware;

use App\Facades\WebcoSetting;
use App\Traits\LogsTrait;
use Closure;

class LogUserActivity
{
    use LogsTrait;
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle($request, Closure $next)
    {
        $user = getCurrentUser();
        $routeName = $request->route()->getName();
        $notRouteLogMenu = [
            'customer.panel.index', 
        ];
        $notRouteStartWith=[
           'logs'
        ];

        if (!in_array($routeName, $notRouteLogMenu) && !$this->starts_with_any($routeName, $notRouteStartWith)) {
            $this->logsUserActivity(config('activitylog.name_logs.view'), config('activitylog.event_logs.other'), $user, $user);
        }


        return $next($request);
    }
    private function starts_with_any($string, $values)
    {
        foreach ($values as $value) {
            if (str_starts_with($string, $value)) {
                return true;
            }
        }

        return false;
    }
}
