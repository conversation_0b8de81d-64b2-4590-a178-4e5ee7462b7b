<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class Checking extends BaseModel
{
    use HasFactory, SoftDeletes,LogsActivity;

    protected $fillable = [
        'project_id',
        'todolist_category_id',
        'creator_id',
        'updater_id',
    ];
    public function getActivitylogOptions(): LogOptions
    {
        try {
            $deviceInfo = getInfoDevice();
        } catch (\Throwable $th) {
            $deviceInfo = 'undefined';
        }
        return LogOptions::defaults()
            ->logAll()
            ->useLogName('model')
            ->logExcept([]) //'password'
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs()
            ->setDescriptionForEvent(fn (string $eventName) => json_encode($deviceInfo));
    }

    public function project()
    {
        return $this->belongsTo(Project::class, 'project_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'creator_id');
    }

    public function todolistCategory()
    {
        return $this->belongsTo(TodolistCategory::class, 'todolist_category_id');
    }
}
