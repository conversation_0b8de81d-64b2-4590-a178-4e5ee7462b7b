<?php

namespace App\Models;

use App\Observers\ExecutionDefectObserver;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

#[ObservedBy([ExecutionDefectObserver::class])]
class ExecutionDefect extends BaseModel
{
    use HasFactory, SoftDeletes,LogsActivity;

    protected $fillable = [
        'project_id',
        'todolist_category_id',
        'execution_defect_date',
        'execution_defect_user_id',
        'fix_execution_defect_date',
        'fix_execution_defect_user_id',
        'creator_id',
        'updater_id',
    ];
    protected $dates = [
        'execution_defect_date',
        'fix_execution_defect_date',
    ];
    public function getActivitylogOptions(): LogOptions
    {
        try {
            $deviceInfo = getInfoDevice();
        } catch (\Throwable $th) {
            $deviceInfo = 'undefined';
        }
        return LogOptions::defaults()
            ->logAll()
            ->useLogName('model')
            ->logExcept([]) //'password'
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs()
            ->setDescriptionForEvent(fn (string $eventName) => json_encode($deviceInfo));
    }

    public function project()
    {
        return $this->belongsTo(Project::class, 'project_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'creator_id');
    }

    /**
     * Get the todolist category that the defect belongs to.
     */
    public function todolistCategory()
    {
        return $this->belongsTo(TodolistCategory::class, 'todolist_category_id');
    }
    public function defectUser()
    {
        return $this->belongsTo(User::class, 'execution_defect_user_id');
    }
    public function fixDefectUser()
    {
        return $this->belongsTo(User::class, 'fix_execution_defect_user_id');
    }
}
