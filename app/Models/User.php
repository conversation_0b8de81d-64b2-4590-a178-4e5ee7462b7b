<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Spatie\Permission\Traits\HasRoles;
use Tymon\JWTAuth\Contracts\JWTSubject;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Permission\Traits\HasPermissions;

class User extends Authenticatable implements JWTSubject
{
    use HasFactory, Notifiable, HasRoles,HasPermissions, SoftDeletes , LogsActivity;

    protected $guarded = [];
    protected $appends = ['last_user_update','last_project_update','last_todo_update','last_media_update'];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->creator_id = getCurrentUser()->id ?? null;
            $model->updater_id = getCurrentUser()->id ?? null;
            $model->birth_date = convertPersianDateToGregorian($model->birth_date, 0);
        });

        static::updating(function ($model) {
            $model->updater_id = getCurrentUser()->id ?? null;
            if (($model->isDirty('birth_date')))
            {
                $model->birth_date = convertPersianDateToGregorian($model->birth_date, 0);
            }

        });
        static::saving(function ($model) {
             // Normalize all string fields before saving
            foreach ($model->getAttributes() as $key => $value) {
                if (is_string($value)) {
                    $model->$key = normalizeRequestCharacter($value,true);
                }
            }
            $model->sync_update = (string) now()->getPreciseTimestamp(6); // Store microseconds as string
        });
    }



    public function getBirthDateAttribute($value)
    {
        return convertDate($value);
    }

    public function getActivitylogOptions(): LogOptions
    {
        try {
            $deviceInfo = getInfoDevice();
        } catch (\Throwable $th) {
            $deviceInfo = 'undefined';
        }
        return LogOptions::defaults()
            ->logAll()
            ->useLogName('model')
            ->logExcept([]) //'password'
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs()
            ->setDescriptionForEvent(fn (string $eventName) => json_encode($deviceInfo));
    }

    public function getJWTIdentifier()
    {
        return $this->getKey();
    }

    public function getJWTCustomClaims(): array
    {
        return [];
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    // protected $fillable = [
    //     'first_name',
    //     'last_name',
    //     'phone',
    //     'phone2',
    //     'father_name',
    //     'national_code',
    //     'bank',
    //     'bank_account_number',
    //     'bank_account_iban',
    //     'email',
    //     'type',
    //     'password',
    //     'last_project_update',
    //     'last_user_update'
    // ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'otp_code_sent' => 'datetime',
            'password' => 'hashed',
        ];
    }

    public function getNameAttribute()
    {
        return ($this->first_name ?? '') . ' ' . ($this->last_name ?? '');
    }

    public function userUpdate()
    {
        return $this->hasOne(UserUpdate::class);
    }
    public function getLastUserUpdateAttribute()
    {
        return $this->userUpdate ? $this->userUpdate->last_user_update : null;
    }
    public function getLastProjectUpdateAttribute()
    {
        return $this->userUpdate ? $this->userUpdate->last_project_update : null;
    }
    public function getLastTodoUpdateAttribute()
    {
        return $this->userUpdate ? $this->userUpdate->last_todo_update : null;
    }
    public function getLastMediaUpdateAttribute()
    {
        return $this->userUpdate ? $this->userUpdate->last_media_update : null;
    }


    public function projects()
    {
        return $this->hasMany(Project::class);
    }

    public function deposits()
    {
        return $this->hasMany(Deposit::class);
    }

    // Validation method
    public static function validateUser($data, $userId = null)
    {
        $rules = [
            'national_code' => [
                'nullable',
                'string',
                'digits:10',
                Rule::unique('users', 'national_code')->ignore($userId),
            ],
            'phone' => [
                $userId ? 'nullable' : 'required',
                'string',
                'regex:/^09\d{9}$/', // Ensure the phone starts with 09 and is 11 digits long
                'size:11',
                Rule::unique('users', 'phone')->ignore($userId),
            ],
        ];

        return Validator::make($data, $rules);
    }

    public function getPersianUserTypeAttribute()
    {
        $data = [
            'personal' => 'حقیقی',
            'legal' => 'حقوقی',
        ];

        return $data[$this->type] ?? 'نامشخص';
    }

    public function getWebGuardRoleNames()
    {
        return $this->roles->filter(function ($role) {
            return $role->guard_name === 'web';
        })->pluck('name');
    }

    public function city()
    {
        return $this->belongsTo(IranCity::class, 'city_id');
    }

    public function village()
    {
        return $this->belongsTo(Village::class, 'village_id');
    }

    public function bank()
    {
        return $this->belongsTo(Bank::class, 'bank_id');
    }

     // Relationship for support organizations
     public function supportOrganizations()
     {
         return $this->hasMany(UserAccessSupportOrganization::class);
     }

     // Relationship for accessed provinces
     public function accessedProvinces()
     {
         return $this->hasMany(UserAccessProvince::class);
     }


}
