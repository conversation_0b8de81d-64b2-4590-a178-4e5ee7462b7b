<?php

namespace App\Models;

use App\Observers\TodolistCategoryObserver;
use GeneaLabs\LaravelModelCaching\Traits\Cachable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

#[ObservedBy([TodolistCategoryObserver::class])]
class TodolistCategory extends BaseModel
{
    use HasFactory, SoftDeletes,LogsActivity,Cachable;

    protected $fillable = [
        'parent_id',
        'title',
        'slug',
        'type',
        'status',
        'creator_id',
        'updater_id'
    ];
    public function getActivitylogOptions(): LogOptions
    {
        
        try {
            $deviceInfo = getInfoDevice();
        } catch (\Throwable $th) {
            $deviceInfo = 'undefined';
        }
        return LogOptions::defaults()
            ->logAll()
            ->useLogName('model')
            ->logExcept([]) //'password'
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs()
            ->setDescriptionForEvent(fn (string $eventName) => json_encode($deviceInfo));
    }

    public function parent()
    {
        return $this->belongsTo(TodolistCategory::class, 'parent_id');
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'creator_id');
    }
    public function updater()
    {
        return $this->belongsTo(User::class, 'updater_id');
    }
}
