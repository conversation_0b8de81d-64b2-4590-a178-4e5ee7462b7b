<?php

namespace App\Models;

use App\Traits\CustomMediable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Plank\Mediable\Mediable;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class SerialPanel extends BaseModel
{
    use HasFactory, SoftDeletes, Mediable,LogsActivity, CustomMediable {
        CustomMediable::detachMediaTags insteadof Mediable;
        CustomMediable::attachMedia insteadof Mediable;
        CustomMediable::media insteadof Mediable;
    }
    public function getActivitylogOptions(): LogOptions
    {
        
        try {
            $deviceInfo = getInfoDevice();
        } catch (\Throwable $th) {
            $deviceInfo = 'undefined';
        }
        return LogOptions::defaults()
            ->logAll()
            ->useLogName('model')
            ->logExcept([]) //'password'
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs()
            ->setDescriptionForEvent(fn (string $eventName) => json_encode($deviceInfo));
    }

    protected $fillable = [
        'project_id',
        'serial',
        'description',
        'creator_id',
        'updater_id',
    ];

    protected $dates = ['deleted_at'];

    public function project()
    {
        return $this->belongsTo(Project::class, 'project_id');
    }

    public function medias()
    {
        return $this->getMedia('description');
    }
}
