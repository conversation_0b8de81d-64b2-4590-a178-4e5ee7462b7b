<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class AdminMenu extends Model
{

    protected $fillable = [
        'id',
        'persian',
        'english',
        'route',
        'permission',
        'roles',
        'priority',
        'type',
        'parent_id'
    ];

    public function parent(): BelongsTo
    {
        return $this->belongsTo(AdminMenu::class, 'parent_id', 'id');
    }

    public function children(): HasMany
    {
        return $this->hasMany(AdminMenu::class, 'parent_id', 'id');
    }
}
