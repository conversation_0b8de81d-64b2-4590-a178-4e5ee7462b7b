<?php

namespace App\Models;

use App\Observers\ProjectSupportObserver;
use App\Traits\CustomMediable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Plank\Mediable\Mediable;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

#[ObservedBy([ProjectSupportObserver::class])]
class ProjectSupport extends BaseModel
{
    use HasFactory, SoftDeletes, Mediable,LogsActivity, CustomMediable {
        CustomMediable::detachMediaTags insteadof Mediable;
        CustomMediable::attachMedia insteadof Mediable;
        CustomMediable::media insteadof Mediable;
    }
    public function getActivitylogOptions(): LogOptions
    {
        
        try {
            $deviceInfo = getInfoDevice();
        } catch (\Throwable $th) {
            $deviceInfo = 'undefined';
        }
        return LogOptions::defaults()
            ->logAll()
            ->useLogName('model')
            ->logExcept([]) //'password'
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs()
            ->setDescriptionForEvent(fn (string $eventName) => json_encode($deviceInfo));
    }

    protected $fillable = [
        'project_id',
        'status',
        'title',
        'start_date',
        'end_date',
        'supporter_id',
        'supporter_comment',
        'creator_id',
        'creator_comment',
        'updater_id',
        'support_implementation_duration',
    ];

    protected $dates = [
        'start_date',
        'end_date',
    ];

    public function supporter()
    {
        return $this->belongsTo(User::class, 'supporter_id');
    }

    public function project()
    {
        return $this->belongsTo(Project::class, 'project_id');
    }

    public function creatorMedias()
    {
        return $this->getMedia('creator_comment');
    }

    public function supporterMedias()
    {
        return $this->getMedia('supporter_comment');
    }
}
