<?php

namespace App\Models;

use App\Observers\ProjectObserver;
use App\Traits\CustomMediable;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Verta\Verta;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Plank\Mediable\Mediable;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

#[ObservedBy([ProjectObserver::class])]
class Project extends BaseModel
{
    use HasFactory, SoftDeletes, Mediable,LogsActivity, CustomMediable {
        CustomMediable::detachMediaTags insteadof Mediable;
        CustomMediable::attachMedia insteadof Mediable;
        CustomMediable::media insteadof Mediable;
    }

    protected $fillable = [
        'project_code',
        'user_id',
        'title',
        'electricity_county_id',
        'electricity_affairs_code',
        'electricity_bill_id',
        'mehrsan_file_number',
        'loan_received_date',
        'postal_code',
        'power_plant_capacity',
        'support_organization',
        'province_id',
        'county_id',
        'city_id',
        'payment_bank',
        'address',
        'payment_status',
        // 'remaining_deposit',
        // 'paid_amount',
        // 'payment_date',
        'balance',
        'payment_until_today',
        'inspection_status',
        'inspection_date',
        'inspector',
        'inspector_comment',
        'design_file_result',
        'panel_installation_date',
        'meter_installation_date',
        'payment_to_execution_duration',
        'payment_to_network_connection_duration',
        'installation_to_today_duration',
        'installer_id',
        'structure_type_image',
        'current_phase',
        'send_list_comments',
        'materials',
        'additional_costs_and_dorm_rent',
        'inverter_model',
        'inverter_serial_number_image',
        'meter_serial_number_image',
        'sim_card_number',
        'utm_y',
        'utm_x',
        'longitude',
        'latitude',
        'google_maps_link',
        'gis_code',
        'network_connection_comment',
        'has_monitoring',
        'monitoring_code',
        'mehrsan_status',
        'insurance',
        'support_needed',
        'creator_id',
        'updater_id',
        'inspector_id',
        'installer_id',
        'designer_id',
        'defect_comment',
        'checking_comment',
        'panel_to_meter_duration', //TODO : handle to calculate this
        'panel_power',
        'panel_type',
        'panel_quantity',
        'inverter_to_electrical_base_distance',
        'cost_of_materials',
        'ware_postage_date',
        'ware_sender_id',
        'postage_date',
        'simban_id',
        'monitoring_installation_date',
        'monitoring_installer_id',
        'inverter_to_the_power_base_cable_used',
        'material_cost_payment_date',
        'additional_costs_and_dorm_rent_payment_date',
        'execution_defect_comment',
        'project_support_comment',
        'type',
        'status',
        'discount',
        'accounting_status',

        'earth_resistance',
        'measurement_date',
        'execution_type',
        'announcement_sanam',
        'meter_assignment',
        'village_id',
    ];

    protected static function boot()
    {
        parent::boot();


    }
    public function getActivitylogOptions(): LogOptions
    {
        try {
            $deviceInfo = getInfoDevice();
        } catch (\Throwable $th) {
            $deviceInfo = 'undefined';
        }
        return LogOptions::defaults()
            ->logAll()
            ->useLogName('model')
            ->logExcept([]) //'password'
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs()
            ->setDescriptionForEvent(fn (string $eventName) => json_encode($deviceInfo));
    }

    public function getPersianSupportOrganizationAttribute()
    {
        $supportOrganizations = [
            '01' => 'کمیته امداد امام خمینی',
            '02' => 'بسیج سازندگی',
            '03' => 'بهزیستی',
            '04' => 'معاونت توسعه روستایی استانداری',
            '05' => 'خصوصی',
            '06' => 'مجتمع اقتصادی',
            '07' => 'بانک رسالت',
            '08' => 'بنیاد علوی',
            '09' => 'بنیاد برکت',
            '10' => 'سازمان اوقاف و امور خیریه',
            '11' => 'حوزه علمیه',
            '12' => 'شرکت شهرک های صنعتی ایران',
            '13' => 'سازمان حفاظت محیط زیست',
            '14' => 'خیرین مدرسه ساز',
            '15' => 'سایر',
        ];

        return $supportOrganizations[$this->support_organization] ?? 'نامشخص';
    }

    public function getPersianPaymentStatusAttribute()
    {
        $data = [
            'deposited' => 'واریز شده',
            'not-deposited' => 'واریز نشده ',
            'deposited-but-has-balance' => 'واریز شده ولی مانده دارد',
            'deposited-more'=>'بیشتر واریز شده',
        ];

        return $data[$this->payment_status] ?? 'نامشخص';
    }

    public function getPersianInspectionStatusAttribute()
    {
        $data = [
            'visited' => 'بازدید شده',
            'not-visited' => 'بازدید نشده ',
        ];

        return $data[$this->inspection_status] ?? 'نامشخص';
    }


    public function getPersianStructureTypeAttribute()
    {
        $data = [
            'one-floor' => 'یک طبقه',
            'two-floors' => 'دو طبقه',
            'Shading' => 'سایه بانی',
            'two-floors-land' => 'زمینی دو طبقه',
            'one-floor-land' => 'زمینی یک طبقه',
        ];

        return $data[$this->structure_type_image] ?? 'نامشخص';
    }

    public function getPersianDesignFileResultAttribute()
    {
        $data = [
            'one-floor' => 'یک طبقه',
            'two-floors' => 'دو طبقه',
            'Shading' => 'سایه بانی',
            'two-floors-land' => 'زمینی دو طبقه',
            'one-floor-land' => 'زمینی یک طبقه',
        ];

        return $data[$this->design_file_result] ?? 'نامشخص';
    }

    public function getPersianCurrentPhaseAttribute()
    {
        $data = [
            'not-visited' => 'بازدید نشده',
            'infeasible' => 'غیرقابل اجرا',
            'visited-ready-to-design' => '(بازدید کننده)بازدید شده آماده طراحی',
            'visited-ready-to-implementation-program' => '(طراح)بازدید شده آماده قرارگیری در برنامه اجرا',
            'designed-ready-to-send-commodity' => '(تیم برنامه ریزی) در برنامه اجرا آماده ارسال جنس',
            'commodity-sent-ready-to-implementation' => '(واحد انبار داری)جنس ارسال شده آماده اجرا',
            'executed-but-have-defect' => '(تیم اجرا)اجرا شده ولی دارای نقص ',
            'run-ready-to-connect-to-the-network' => '(تیم اجرا)اجرا شده آماده اتصال به شبکه',
            'connected-to-the-network' => '(سیم بان)متصل شده به شبکه',
        ];

        

        return $data[$this->current_phase] ?? 'نامشخص';
    }

    public function getPersianPowerPlantCapacityAttribute()
    {

        $data = [
            
            '5' => '5 کیلو وات',
            '10' =>'10 کیلو وات',    
            '15' => '15 کیلو وات',
            '20'=> '20 کیلو وات',
            '40' => '40 کیلو وات',
            '100' => '100 کیلو وات',
              
        ];

        return $data[$this->power_plant_capacity] ?? 'نامشخص';

    }
    public function getPersianAccountingStatusAttribute()
    {

        $data = [
            
            'not-approved' => 'عدم تایید',
            'approved' => 'تایید حسابدار',
              
        ];

        return $data[$this->accounting_status] ?? 'نامشخص';

    }

    public function getPersianMehrsanAttribute()
    {
        $data = [
            'plant-registration-by-customer' => 'ثبت نیروگاه توسط مشترک',
            'inspection-schedule-by-supervisor' => 'تعیین زمان بازدید توسط ناظر',
            'inspection-form-completion-by-supervisor' => 'تکمیل فرم بازدید توسط ناظر',
            'document-upload-by-customer' => 'بارگذاری مدارک توسط مشترک',
            'document-verification-by-supervisor' => 'صحت سنجی و تایید مدارک توسط ناظر',
            'contract-signing-with-customer' => 'عقد قرارداد شرکت برق با مشترک',
            'contractor-selection-by-customer' => 'انتخاب پیمانکار توسط مشترک',
            'contractor-info-verification-by-supervisor' => 'بررسی و صحت سنجی اطلاعات پیمانکار توسط ناظر',
            'contractor-profile-creation' => 'ساخت کارتابل پیمانکار',
            'contractor-contract-signing-with-customer' => 'عقد قرارداد پیمانکار با مشترک',
            'contract-verification-by-supervisor' => 'صحت سنجی قرارداد توسط ناظر',
            'project-scheduling-by-contractor' => 'ثبت زمانبندی اجرای نیروگاه توسط پیمانکار',
            'plant-design-by-contractor' => 'طراحی نیروگاه توسط پیمانکار',
            'design-review-by-supervisor' => 'بررسی طراحی توسط ناظر',
            'plant-construction-by-contractor' => 'احداث نیروگاه توسط پیمانکار',
            'construction-review-by-supervisor' => 'بررسی احداث توسط ناظر',
            'plant-testing-by-contractor' => 'تست نیروگاه توسط پیمانکار',
            'plant-testing-by-supervisor' => 'تست نیروگاه توسط ناظر',
            'plant-connection-by-supervisor' => 'اتصال نیروگاه به شرکت برق توسط ناظر',
            'operation-license-issuance' => 'صدور پروانه بهره برداری',
            'completion' => 'پایان',
        ];
        return $data[$this->mehrsan_status] ?? 'نامشخص';
    }
    

    public function getPersianInsuranceAttribute()
    {
        $data = [
            'have' => 'دارد',
            'not-have' => 'ندارد',

        ];
        return $data[$this->insurance] ?? 'نامشخص';
    }
    public function getPersianMeterAssignmentAttribute()
    {
        $data = [
            'have' => 'شده',
            'not-have' => 'نشده',

        ];
        return $data[$this->meter_assignment] ?? 'نامشخص';
    }
    public function getPersianAnnouncementSanamAttribute()
    {
        $data = [
            'have' => 'شده',
            'not-have' => 'نشده',


        ];
        return $data[$this->announcement_sanam] ?? 'نامشخص';
    }

    public function getPersianSupportNeededAttribute()
    {
        $data = [
            'have' => 'دارد',
            'not-have' => 'ندارد',
        ];
        return $data[$this->support_needed] ?? 'نامشخص';
    }

    public function getPersianTypeAttribute()
    {
        $data = [
            'support' => 'پشتیبانی',
            'full' => 'کامل',
        ];
        return $data[$this->type] ?? 'نامشخص';
    }

    public function getPersianStatusAttribute()
    {
        $data = [
            'active' => 'فعال',
            'inactive' => 'غیرفعال',
            'pending' => 'درانتظار',
            'completed' => 'تکمیل شده',
            'under-review' => 'دردست بررسی',
            'other' => 'دیگر',
        ];
        return $data[$this->status] ?? 'نامشخص';
    }

    public function attributesToArrayWithColumns(array $columns)
    {
        $attributes = $this->attributesToArray();

        // Filter the attributes based on the specified columns
        $filteredAttributes = [];
        foreach ($columns as $key => $label) {
            // Check if the attribute key exists in the attributes array
            if (array_key_exists($key, $attributes)) {
                $filteredAttributes[$label] = $attributes[$key];
            }
        }

        return $filteredAttributes;
    }


    public function owner()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'creator_id');
    }

    public function designer()
    {
        return $this->belongsTo(User::class, 'designer_id');
    }

    public function inspector()
    {
        return $this->belongsTo(User::class, 'inspector_id');
    }

    public function simban()
    {
        return $this->belongsTo(User::class, 'simban_id');
    }

    public function updater()
    {
        return $this->belongsTo(User::class, 'updater_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function serialPanels()
    {
        return $this->hasMany(SerialPanel::class, 'project_id');
    }

    public function support()
    {
        return $this->hasMany(ProjectSupport::class, 'project_id');
    }

    public function deposit($type = "")
    {
        $query = $this->hasMany(Deposit::class, 'project_id');
    
        if ($type === "withdraw") {
            return $query->where('type', 'withdraw');
        } elseif ($type === "deposit") {
            return $query->where('type', 'deposit');
        }
    
        return $query; // No filtering if $type is empty or not recognized
    }
    
   

    public function checking()
    {
        return $this->hasMany(Checking::class, 'project_id');
    }

    public function defect()
    {
        return $this->hasMany(Defect::class, 'project_id');
    }

    public function executionDefect()
    {
        return $this->hasMany(ExecutionDefect::class, 'project_id');
    }

    public function thirdParty()
    {
        return $this->hasMany(ThirdPartySupport::class, 'project_id');
    }

    public function visitor()
    {
        return $this->hasMany(Visitor::class, 'project_id');
    }


    public function province()
    {
        return $this->belongsTo(IranProvince::class, 'province_id');
    }

    public function country()
    {
        return $this->belongsTo(IranCounty::class, 'county_id');
    }

    public function city()
    {
        return $this->belongsTo(IranCity::class, 'city_id');
    }

    public function electricCity()
    {
        return $this->belongsTo(IranCounty::class, 'electricity_county_id');
    }

    public function installer()
    {
        return $this->belongsTo(User::class, 'installer_id');
    }

    public function wareSender()
    {
        return $this->belongsTo(User::class, 'ware_sender_id');
    }

    public function todolistDefect()
    {
        return $this->belongsToMany(TodolistCategory::class, 'defects', 'project_id', 'todolist_category_id')
            ->withPivot('user_id', 'creator_id', 'updater_id')
            ->withTimestamps();
    }

    public function todolistChecking()
    {
        return $this->belongsToMany(TodolistCategory::class, 'checkings', 'project_id', 'todolist_category_id')
            ->withPivot('user_id', 'creator_id', 'updater_id')
            ->withTimestamps();
    }

    public function medias()
    {
        return $this->getMedia('send_list_comments');
    }

    public function designMedias()
    {
        return $this->getMedia('design_file_result');
    }
    
    public function materialCostMedias()
    {
        return $this->getMedia('cost_of_materials');
    }

    public function additionalCostMedias()
    {
        return $this->getMedia('additional_costs_and_dorm_rent');
    }
    

    
    public function checkingMedias()
    {
        return $this->getMedia('checking_comment');
    }

    public function defectMedias()
    {
        return $this->getMedia('defect_comment');
    }

    public function executionDefectMedias()
    {
        return $this->getMedia('execution_defect_comment');
    }

    public function inverterMedias()
    {
        return $this->getMedia('inverter_serial_number_image');
    }

    public function monitoringMedias()
    {
        return $this->getMedia('monitoring_code');
    }

    public function meterMedias()
    {
        return $this->getMedia('meter_serial_number_image');
    }

    public function structureMedias()
    {
        return $this->getMedia('structure_type_image');
    }

    protected static function generateCodeProject($provinceId, $supportOrganization, $countyId)
    {
        // Get current year in Persian date
        $now = Verta::now();
        $year = $now->format('y');

        // Format province ID and ensure it's 2 digits
        $formattedProvinceId = str_pad($provinceId, 2, '0', STR_PAD_LEFT);

        // Ensure support organization is 2 digits
        $formattedSupportOrganization = str_pad($supportOrganization, 2, '0', STR_PAD_LEFT);

        // Format county ID and ensure it's 3 digits
        $formattedCountyId = str_pad($countyId, 3, '0', STR_PAD_LEFT);

        // Pattern to find the latest project code
        $pattern = '^' . $year . $formattedProvinceId . $formattedSupportOrganization . $formattedCountyId . '\d{4}$';

        // Search for the last project code that matches the pattern
        $latestProjectCode = self::withTrashed()
            ->where('project_code', 'regexp', $pattern)
            ->max('project_code');


        // If no project is found, initialize the next four digits as '0001'
        if (!$latestProjectCode) {
            $nextFourDigits = '0001';
        } else {
            // Extract the last 4 digits from the latest project code
            $lastFourDigits = substr($latestProjectCode, -4);

            // Increment the last 4 digits by 1
            $nextFourDigits = str_pad(intval($lastFourDigits) + 1, 4, '0', STR_PAD_LEFT);
        }

        if (config('sun.seeding')) {
            $lastFourDigits = random_int(0, 9999) * now()->timestamp;
            $nextFourDigits = str_pad(intval($lastFourDigits) + 1, 4, '0', STR_PAD_LEFT);
        }

        // Concatenate all parts to form the project code
        $projectCode = $year . $formattedProvinceId . $formattedSupportOrganization . $formattedCountyId . $nextFourDigits;

        return $projectCode;
    }

    public function set_remaining_deposit()
    {
        $paid_amount = $this->deposit("deposit")->sum('amount');
        $this->remaining_deposit = $this->balance - $paid_amount;

        $min_payment_date = $this->deposit("deposit")->min('payment_date');
        $this->payment_date = $min_payment_date;
        
        return $this->remaining_deposit;
    }

    // public function set_inspection_details() // TODO: move it to observer
    // {
    //     $latestVisitor = $this->visitor()->orderBy('visit_date', 'desc')->first();

    //     if ($latestVisitor) {
    //         $this->inspection_status = $latestVisitor->status;
    //         $this->inspection_date = $latestVisitor->visit_date;
    //         $this->inspector_id = $latestVisitor->user_id;
    //         // $this->save();
    //     }
    // }

    public function village()
    {
        return $this->belongsTo(Village::class, 'village_id');
    }


}
