<?php

namespace App\Models;

use GeneaLabs\LaravelModelCaching\Traits\Cachable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserAccessSupportOrganization extends Model
{
    use HasFactory,Cachable;

    protected $fillable = ['user_id', 'support_organization_code'];

    // Define the relationship to the User
    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
