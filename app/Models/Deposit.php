<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Observers\DepositObserver;
use App\Traits\CustomMediable;
use Plank\Mediable\Mediable;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

#[ObservedBy([DepositObserver::class])]
class Deposit extends BaseModel
{
    use HasFactory, SoftDeletes,Mediable,LogsActivity, CustomMediable {
        CustomMediable::detachMediaTags insteadof Mediable;
        CustomMediable::attachMedia insteadof Mediable;
        CustomMediable::media insteadof Mediable;
    }


    protected $fillable = [
        'project_id',
        'amount',
        'payment_date',
        'user_id',
        'creator_id',
        'updater_id',
        'bank_id',
        'type',
        'info_deposit',
    ];

    protected $dates = [
        'payment_date',
    ];

    public function getActivitylogOptions(): LogOptions
    {
        try {
            $deviceInfo = getInfoDevice();
        } catch (\Throwable $th) {
            $deviceInfo = 'undefined';
        }
        return LogOptions::defaults()
            ->logAll()
            ->useLogName('model')
            ->logExcept([]) //'password'
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs()
            ->setDescriptionForEvent(fn (string $eventName) => json_encode($deviceInfo));
    }

    public function project()
    {
        return $this->belongsTo(Project::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'creator_id');
    }

    public function updater()
    {
        return $this->belongsTo(User::class, 'updater_id');
    }

    public function bankId()
    {
        return $this->belongsTo(Bank::class, 'bank_id');
    }

    public function medias()
    {
        return $this->getMedia('payment_date');
    }
    public function getPersianTypeAttribute()
    {
        $data = [
            'deposit' => 'واریز',
            'withdraw' => 'برگشت از فروش',
        ];

        return $data[$this->type] ?? 'نامشخص';
    }

}
