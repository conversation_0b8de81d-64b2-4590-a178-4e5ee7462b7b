<?php

namespace App\Models;

use GeneaLabs\LaravelModelCaching\Traits\Cachable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserAccessProvince extends Model
{
    use HasFactory,Cachable;

    protected $fillable = ['user_id', 'province_id'];

    // Define the relationship to the User
    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
