<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserUpdate extends Model
{
    use HasFactory;
    protected static function boot()
    {
        parent::boot();

        static::saving(function ($model) {
            // Format the current time in microseconds (since nanoseconds are not directly available)
            $model->sync_update = (string) now()->getPreciseTimestamp(6); // Store microseconds as string
        });
    }

    protected $fillable = [
        'user_id',
        'last_project_update',
        'last_user_update',
        'last_todo_update',
        'last_media_update',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
