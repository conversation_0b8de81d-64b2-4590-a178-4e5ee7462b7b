<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class ThirdPartySupport extends BaseModel
{
    use HasFactory, SoftDeletes,LogsActivity;

    protected $fillable = [
        'project_id',
        'title',
        'support_type',
        'price',
        'support_date',
        'support_start_date',
        'support_end_date',
        'support_comments',
        'support_completed',
        'support_performer_id',
        'creator_id',
        'updater_id',
    ];

    protected $dates = [
        'support_date',
        'support_start_date',
        'support_end_date',
    ];
    public function getActivitylogOptions(): LogOptions
    {
        
        try {
            $deviceInfo = getInfoDevice();
        } catch (\Throwable $th) {
            $deviceInfo = 'undefined';
        }
        return LogOptions::defaults()
            ->logAll()
            ->useLogName('model')
            ->logExcept([]) //'password'
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs()
            ->setDescriptionForEvent(fn (string $eventName) => json_encode($deviceInfo));
    }

    public function getPersianSupportTypeAttribute()
    {
        $data = [
            'contract' => 'قراردادی',
            'monthly' => 'ماهانه',
            'yearly' => 'سالانه',
        ];
        return $data[$this->support_type] ?? 'نامشخص';
    }

    public function project()
    {
        return $this->belongsTo(Project::class, 'project_id');
    }
}
