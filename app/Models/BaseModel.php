<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;


class BaseModel extends Model
{

    protected static function boot()
    {
        parent::boot();
        if (config('sun.seeding') == true)
            return ;
        static::creating(function ($model) {

            _logger([
                'base model creating event : ', 
                getCurrentUser()['id'], 
                get_class($model), 
                'getFillable' => $model->getFillable(),
                'getAttributes' => $model->getAttributes(),
                'attributes' => $model->attributes,
                array_key_exists('creator_id', $model->attributes), 
                $model->isFillable('creator_id'),
                $model->hasAttribute('creator_id'),
                array_key_exists('updater_id', $model->attributes), 
                $model->isFillable('updater_id'),
                $model->hasAttribute('updater_id')
            ]);

            if ($model->isFillable('creator_id')) {
                $model->creator_id = getCurrentUser()['id'] ?? null;
                // _logger(['the model has creator_id : ', $model]);
            }
            if ($model->isFillable('updater_id')) {
               
                $model->updater_id = getCurrentUser()['id']?? null;
                // _logger(['the model has updater_id : ', $model]);
            }
            if ($model->isFillable('project_id') && $model->project_id) {
                $project = Project::find($model->project_id);
                
                if ($project) {
                    $project->touch();
                } else {
                    // Handle the case where the project is not found
                    $user=getCurrentUser()['id'] ?? null;
                    info("Project with ID {$model->project_id} not found. {$user}");
                }
            }
        });

        static::updating(function ($model) {
            // _logger(['base model updating event : ', getCurrentUser()['id'] , $model]);
            if ($model->isFillable('updater_id')) {
                
                $model->updater_id = getCurrentUser()['id'] ?? null;
                // _logger(['the model has updater_id : ', $model]);
            }
            if ($model->isFillable('project_id')) {
                $project = Project::find($model->project_id);
                if ($project) {
                    $project->touch();
                } else {
                    // Handle the case where the project is not found
                    $user=getCurrentUser()['id'] ?? null;
                    info("Project with ID {$model->project_id} not found. {$user}");
                }
            }
        });  
        
        static::deleted(function ($model){
            if ($model->isFillable('project_id')) {
                $project = Project::find($model->project_id);
                if ($project) {
                    $project->touch();
                } else {
                    // Handle the case where the project is not found
                    $user=getCurrentUser()['id'] ?? null;
                    info("Project with ID {$model->project_id} not found. {$user}");
                }
            }
        });
        static::saving(function ($model) {
            $model->sync_update = (string) now()->getPreciseTimestamp(6); // Store microseconds as string
        });
    }
   
}
