<?php

namespace App\Models;

use App\Observers\DefectObserver;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

#[ObservedBy([DefectObserver::class])]
class Defect extends BaseModel
{
    use HasFactory, SoftDeletes,LogsActivity;

    protected $fillable = [
        'project_id',
        'todolist_category_id',
        'creator_id',
        'updater_id',
    ];
    public function getActivitylogOptions(): LogOptions
    {
        try {
            $deviceInfo = getInfoDevice();
        } catch (\Throwable $th) {
            $deviceInfo = 'undefined';
        }
        return LogOptions::defaults()
            ->logAll()
            ->useLogName('model')
            ->logExcept([]) //'password'
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs()
            ->setDescriptionForEvent(fn (string $eventName) => json_encode($deviceInfo));
    }

    public function project()
    {
        return $this->belongsTo(Project::class, 'project_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'creator_id');
    }

    /**
     * Get the todolist category that the defect belongs to.
     */
    public function todolistCategory()
    {
        return $this->belongsTo(TodolistCategory::class, 'todolist_category_id');
    }
}
