<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class Mediable extends Model
{
    use SoftDeletes, LogsActivity;

    protected $table = 'mediables';
    protected $primaryKey = 'media_id';
    public $incrementing = false;
    protected $keyType = 'int';
    protected $guarded = [];

    public function getActivitylogOptions(): LogOptions
    {
        try {
            $deviceInfo = getInfoDevice();
        } catch (\Throwable $th) {
            $deviceInfo = 'undefined';
        }
        return LogOptions::defaults()
            ->logAll()
            ->useLogName('model')
            ->logExcept([]) //'password'
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs()
            ->setDescriptionForEvent(fn (string $eventName) => json_encode($deviceInfo));
    }
    public function getKeyName()
    {
    return 'media_id';
    }

    protected static function boot()
    {
        parent::boot();

      
        static::updated(function ($model) {
            $model->sync_update = (string) now()->getPreciseTimestamp(6); // Store microseconds as string
            $model->saveQuietly();
        });
      
        static::created(function ($model) {
            $model->sync_update = (string) now()->getPreciseTimestamp(6); // Store microseconds as string
            $model->saveQuietly();
        });
        static::saving(function ($model) {
            $model->sync_update = (string) now()->getPreciseTimestamp(6); // Store microseconds as string
            $model->saveQuietly();
        });
        static::deleting(function ($model) {
            $model->sync_update = (string) now()->getPreciseTimestamp(6); // Store microseconds as string
            $model->saveQuietly();
        });
       
       
    }
}