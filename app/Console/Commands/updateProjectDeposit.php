<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Models\UserUpdate;
use Illuminate\Console\Command;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schema;

class updateProjectDeposit extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'deposit-update';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        ini_set('memory_limit', '-1');
        ini_set('max_execution_time', -1);
        //where('id',280)->
        \App\Models\Project::chunk(500, function ($projects) {
            foreach ($projects as $project) {
                $existingDepositIds = $project->deposit->pluck('id')->toArray();
                \App\Models\Deposit::whereIn('id', $existingDepositIds)->update(['updated_at' => now()]);

                $project->update(['updated_at' => now()]);
            }
        });

    }
}
