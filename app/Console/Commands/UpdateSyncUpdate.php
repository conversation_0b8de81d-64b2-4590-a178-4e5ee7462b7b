<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class UpdateSyncUpdate extends Command
{
    protected $signature = 'update:sync-update';
    protected $description = 'Update sync_update column with the current timestamp for all records, oldest first.';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        // Get all table names
        $tables = DB::select('SHOW TABLES');

        foreach ($tables as $table) {
            $tableName = array_values((array)$table)[0];
           
            // if($tableName!="mediables")
            //     continue;
            if (!Schema::hasColumn($tableName, 'sync_update')) {
                continue; // Skip tables without sync_update column
            }
         
            // dd($tableName );
            try {
                if($tableName=="mediables"){
                    DB::table($tableName)
                    ->orderBy('updated_at') 
                    ->chunkById(2000, function ($records) use ($tableName) {
                        
                        foreach ($records as $record) {
                            // dd($records );
                            $timestamp = (string) now()->getPreciseTimestamp(6);
                            DB::table($tableName)
                                ->where('media_id', $record->media_id)
                                ->update(['sync_update' => $timestamp]);
                        }
                    }, 'media_id');
                }
                else{
                    DB::table($tableName)
                    ->orderBy('updated_at') 
                    ->chunkById(2000, function ($records) use ($tableName) {
                        
                        foreach ($records as $record) {
                            // dd($records );
                            $timestamp = (string) now()->getPreciseTimestamp(6);
                            DB::table($tableName)
                                ->where('id', $record->id)
                                ->update(['sync_update' => $timestamp]);
                        }
                    }, 'id');
                }
                

                
                $this->info("Updating table: $tableName");

            } catch (\Exception $e) {
                // $this->info($e->getMessage());
                info($e->getMessage());
            }
            // Process oldest records first
            
        }

        $this->info('Update completed.');
    }
}
