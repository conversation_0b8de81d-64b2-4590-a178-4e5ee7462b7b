<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Models\UserUpdate;
use Illuminate\Console\Command;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schema;

class UserUpdateFetch extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'user-update';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // $users=User::whereNotNull('last_project_update')->get();
        // foreach($users as $user){
        //     $userUpdate = UserUpdate::firstOrCreate(
        //         ['user_id' => $user->id],
        //         [
        //             'last_project_update' => $user->last_project_update,
        //             'last_user_update' => $user->last_user_update,
        //             'last_todo_update' => $user->last_todo_update,
        //             'last_media_update' => $user->last_media_update
        //         ]
        //     );
        // }
         Schema::table('users', function (Blueprint $table) {
                $table->dropColumn([
                    'last_user_update',
                    'last_project_update',
                    'last_todo_update',
                    'last_media_update'
                ]);
        });

    }
}
