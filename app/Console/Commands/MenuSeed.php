<?php

namespace App\Console\Commands;

use Database\Seeders\MenuSeeder;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;

class MenuSeed extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'menu-seed';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        Artisan::call('db:seed', ['--class' => 'MenuSeeder']);
        $this->info('Done !');

    }
}
