<?php

namespace App\Exports;


use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Maatwebsite\Excel\Events\BeforeSheet;
use Maatwebsite\Excel\Events\AfterSheet;
use Maatwebsite\Excel\Concerns\WithEvents;



class CustomProjectsExport implements FromCollection, WithHeadings,ShouldAutoSize, WithEvents, WithStyles
{
    protected $data;
    protected $header;
    


    public function __construct(array $data, array $header)
    {
        $this->data = $data;
        $this->header = $header;
    }

    public function collection()
    {
        // Find the indices for the headers that need to be numeric
        $indicesToFormat = [
            'کد ملی' => null,
            'شناسه قبض برق' => null,
            'کد پستی' => null,
            'مبلغ واریزی' => null,
            'مبلغ کلی که باید واریز شود' => null,
            'UTM Y' => null
        ];

        // Get the indices of each field
        foreach ($indicesToFormat as $headerName => &$index) {
            $index = array_search($headerName, $this->header);
        }

        // Format data before returning
        $formattedData = array_map(function($row) use ($indicesToFormat) {
            foreach ($indicesToFormat as $headerName => $index) {
                if ($index !== false && isset($row[$index])) {
                    $row[$index] = is_numeric($row[$index]) ? (float)$row[$index] : $row[$index];
                }
            }
            return $row;
        }, $this->data);

        return collect($formattedData);
    }

    public function headings(): array
    {
        return $this->header;

    }


    /**
     * Register events for styling and setting RTL.
     */
    public function registerEvents(): array
    {
        return [
            BeforeSheet::class => function (BeforeSheet $event) {
                // Set the worksheet direction to RTL
                $event->sheet->getDelegate()->setRightToLeft(true);
                
            },
            AfterSheet::class => function (AfterSheet $event) {
                // Apply autofilter on all columns in the header row
                $highestColumn = $event->sheet->getDelegate()->getHighestColumn(); // Get the last column
                $cellRange = 'A1:' . $highestColumn . '1'; // Adjust range for the header row
                $event->sheet->getDelegate()->setAutoFilter($cellRange);

                $event->sheet->getDelegate()->getStyle($cellRange)->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_RIGHT);

            },
        ];
    }

    /**
     * Apply styles.
     */
    public function styles(Worksheet $sheet)
    {
        $columnWidths = $this->calculateColumnWidths();
        // dd($columnWidths);
        foreach ($columnWidths as $column => $width) {
            if (is_numeric($width) && $width > 0) {
                // Apply a scaling factor if necessary; experiment with 0.7-0.9 to match display results.
                $adjustedWidth = $width * 0.9;
    
                $sheet->getColumnDimension($column)
                      ->setAutoSize(false)  // Ensure auto-size is disabled
                      ->setWidth($adjustedWidth);  // Set the adjusted width
    
                // Debug log
                error_log("Applied width for $column: $adjustedWidth");
            } else {
                $sheet->getColumnDimension($column)->setAutoSize(true);
            }
        }

        // Apply RTL alignment specifically to the header row
        $highestColumn = $sheet->getHighestColumn();
        $headerRange = 'A1:' . $highestColumn . '1';

        return [
            $headerRange => [
                'alignment' => [
                    'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_RIGHT,
                ],
                'font' => [
                    'bold' => true,
                ],
            ],
        ];
    }

    // public function styles(Worksheet $sheet)
    // {
    //     $columnWidths = [];
    //     $minWidth = 10; // Set a minimum width for better readability

    //     // Collect maximum lengths of data and headers
    //     foreach ($this->data as $row) {
    //         foreach ($row as $index => $value) {
    //             $index = (int)$index; // Ensure index is an integer
    //             $valueLength = strlen(trim((string)$value));
    //             $columnWidths[$index] = max($columnWidths[$index] ?? 0, $valueLength);
    //         }
    //     }

    //     foreach ($this->header as $index => $header) {
    //         $index = (int)$index; // Ensure index is an integer
    //         $headerLength = strlen(trim($header));
    //         $columnWidths[$index] = max($columnWidths[$index] ?? 0, $headerLength);
    //     }

    //     // Set column width with a margin
    //     foreach ($columnWidths as $index => $width) {
    //         $index = (int)$index; // Ensure index is an integer
    //         $columnLetter = $this->getColumnLetter($index);
    //         // Set the width to fit content, with a small margin to avoid clipping
    //         $sheet->getColumnDimension($columnLetter)->setWidth(max($minWidth, $width * 1.2));
    //     }
    // }


    /**
     * Calculate the required width for each column based on maximum content length.
     */
    private function calculateColumnWidths()
    {
        $columnWidths = [];
        $minWidth = 10; // Minimum width for each column

        // Calculate widths for data rows
        foreach ($this->data as $row) {
            // Ensure the row is an array with integer keys
            foreach (array_values($row) as $index => $value) {
               try {
                $columnLetter = $this->getColumnLetter((int) $index); // Cast to integer explicitly
                $valueLength = mb_strlen((string) $value) + 2; // Convert to string and add padding

                // Compare with the current width or set it if not defined
                $columnWidths[$columnLetter] = max($columnWidths[$columnLetter] ?? $minWidth, $valueLength);
               } catch (\Throwable $th) {
                //throw $th;
               }
            }
        }

        // Include header widths in the calculation
        foreach (array_values($this->header) as $index => $header) {
            $columnLetter = $this->getColumnLetter((int) $index); // Explicit cast
            $headerLength = mb_strlen((string) $header) + 2;

            // Compare with data width if already set or use minimum width
            $columnWidths[$columnLetter] = max($columnWidths[$columnLetter] ?? $minWidth, $headerLength);
        }

        return $columnWidths;
    }






   
    
    /**
     * Convert a zero-based index to an Excel column letter.
     *
     * @param int $index
     * @return string
    */
    private function getColumnLetter(int $index): string
    {
        $columnLetter = '';
        while ($index >= 0) {
            $columnLetter = chr($index % 26 + 65) . $columnLetter;
            $index = (int) ($index / 26) - 1;
        }
        return $columnLetter;
    }


    
}
