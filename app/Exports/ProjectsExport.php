<?php

namespace App\Exports;

use App\Models\Project;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class ProjectsExport implements FromCollection, WithHeadings
{
    protected $projects;
    


    public function __construct($projects)
    {
        $this->projects = $projects;
    }

    public function collection()
    {
        return collect($this->projects->map(function($project) {

            return [
                'name' => $project->user->last_name,
                'phone' => $project->user->phone,
                'phone2' => $project->user->phone2,
                "father_name" => $project->user->father_name,
                "national_code" => $project->user->national_code,
                "birth_date" => $project->user->birth_date,
                "bank" => $project->user->bank,
                "bank_branch" => $project->user->bank_branch,
                "bank_account_number" => $project->user->bank_account_number,
                "bank_account_iban" => $project->user->bank_account_iban,
                'project_code' => $project->project_code,
                'title' => $project->title,
                'electricity_county_id' => $project->electricCity?->name,
                'electricity_affairs_code' => $project->electricity_affairs_code,
                'electricity_bill_id' => $project->electricity_bill_id,
                'mehrsan_file_number' => $project->mehrsan_file_number,
                'postal_code' => $project->postal_code,
                'power_plant_capacity' => $project->persian_power_plant_capacity,
                'support_organization' => $project->persian_support_organization,
                'province_id' => $project->province?->name,
                'county_id' => $project->country?->name,
                'city_id' => $project->city?->name,
                // 'payment_bank' => $project->payment_bank,
                'address' => $project->address,
                'payment_status' => $project->persian_payment_status,
                'remaining_deposit' => $project->remaining_deposit,
                'paid_amount' => $project->paid_amount,
                'payment_date' => convertDate($project->payment_date),
                'balance' => $project->balance,
                'inspection_status' => $project->persian_inspection_status,
                'inspection_date' => convertDate($project->inspection_date),
                'inspector_id' => $project->inspector?->name,
                'inspector_comment' => $project->inspector_comment,
                'design_file_result' => $project->persian_design_file_result,
                'panel_installation_date' => convertDate($project->panel_installation_date),
                'meter_installation_date' => convertDate($project->meter_installation_date),
                'panel_to_meter_duration' => $project->panel_to_meter_duration,
                'panel_to_meter_duration' => $project->panel_to_meter_duration,
                'panel_to_meter_duration' => $project->panel_to_meter_duration,
                'simban_id' => $project->simban_id,
                'payment_to_execution_duration' => $project->payment_to_execution_duration,
                'payment_to_network_connection_duration' => $project->payment_to_network_connection_duration,
                'installer_id' => $project->installer?->name,
                'designer_id' => $project->designer?->name,
                'structure_type_image' => $project->persian_structure_type,
                'current_phase' => $project->persian_current_phase,
                'send_list_comments' => $project->send_list_comments,
                'additional_costs_and_dorm_rent' => $project->additional_costs_and_dorm_rent,
                'additional_costs_and_dorm_rent_payment_date' => convertDate($project->additional_costs_and_dorm_rent_payment_date),
                'cost_of_materials' => $project->cost_of_materials,
                'material_cost_payment_date' => convertDate($project->material_cost_payment_date),
                'panel_type' => $project->panel_type,
                'panel_power' => $project->panel_power,
                'panel_quantity' => $project->panel_quantity,
                'inverter_model' => $project->inverter_model,
                'inverter_serial_number_image' => $project->inverter_serial_number_image,
                'inverter_to_electrical_base_distance' => $project->inverter_to_electrical_base_distance,
                'inverter_to_the_power_base_cable_used' => $project->inverter_to_the_power_base_cable_used,
                'meter_serial_number_image' => $project->meter_serial_number_image,
                'sim_card_number' => $project->sim_card_number,
                'utm_y' => $project->utm_y,
                'utm_x' => $project->utm_x,
                'longitude' => $project->longitude,
                'latitude' => $project->latitude,
                'google_maps_link' => $project->google_maps_link,
                'gis_code' => $project->gis_code,
                'has_monitoring' => $project->has_monitoring,
                'monitoring_code' => $project->monitoring_code,
                'monitoring_installer_id' => $project->monitoring_installer_id,
                'mehrsan_status' => $project->persian_mehrsan,
                'insurance' => $project->persian_insurance,
                'support_needed' => $project->persian_support_needed,
                'defect_comment' => $project->defect_comment,
                'checking_comment' => $project->checking_comment,
                'ware_postage_date' => convertDate($project->ware_postage_date),
                'ware_sender_id'=> $project->wareSender?->name,
                'type' => $project->persian_type,
                'status' => $project->persian_status,
                'defects_title' => implode(',', $project->defect->pluck('todolistCategory')->pluck('title')->toArray()),
                'checking_title' => implode(',', $project->checking->pluck('todolistCategory')->pluck('title')->toArray()),
            ];
        }));
    }

    public function headings(): array
    {
        return [
            'نام',
            'تلفن',
            'تلفن دوم',
            'نام پدر',
            'کد ملی',
            'تاریخ تولد',
            'بانک',
            'شعبه بانک',
            'شماره حساب بانک',
            'شماره شبا بانک مشترک',
            'کد پروژه',
            'عنوان',
            'شهرستان برق',
            'کد امور برق',
            'شناسه قبض برق',
            'شماره پرونده مهرسان',
            'کد پستی',
            'قدرت نیروگاه کیلو وات',
            'سازمان حمایتی',
            'استان',
            'شهرستان',
            'شهر',
            // 'payment_bank' => 'بانک',
            'آدرس',
            'وضعیت واریزی',
            'مانده',
            'مبلغ واریزی',
            'تاریخ واریز',
            'مبلغ کلی که باید واریز شود',
            'وضعیت بازدید',
            'تاریخ بازدید',
            'بازدید کننده',
            'کامنت بازدیدکننده',
            'نتیجه طراحی فایل',
            'تاریخ نصب پنل',
            'تاریخ نصب کنتور و اتصال به شبکه',
            'زمان انتظار تا اتصال به شبکه',
            'مسئول اتصال به شبکه',
            'مدت زمان واریز تا اجرا',
            'مدت زمان واریز تا اتصال به شبکه',
            'نصاب',
            'طراح',
            'نوع سازه عکس',
            'مرحله اجرایی',
            'لیست ارسال ها + توضیحات',
            'هزینه جانبی و کرایه خوابگاه',
            'تاریخ واریز هزینه های جانبی',
            'هزینه مصالح',
            'تاریخ واریز هزینه مصالح',
            'نوع پنل',
            'توان پنل',
            'تعداد پنل',
            'مدل اینورتر',
            'سریال اینورتر عکس',
            'فاصله اینورتر تا پایه ی برق',
            'مقدار کابل مصرفی تا پایه برق',
            'سریال کنتور عکس',
            'شماره سیم کارت',
            'UTM Y',
            'UTM x',
            'long',
            'latitude',
            'لینک کامل موقعیت گوگل',
            'کد GIS',
            'مانیتورینگ',
            'بارکد مانیتورینگ',
            'نصاب مانیتورینگ',
            'وضعیت مهرسان',
            'بیمه',
            'نیاز به پشتیبانی',
            'توضیحات نواقص ارسالی',
            'توضیحات بازررسی',
            'تاریخ ارسال جنس',
            'شخص ارسال کننده جنس',
            'نوع',
            'وضعیت',
            'نواقص',
            'بررسی ها',
        ];
    }

    
}
