<?php

namespace App\Observers;

use App\Models\Deposit;
use App\Models\Project;
use Carbon\Carbon;

class DepositObserver
{

    public function creating($deposit)
    {
        
        if (($deposit->isDirty('payment_date')))
        {
            $deposit->payment_date = convertPersianDateToGregorian($deposit->payment_date, 0);
        } 

        
    }

    public function updating($deposit)
    {
        
        if (($deposit->isDirty('payment_date')))
        {
            $deposit->payment_date = convertPersianDateToGregorian($deposit->payment_date, 0);
        }  
        $project = Project::find($deposit->project_id);
        $paid_amount_withdraw = $project->deposit("withdraw")->sum('amount');
        $paid_amount = $project->deposit("deposit")->sum('amount');
        $paid_amount = $paid_amount - $paid_amount_withdraw;
        $project->paid_amount = ($paid_amount);
        if(($project->isDirty('balance')) || env('DEBUG') || !is_null($project->balance)) 
        {
            $project->remaining_deposit = intval($project->balance) - intval(($project->paid_amount)) - intval($project->discount ?? 0);


            if ($project->remaining_deposit == 0  && !is_null($project->balance))
            {
                $project->payment_status = 'deposited';
            } 
            elseif ($project->remaining_deposit < 0  && !is_null($project->balance))
            {
                $project->payment_status = 'deposited-more';
                
                if ($project->payment_status=="deposited-more"){
                    $project->remaining_deposit=-1 * abs($project->remaining_deposit);
                }
            } 
            elseif ( $project->remaining_deposit && $project->balance && $project->remaining_deposit != $project->balance &&  $project->remaining_deposit < $project->balance && !is_null($project->balance))
            {
                $project->payment_status = 'deposited-but-has-balance';
            }
            elseif (($project->remaining_deposit == $project->balance) || ($project->remaining_deposit == 0 && $project->balance == 0) || ($project->remaining_deposit == null && $project->balance == null)){
                $project->payment_status = 'not-deposited';
            }
        }
        

        $deposit = $project->deposit("deposit")->first();

 
        $payment_date = $deposit->payment_date ?? null;
        $project->payment_date = $payment_date;


        $project->payment_bank = $deposit->bankId?->name  ?? null;    
            
        
        $project->save();

    }

    public function created(Deposit $deposit): void
    {
        // update project paid_amount , remaining_deposit and payment_date -----------------------
        $project = Project::find($deposit->project_id);
        $paid_amount_withdraw = $project->deposit("withdraw")->sum('amount');
        $paid_amount = $project->deposit("deposit")->sum('amount');
        $paid_amount = $paid_amount - $paid_amount_withdraw;
        $project->paid_amount = ($paid_amount);
        if (!is_null($project->balance))
        {
            $project->remaining_deposit = intval($project->balance) - intval(($project->paid_amount)) - intval($project->discount ?? 0);


            if ($project->remaining_deposit == 0  && !is_null($project->balance))
            {
                $project->payment_status = 'deposited';
            } 
            elseif ($project->remaining_deposit < 0  && !is_null($project->balance))
            {
                $project->payment_status = 'deposited-more';
                
                if ($project->payment_status=="deposited-more"){
                    $project->remaining_deposit=-1 * abs($project->remaining_deposit);
                }
            } 
            elseif ( $project->remaining_deposit && $project->balance && $project->remaining_deposit != $project->balance &&  $project->remaining_deposit < $project->balance && !is_null($project->balance))
            {
                $project->payment_status = 'deposited-but-has-balance';
            }
            elseif (($project->remaining_deposit == $project->balance) || ($project->remaining_deposit == 0 && $project->balance == 0) || ($project->remaining_deposit == null && $project->balance == null)){
                $project->payment_status = 'not-deposited';
            }
        }
        
        $min_payment_date = $project->deposit("deposit")->min('payment_date');
        $project->payment_date = $min_payment_date;
        
        $project->save();
        // -------------------------------------------------------------------------
    }

    public function updated(Deposit $deposit): void
    {
        // update project paid_amount , remaining_deposit and payment_date -----------------------
        $project = Project::find($deposit->project_id);
        $paid_amount_withdraw = $project->deposit("withdraw")->sum('amount');
        $paid_amount = $project->deposit("deposit")->sum('amount');
        $paid_amount = $paid_amount - $paid_amount_withdraw;
        $project->paid_amount = ($paid_amount);
        if(($project->isDirty('balance')) || env('DEBUG') || !is_null($project->balance)) 
        {
            $project->remaining_deposit = intval($project->balance) - intval(($project->paid_amount)) - intval($project->discount ?? 0);


            if ($project->remaining_deposit == 0  && !is_null($project->balance))
            {
                $project->payment_status = 'deposited';
            } 
            elseif ($project->remaining_deposit < 0  && !is_null($project->balance))
            {
                $project->payment_status = 'deposited-more';
                
                if ($project->payment_status=="deposited-more"){
                    $project->remaining_deposit=-1 * abs($project->remaining_deposit);
                }
            } 
            elseif ( $project->remaining_deposit && $project->balance && $project->remaining_deposit != $project->balance &&  $project->remaining_deposit < $project->balance && !is_null($project->balance))
            {
                $project->payment_status = 'deposited-but-has-balance';
            }
            elseif (($project->remaining_deposit == $project->balance) || ($project->remaining_deposit == 0 && $project->balance == 0) || ($project->remaining_deposit == null && $project->balance == null)){
                $project->payment_status = 'not-deposited';
            }

        }
       
        // dd($project->remaining_deposit,intval($project->balance) , intval($project->paid_amount ?? 0) , intval($project->discount ?? 0));

        $deposit = $project->deposit("deposit")->first();

 
        $payment_date = $deposit->payment_date ?? null;
        $project->payment_date = $payment_date;


        $project->payment_bank = $deposit->bankId?->name  ?? null;    
            
        
        $project->save();
        // -------------------------------------------------------------------------

    }


    public function deleting(Deposit $deposit): void
    {
        // Find the corresponding project
        $project = Project::find($deposit->project_id);

        // Update the paid_amount and remaining_deposit fields before the deposit is deleted
        $paid_amount_withdraw = $project->deposit("withdraw")->sum('amount');
        $paid_amount = $project->deposit("deposit")->sum('amount') - $deposit->amount;
        $paid_amount = $paid_amount - $paid_amount_withdraw;
        $project->paid_amount = ($paid_amount);

        if(($project->isDirty('balance')) || env('DEBUG') || !is_null($project->balance)) {
            $project->remaining_deposit = intval($project->balance) - intval(($project->paid_amount)) - intval($project->discount ?? 0);
            if ($project->remaining_deposit == 0  && !is_null($project->balance))
            {
                $project->payment_status = 'deposited';
            } 
            elseif ($project->remaining_deposit < 0  && !is_null($project->balance))
            {
                $project->payment_status = 'deposited-more';
                
                if ($project->payment_status=="deposited-more"){
                    $project->remaining_deposit=-1 * abs($project->remaining_deposit);
                }
            } 
            elseif ( $project->remaining_deposit && $project->balance && $project->remaining_deposit != $project->balance &&  $project->remaining_deposit < $project->balance && !is_null($project->balance))
            {
                $project->payment_status = 'deposited-but-has-balance';
            }
            elseif (($project->remaining_deposit == $project->balance) || ($project->remaining_deposit == 0 && $project->balance == 0) || ($project->remaining_deposit == null && $project->balance == null)){
                $project->payment_status = 'not-deposited';
            }
        }

        // Get the minimum payment date excluding the current deposit being deleted
        $min_payment_date = $project->deposit()->where('id', '!=', $deposit->id)->min('payment_date');
        $project->payment_date = $min_payment_date;

        // Save the project changes
        $project->save();
    }




}
