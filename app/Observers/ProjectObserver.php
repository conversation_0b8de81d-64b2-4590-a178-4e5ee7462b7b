<?php

namespace App\Observers;

use App\Models\Project;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class ProjectObserver
{

    public function creating($project)
    {
        // _logger(['project observer' , 'creating event']);
        if ($project->province_id && $project->support_organization && $project->county_id) {
            $project->project_code = $project::generateCodeProject(
                $project->province_id,
                $project->support_organization,
                $project->county_id
            );
        }
        // converting dates ------------------------------------
        if (($project->isDirty('ware_postage_date')) || env('DEBUG') )
        {
            $project->ware_postage_date = convertPersianDateToGregorian($project->ware_postage_date, 0);
        }
       
        if (($project->isDirty('panel_installation_date')) || env('DEBUG'))
        {
            $project->panel_installation_date = convertPersianDateToGregorian($project->panel_installation_date, 0);
        }

        if (($project->isDirty('meter_installation_date')) || env('DEBUG'))
        {
            $project->meter_installation_date = convertPersianDateToGregorian($project->meter_installation_date, 0);
        }
        if (($project->isDirty('monitoring_installation_date')) || env('DEBUG'))
        {
            $project->monitoring_installation_date = convertPersianDateToGregorian($project->monitoring_installation_date, 0);
        }
        if (($project->isDirty('additional_costs_and_dorm_rent_payment_date')) || env('DEBUG'))
        {
            $project->additional_costs_and_dorm_rent_payment_date = convertPersianDateToGregorian($project->additional_costs_and_dorm_rent_payment_date, 0);
        }
        if (($project->isDirty('material_cost_payment_date')) || env('DEBUG'))
        {
            $project->material_cost_payment_date = convertPersianDateToGregorian($project->material_cost_payment_date, 0);
        }
        if (($project->isDirty('measurement_date')) || env('DEBUG'))
        {
            $project->measurement_date = convertPersianDateToGregorian($project->measurement_date, 0);
        }
        if (($project->isDirty('mehrsan_status')) || env('DEBUG'))
        {
            $project->mehrsan_date = now();
        }
        if (($project->isDirty('mehrsan_date')) || env('DEBUG')){
            $project->mehrsan_date = $project->mehrsan_date ? convertPersianDateToGregorian($project->mehrsan_date, 0) : now();
        }
        // ------------------------------------------------------
        if (($project->isDirty('balance')))
        {
            if ( $project->remaining_deposit == null)
            {
                $project->remaining_deposit = $project->balance;
            }
                
        } 

        $project->payment_status = 'not-deposited';
      
        if ($project->remaining_deposit == 0  && $project->balance && $project->balance!=0 && $project->balance!=null && $project->remaining_deposit != $project->balance)
        {
            $project->payment_status = 'deposited';
        } 
        elseif ($project->remaining_deposit < 0  )
        {
            $project->payment_status = 'deposited-more';
        } 
        elseif ($project->remaining_deposit && $project->balance && $project->remaining_deposit != $project->balance &&  $project->remaining_deposit < $project->balance)
        {
            $project->payment_status = 'deposited-but-has-balance';
        }
        
    }

    public function updating($project)
    {
        // _logger(['project observer' , 'updating event']);
        if (
            $project->isDirty('province_id') ||
            $project->isDirty('support_organization') ||
            $project->isDirty('county_id')
            ) 
        {
            if ($project->province_id && $project->support_organization && $project->county_id)
            {
                $project->project_code = $project::generateCodeProject(
                $project->province_id,
                $project->support_organization,
                $project->county_id
                );
            }
            
        }
        // converting dates ------------------------------------
        // if (($project->isDirty('execution_date')))
        // {
        //     $project->execution_date = convertPersianDateToGregorian($project->execution_date, 0);
        // }

        // if (($project->isDirty('inspection_date')))
        // {
        //     $project->inspection_date = convertPersianDateToGregorian($project->inspection_date, 0);
        // }
        if (($project->isDirty('ware_postage_date')) || env('DEBUG'))
        {
            $project->ware_postage_date = convertPersianDateToGregorian($project->ware_postage_date, 0);
        }
       
        if (($project->isDirty('panel_installation_date')) || env('DEBUG'))
        {
            $project->panel_installation_date = convertPersianDateToGregorian($project->panel_installation_date, 0);
        }

        if (($project->isDirty('meter_installation_date')) || env('DEBUG'))
        {
            $project->meter_installation_date = convertPersianDateToGregorian($project->meter_installation_date, 0);
        }
        if (($project->isDirty('monitoring_installation_date')) || env('DEBUG'))
        {
            $project->monitoring_installation_date = convertPersianDateToGregorian($project->monitoring_installation_date, 0);
        }
        if (($project->isDirty('additional_costs_and_dorm_rent_payment_date')) || env('DEBUG'))
        {
            $project->additional_costs_and_dorm_rent_payment_date = convertPersianDateToGregorian($project->additional_costs_and_dorm_rent_payment_date, 0);
        }
        if (($project->isDirty('material_cost_payment_date')) || env('DEBUG'))
        {
            $project->material_cost_payment_date = convertPersianDateToGregorian($project->material_cost_payment_date, 0);
        }
        if (($project->isDirty('measurement_date')) || env('DEBUG'))
        {
            $project->measurement_date = convertPersianDateToGregorian($project->measurement_date, 0);
        }
        if (($project->isDirty('mehrsan_status')))
        {
            $project->mehrsan_date = now();
        }
        if (($project->isDirty('mehrsan_date')) || env('DEBUG')){
            $project->mehrsan_date = $project->mehrsan_date ? convertPersianDateToGregorian($project->mehrsan_date, 0) : now();
        }
        // ------------------------------------------------------
        if (($project->isDirty('remaining_deposit')) || env('DEBUG'))
        {
            $project->remaining_deposit=cleanData($project->remaining_deposit);
        } 
        if (($project->isDirty('balance')) || env('DEBUG'))
        {
            if ( $project->remaining_deposit == null)
            {
                $project->remaining_deposit = cleanData($project->remaining_deposit);
            }       
            $project->balance=cleanData($project->balance);
        } 
        if ($project->payment_status=="deposited-more"){
            $project->remaining_deposit=-1 * abs($project->remaining_deposit);
        }  
        

    }

    public function created(Project $project): void
    {
        // Define a flag to check if saveQuietly() should be called
        $shouldSave = false;

        if (($project->isDirty('panel_installation_date') || $project->isDirty('payment_date'))
            && isset($project->payment_date) || env('DEBUG')) {
            if ($project->payment_date and $project->panel_installation_date)
            {
                $paymentDate = new Carbon($project->payment_date);
                $executionDate = new Carbon($project->panel_installation_date);
                $project->payment_to_execution_duration = $paymentDate->diffInDays($executionDate);
                $shouldSave = true;
            }
            
        }
        // payment_to_network_connection_duration
        if (($project->isDirty('payment_date') || $project->isDirty('meter_installation_date')) || env('DEBUG')) {
            if ($project->payment_date and $project->meter_installation_date)
            {
                $project->payment_to_network_connection_duration = (new Carbon($project->payment_date))->diffInDays((new Carbon($project->meter_installation_date)));
                $shouldSave = true;
            }   
        }
        // panel_to_meter_duration
        if (($project->isDirty('panel_installation_date') || $project->isDirty('meter_installation_date')) || env('DEBUG')) {
        if ($project->panel_installation_date and $project->meter_installation_date)
        {
            $project->panel_to_meter_duration = (new Carbon($project->panel_installation_date))->diffInDays(new Carbon($project->meter_installation_date));
            $shouldSave = true;
        }
            
        }
        if (($project->isDirty('mehrsan_status')) || env('DEBUG'))
        {
            $project->mehrsan_date = now();
            $shouldSave = true;
        }
        if (($project->isDirty('mehrsan_date')) || env('DEBUG')){
            $project->mehrsan_date = $project->mehrsan_date ? convertPersianDateToGregorian($project->mehrsan_date, 0) : now();
            $shouldSave = true;
        }
        // update project remaining_deposit
        if(($project->isDirty('balance') && !is_null($project->balance))  || env('DEBUG')){
            $project->remaining_deposit = intval($project->balance) - intval(($project->paid_amount) ?? 0) - intval($project->discount ?? 0);
            $shouldSave = true;
        }
        // ----------------------------------
        if ($shouldSave)
            $project->saveQuietly();

    }

    public function updated(Project $project): void
    {

        // _logger(['project observer' , 'updated event']);
        //TODO : handle when current_phase changes
        // Define a flag to check if saveQuietly() should be called
        
        if (($project->isDirty('panel_installation_date') || $project->isDirty('payment_date') || env('DEBUG')) ) {
            if ($project->payment_date and $project->panel_installation_date)
            {
                $paymentDate = new Carbon($project->payment_date);
                $executionDate = new Carbon($project->panel_installation_date);
                $project->payment_to_execution_duration = $paymentDate->diffInDays($executionDate);
            }
        }
        
        // payment_to_network_connection_duration
        if (($project->isDirty('payment_date') || $project->isDirty('meter_installation_date') || env('DEBUG'))){
            if($project->payment_date and $project->meter_installation_date)
            {
                $project->payment_to_network_connection_duration = (new Carbon($project->payment_date))->diffInDays((new Carbon($project->meter_installation_date)));
            }
                
        }


        // panel_to_meter_duration
        if (($project->isDirty('panel_installation_date') || $project->isDirty('meter_installation_date') || env('DEBUG'))) {
            if($project->panel_installation_date and $project->meter_installation_date)
            {
                $project->panel_to_meter_duration = (new Carbon($project->panel_installation_date))->diffInDays(new Carbon($project->meter_installation_date));    
            }
               
        }
        if (($project->isDirty('mehrsan_status')))
        {
            $project->mehrsan_date = now();
        }
        if (($project->isDirty('mehrsan_date')) || env('DEBUG')){
            $project->mehrsan_date = $project->mehrsan_date ? convertPersianDateToGregorian($project->mehrsan_date, 0) : now();
        }
        // update project remaining_deposit
        if(( !is_null($project->balance) && ($project->balance!=0 && $project->balance!=''))){
            $paid_amount_withdraw = $project->deposit("withdraw")->sum('amount');
            $paid_amount = $project->deposit("deposit")->sum('amount');

            $paid_amount = $paid_amount - $paid_amount_withdraw;

            $project->paid_amount = $paid_amount;

            $project->remaining_deposit = intval($project->balance) - (intval($project->paid_amount ?? 0)) - intval($project->discount ?? 0);

            // dd($project->remaining_deposit,$project->balance,$project->paid_amount,intval($project->discount ?? 0));

        }elseif(is_null($project->balance) || $project->balance==0 || $project->balance==''){
            $project->payment_status = 'not-deposited';
            $project->remaining_deposit = null;
            $project->balance=null;
        }

        // dd($project->remaining_deposit,$project->paid_amount,abs($project->paid_amount));
        if (($project->isDirty('remaining_deposit') || env('DEBUG')) && $project->remaining_deposit == 0  && !is_null($project->balance))
        {
            $project->payment_status = 'deposited';
        } 
        elseif (($project->isDirty('remaining_deposit') || env('DEBUG')) && $project->remaining_deposit < 0  && !is_null($project->balance))
        {
            $project->payment_status = 'deposited-more';
            if ($project->payment_status=="deposited-more"){
                $project->remaining_deposit=-1 * abs($project->remaining_deposit);
            }
        } 
        elseif (($project->isDirty('remaining_deposit') || env('DEBUG'))  && $project->remaining_deposit && $project->balance && $project->remaining_deposit != $project->balance &&  $project->remaining_deposit < $project->balance && !is_null($project->balance))
        {
            $project->payment_status = 'deposited-but-has-balance';
        }
        
        elseif (($project->remaining_deposit == $project->balance) || ($project->remaining_deposit == 0 && $project->balance == 0) || ($project->remaining_deposit == null && $project->balance == null)){
            $project->payment_status = 'not-deposited';
        }
        elseif (($project->isDirty('remaining_deposit') || env('DEBUG'))){
            $project->payment_status = 'not-deposited';
        }
        
        // ----------------------------------
        $project->saveQuietly();
        
    }

}
