<?php

namespace App\Observers;

use App\Models\Deposit;
use App\Models\TodolistCategory;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class TodolistCategoryObserver
{

    public function creating($todolist)
    {
        // _logger(['todolist' , '1']);
    }

    public function updating($todolist)
    {
        // _logger(['todolist' , '2']);
         
    }

    public function created(TodolistCategory $todolist): void
    {

    }

    public function updated(TodolistCategory $todolist): void
    {
            

    }

}
