<?php

namespace App\Observers;

use App\Models\Defect;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class DefectObserver
{

    public function creating($defect)
    {
        // _logger(['test' , '1']);
    }

    public function updating($defect)
    {
         
    }

    public function created(Defect $defect): void
    {
    }

    public function updated(Defect $defect): void
    {
    }

    public function saved(Defect $defect)
    {
        
    }

    public function deleted(Defect $defect)
    {
        
    }

}
