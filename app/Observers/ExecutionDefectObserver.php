<?php

namespace App\Observers;

use App\Models\ExecutionDefect;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class ExecutionDefectObserver
{

    public function creating($defect)
    {
        if (($defect->isDirty('execution_defect_date')))
        {
            $defect->execution_defect_date = convertPersianDateToGregorian($defect->execution_defect_date, 0);
        }
        if (($defect->isDirty('fix_execution_defect_date')))
        {
            $defect->fix_execution_defect_date = convertPersianDateToGregorian($defect->fix_execution_defect_date, 0);
        }
    }

    public function updating($defect)
    {
        if (($defect->isDirty('execution_defect_date')))
        {
            $defect->execution_defect_date = convertPersianDateToGregorian($defect->execution_defect_date, 0);
        }
        if (($defect->isDirty('fix_execution_defect_date')))
        {
            $defect->fix_execution_defect_date = convertPersianDateToGregorian($defect->fix_execution_defect_date, 0);
        }
    }

    public function created(ExecutionDefect $defect): void
    {
    }

    public function updated(ExecutionDefect $defect): void
    {
    }

    public function saved(ExecutionDefect $defect)
    {
        
    }

    public function deleted(ExecutionDefect $defect)
    {
        
    }

}
