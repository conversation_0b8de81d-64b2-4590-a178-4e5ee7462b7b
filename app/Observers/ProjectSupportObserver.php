<?php

namespace App\Observers;

use App\Models\Project;
use App\Models\ProjectSupport;
use Carbon\Carbon;

class ProjectSupportObserver
{

    public function creating($project_support)
    {
        if (($project_support->isDirty('start_date')))
        {
            $project_support->start_date = convertPersianDateToGregorian($project_support->start_date, 0);
        } 

        if (($project_support->isDirty('end_date')))
        {
            $project_support->end_date = convertPersianDateToGregorian($project_support->end_date, 0);
        } 

        if ($project_support->isDirty('start_date') || $project_support->isDirty('end_date')) 
        {
            if($project_support->start_date && $project_support->end_date)
            {
                $start_date = Carbon::parse($project_support->start_date);
                $end_date = Carbon::parse($project_support->end_date);
                $duration = $start_date->diffInDays($end_date);
                $project_support->support_implementation_duration = $duration;
            }
            
        }
          
    }

    public function updating($project_support)
    {      
        if (($project_support->isDirty('start_date')))
        {
            $project_support->start_date = convertPersianDateToGregorian($project_support->start_date, 0);
        }  

        if (($project_support->isDirty('end_date')))
        {
            $project_support->end_date = convertPersianDateToGregorian($project_support->end_date, 0);
        }  
        
        if ($project_support->isDirty('start_date') || $project_support->isDirty('end_date')) 
        {
            if($project_support->start_date && $project_support->end_date)
            {
                $start_date = Carbon::parse($project_support->start_date);
                $end_date = Carbon::parse($project_support->end_date);
                $duration = $start_date->diffInDays($end_date);
                $project_support->support_implementation_duration = $duration;
            }
            
        }
    }

    public function created(ProjectSupport $project_support): void
    {
        // update project support_needed
        if (($project_support->isDirty('status')))
        {
            $project = Project::find($project_support->project_id);
            $earliestSupport = $project->support()->orderBy('updated_at', 'desc')->first();
            if ($earliestSupport) 
            {
                $project->support_needed = $earliestSupport->status;
                $project->save();
            }
        }
    }

    public function updated(ProjectSupport $project_support): void
    {
        // update project support_needed
        if (($project_support->isDirty('status')))
        {
            $project = Project::find($project_support->project_id);
            $earliestSupport = $project->support()->orderBy('updated_at', 'desc')->first();
            if ($earliestSupport) 
            {
                $project->support_needed = $earliestSupport->status;
                $project->save();
            }
        } 

    }

    public function deleting(ProjectSupport $project_support): void
    {

            $project = Project::find($project_support->project_id);
            $earliestSupport = $project->support()->where('id','!=', $project_support->id)->orderBy('updated_at', 'desc')->first();
            if ($earliestSupport) 
            {
                $project->support_needed = $earliestSupport->status;
                $project->save();
            }
            else
            {
                $project->support_needed = null;
                $project->save();
            }
    } 


}
