<?php

namespace App\Observers;

use App\Models\Project;
use App\Models\Visitor;
use Carbon\Carbon;

class VisitorObserver
{

    public function creating($visitor)
    {
        if (($visitor->isDirty('visit_date')))
        {
            $visitor->visit_date = convertPersianDateToGregorian($visitor->visit_date, 0);
        } 
    }

    public function updating($visitor)
    {
        
        if (($visitor->isDirty('visit_date')))
        {
            $visitor->visit_date = convertPersianDateToGregorian($visitor->visit_date, 0);
        }        
    }

    public function created(Visitor $visitor): void
    {
        $project = Project::find($visitor->project_id);
        if($project->visitor())
        {
           $latestVisitor = $project->visitor()->orderBy('visit_date', 'desc')->first();

            if ($latestVisitor) {
                $project->inspection_status = $latestVisitor->status;
                $project->inspection_date = $latestVisitor->visit_date;
                $project->inspector_id = $latestVisitor->user_id;
                $project->save();
            } 
        }
    }

    public function updated(Visitor $visitor): void
    {
        $project = Project::find($visitor->project_id);

        if($project->visitor())
        {
           $latestVisitor = $project->visitor()->orderBy('visit_date', 'desc')->first();
            if ($latestVisitor) {
                $project->inspection_status = $latestVisitor->status;
                $project->inspection_date = $latestVisitor->visit_date;
                $project->inspector_id = $latestVisitor->user_id;
                $project->save();
            } 
        }

    }

    public function deleting(Visitor $visitor): void
    {
        $project = Project::find($visitor->project_id);

        if($project->visitor())
        {
           $latestVisitor = $project->visitor()->where('id','!=', $visitor->id)->orderBy('visit_date', 'desc')->first();
            if ($latestVisitor) {
                $project->inspection_status = $latestVisitor->status;
                $project->inspection_date = $latestVisitor->visit_date;
                $project->inspector_id = $latestVisitor->user_id;
                $project->save();
            } 
            else
            {
                $project->inspection_status = 'not-visited';
                $project->inspection_date = null;
                $project->inspector_id = null;
                $project->save();
            }
        }

    }

}
