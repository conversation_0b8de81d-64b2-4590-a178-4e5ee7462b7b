<?php

namespace App\Traits;

use App\Jobs\UserActivityLogJob;
use Illuminate\Support\Facades\Request;

trait LogsTrait
{
    public function logsUserActivity($name, $event, $causedBy = null, $performedOn = null, $properties = null, $more_properties = null)
    {
        //check the activitylog activation
        if (!config('activitylog.enabled')) return;

        try {
            $deviceInfo = getInfoDevice();

        } catch (\Throwable $th) {
            $deviceInfo = 'undefined';
        }
        $properties = $this->setProperties($name, $event, $properties, $more_properties);
        UserActivityLogJob::dispatch($name, $event, $causedBy, $performedOn, $properties, $more_properties, $deviceInfo);
    }

    protected function setProperties($name, $event, $properties, $more_properties)
    {
        $baseUrl = Request::getSchemeAndHttpHost();
        // dd(Route::currentRouteName());
        $arrayProperties = null;
        if ($properties) {
            if ($more_properties == null)
                $more_properties = [];
            $arrayProperties = array_merge($more_properties, $properties);
            return $arrayProperties;
        } else {
            //$defaultLocale = WebcoSetting::getLocale();
            if ($name == config('activitylog.name_logs.view')) {
                // if (Str::startsWith(route(Route::currentRouteName(),['locale'=>$defaultLocale]), $baseUrl)) {
                //     $currentUrl = Str::after(route(Route::currentRouteName(),['locale'=>$defaultLocale]), $baseUrl);
                // }
                $currentUrl = str_replace(config('app.url'), '', Request::url());
                $arrayProperties = [
                    'attributes' => [
                        // 'route' => Route::currentRouteName(),
                        'URI' => $currentUrl
                    ],
                    'old' => [
                        // 'route' => app('router')->getRoutes(url()->previous())->match(app('request')->create(url()->previous()))->getName() ?? '',
                        'URI' => str_replace(url('/'), '', url()->previous())
                    ]
                ];

                if ($more_properties) {
                    $arrayProperties = array_merge($arrayProperties, $more_properties);
                }

                return $arrayProperties;
            }
        }

        return $arrayProperties;
    }
}
