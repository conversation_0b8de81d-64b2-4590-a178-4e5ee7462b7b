<?php

namespace App\Traits;

use App\Models\Mediable;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Database\UniqueConstraintViolationException;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

trait CustomMediable
{
    public function detachMediaTags($tags): void
    {
        $this->media()->newPivotStatement()
            ->where($this->media()->getMorphType(), $this->media()->getMorphClass())
            ->where($this->media()->getQualifiedForeignPivotKeyName(), $this->getKey())
            ->whereIn('tag', (array)$tags)
            ->update(['deleted_at' => now(), 'updated_at' => now()]);

        $this->markMediaDirty($tags);
    }

    public function attachMedia($media, $tags): void
    {
        $tags = (array)$tags;
        $increments = $this->getOrderValueForTags($tags);

        $ids = $this->extractPrimaryIds($media);

        foreach ($tags as $tag) {
            $attach = [];
            $media_ids = Mediable::whereIn('media_id', $ids)->where('mediable_id', $this->id)
                ->where('mediable_type', get_class($this))->where('tag', $tag)
                ->withTrashed()
                ->pluck('media_id')->toArray();
            $mediables = Mediable::whereIn('media_id', $ids)
            ->where('mediable_id', $this->id)
            ->where('mediable_type', get_class($this))
            ->where('tag', $tag)
            ->withTrashed()
            ->get();

            foreach ($mediables as $mediable) {
                $mediable->deleted_at = null;
                $mediable->updated_at = now();
                $mediable->save();
            }


            foreach ($ids as $id) {
                if (in_array($id, $media_ids))
                    continue;
                $attach[$id] = [
                    'tag' => $tag,
                    'order' => ++$increments[$tag],
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
            }
            if (!empty($attach)) {
                try {
                    foreach ($attach as $media_id => $pivotData) {
                        $mediable = new Mediable();
                        $mediable->media_id = $media_id;
                        $mediable->mediable_id = $this->id;
                        $mediable->mediable_type = get_class($this);
                        $mediable->tag = $pivotData['tag'];
                        $mediable->order = $pivotData['order'];
                        $mediable->created_at = $pivotData['created_at'];
                        $mediable->updated_at = $pivotData['updated_at'];
                        $mediable->save();
                    }
                } catch (UniqueConstraintViolationException $e) {
                    // Update deleted_at to null instead of throwing an exception
                    _logger(['problem in Custom mediable', $e], 'error');
                    Mediable::whereIn('media_id', array_keys($attach))
                        ->where('mediable_id', $this->id)
                        ->where('mediable_type', get_class($this))
                        ->where('tag', $tag)
                        ->withTrashed()
                        ->update(['deleted_at' => null, 'updated_at' => now()]);
                }
            }
            
        }

        $this->markMediaDirty($tags);
    }

    public function media(): MorphToMany
    {
        return $this
            ->morphToMany(
                config('mediable.model'),
                'mediable',
                config('mediable.mediables_table', 'mediables'),
                'mediable_id',
                config('mediable.mediables_table_related_key', 'media_id')
            )
            ->whereNull('deleted_at')
            ->withPivot('tag', 'order')
            ->orderBy('order');
    }


}