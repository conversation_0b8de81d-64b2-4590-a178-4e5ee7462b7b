<?php

namespace App\Traits;

use PhpOffice\PhpSpreadsheet\IOFactory;

trait UsesXlsxToArray {

    /**
     * Converts an xlsx file into an array. uses first row as header and indexing values
     * @param string $filename
     * @return array|false
     */
    public function xlsxToArray($filename = '')
    {
        if (!file_exists($filename) || !is_readable($filename))
            return false;

        $spreadsheet = IOFactory::load($filename);
        $worksheet = $spreadsheet->getActiveSheet();
        $header = null;
        $data = [];

        foreach ($worksheet->getRowIterator() as $rowIndex => $row) {
            $cellIterator = $row->getCellIterator();
            $cellIterator->setIterateOnlyExistingCells(false); // This loops through all cells, even if they are empty
            $rowArray = [];

            foreach ($cellIterator as $cell) {
                $rowArray[] = $cell->getValue();
            }

            if (!$header) {
                $header = array_map(function($column) {
                    $column = str_replace("\xc2\xa0",' ', $column);
                    $column = trim(strtolower($column));
                    $column = str_replace(' ', '_', $column);
                    return $column;
                }, $rowArray);
            } else {
                $correctedRow = array_map('trim', $rowArray);
                $data[] = array_combine($header, array_slice($correctedRow, 0, count($header)));
            }
        }

        return [$data, $header];
    }
}
