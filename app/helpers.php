<?php

use App\Models\AdminMenu;
use App\Responses\ApiResponse;
use App\Responses\ServiceResponse;
use Hekmatinasser\Verta\Verta;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Log;

if (!function_exists('get_menu')) {

    function get_menu()
    {
        return AdminMenu::where('type', 'menu')->orderBy('priority', 'ASC')->get();
    }
}
if (!function_exists('is_this_route')) {
    function is_this_route($routeName, $menu): bool
    {
        $routes = get_all_routes($menu);
        return in_array($routeName, $routes);
    }
}
if (!function_exists('cleanData')) {

    function cleanData($data)
    {
        $cleanedString = preg_replace('/[^0-9,]/', '', $data);
        $cleanedString = str_replace(',', '', $cleanedString);
        return (int)$cleanedString;
    }
}
if (!function_exists('get_all_routes')) {

    function get_all_routes($menu)
    {
        $route = [];
        if ($menu->route != 'none')
            $route[] = $menu->route;
        else {
            foreach ($menu->children as $child)
                $route = array_merge($route, get_all_routes($child));
        }
        return $route;
    }
}
if (!function_exists('encryptString')) {

    function encryptString($str): string
    {
        return Crypt::encryptString($str);
    }
}
if (!function_exists('decryptString')) {

    function decryptString($str): string
    {
        return Crypt::decryptString($str);
    }
}

if (!function_exists('getIp')) {

    function getIp(): string
    {
        return htmlspecialchars(strip_tags(@$_SERVER['HTTP_X_FORWARDED_FOR'] ?? (@$_SERVER['HTTP_X_REAL_IP'] ?? request()->ip())));
    }
}
if (!function_exists('apiResponse')) {

    function apiResponse(): ApiResponse
    {
        return new ApiResponse();
    }
}
if (!function_exists('convertDate')) {

    function convertDate($date, $with_time = 0)
    {
        try {
            if ($date) {
                $year = substr($date, 0, 4);
                if ($year[0] == '1') {
                    if ($year[1] == '3' || $year[1] == '4')
                        return $date;
                }
                // dd(Verta::instance($date)->format('Y/m/d'));
                if ($with_time)
                    return Verta::instance($date)->format('Y/m/d H:i');
                else
                    return Verta::instance($date)->format('Y/m/d');
            } else
                return null;
        } catch (\Throwable $th) {
            //throw $th;
            // dd($date);
            return $date;
        }
    }
}
if (!function_exists('convertPersianDateToGregorian')) {
    function convertPersianDateToGregorian($persianDate, $with_time = 1)
    {
        if (!$persianDate)
            return null;
        $year = substr($persianDate, 0, 4);
        if ($year[0] == '1') {
            if ($with_time)
                return Verta::parse($persianDate)->datetime()->format('Y-m-d H:i:s');
            else
                return Verta::parse($persianDate)->datetime()->format('Y-m-d');
        } else {
            return $persianDate;
        }
    }
}
if (!function_exists('getBrowser')) {

    function getBrowser()
    {

        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'undefined';

        $browser = "Unknown Browser";

        $browser_array = array(
            '/msie/i' => 'Internet Explorer',
            '/firefox/i' => 'Firefox',
            '/safari/i' => 'Safari',
            '/chrome/i' => 'Chrome',
            '/edge/i' => 'Edge',
            '/opera/i' => 'Opera',
            '/netscape/i' => 'Netscape',
            '/maxthon/i' => 'Maxthon',
            '/konqueror/i' => 'Konqueror',
            '/mobile/i' => 'Handheld Browser'
        );

        foreach ($browser_array as $regex => $value)
            if (preg_match($regex, $user_agent))
                $browser = $value;

        return $browser;
    }
}
if (!function_exists('getOS')) {

    function getOS()
    {

        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'undefined';

        $os_platform = "Unknown OS Platform";

        $os_array = array(
            '/windows nt 10/i' => 'Windows 10',
            '/windows nt 6.3/i' => 'Windows 8.1',
            '/windows nt 6.2/i' => 'Windows 8',
            '/windows nt 6.1/i' => 'Windows 7',
            '/windows nt 6.0/i' => 'Windows Vista',
            '/windows nt 5.2/i' => 'Windows Server 2003/XP x64',
            '/windows nt 5.1/i' => 'Windows XP',
            '/windows xp/i' => 'Windows XP',
            '/windows nt 5.0/i' => 'Windows 2000',
            '/windows me/i' => 'Windows ME',
            '/win98/i' => 'Windows 98',
            '/win95/i' => 'Windows 95',
            '/win16/i' => 'Windows 3.11',
            '/macintosh|mac os x/i' => 'Mac OS X',
            '/mac_powerpc/i' => 'Mac OS 9',
            '/linux/i' => 'Linux',
            '/ubuntu/i' => 'Ubuntu',
            '/iphone/i' => 'iPhone',
            '/ipod/i' => 'iPod',
            '/ipad/i' => 'iPad',
            '/android/i' => 'Android',
            '/blackberry/i' => 'BlackBerry',
            '/webos/i' => 'Mobile'
        );

        foreach ($os_array as $regex => $value)
            if (preg_match($regex, $user_agent))
                $os_platform = $value;

        return $os_platform;
    }
}
if (!function_exists('getInfoDevice')) {

    function getInfoDevice()
    {
        try {
            if (config()->get('seeding') === true)
                return [
                    'ip' => 'undefined',
                    'os' => 'undefined',
                    'browser' => 'undefined',
                    'info' => 'undefined',
                ];
            $ip = 'undefined';
            if (array_key_exists('HTTP_CLIENT_IP', $_SERVER)) {
                if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
                    $ip = $_SERVER['HTTP_CLIENT_IP'];
                }
            } else if (array_key_exists('HTTP_X_FORWARDED_FOR', $_SERVER)) {
                if (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
                    $ip = array_key_exists('HTTP_X_FORWARDED_FOR', $_SERVER) ? $_SERVER['HTTP_X_FORWARDED_FOR'] : 'undefined';
                } else {
                    $ip = array_key_exists('REMOTE_ADDR', $_SERVER) ? $_SERVER['REMOTE_ADDR'] : 'undefined';
                }
            }
            $user_agent = array_key_exists('HTTP_USER_AGENT', $_SERVER) ? $_SERVER['HTTP_USER_AGENT'] : 'undefined';
            return [
                'ip' => $ip,
                'os' => getOS(),
                'browser' => getBrowser(),
                'info' => $user_agent,
            ];
        } catch (\Throwable $th) {
            return [
                'ip' => 'undefined',
                'os' => 'undefined',
                'browser' => 'undefined',
                'info' => 'undefined',
            ];
        }
    }
}
if (!function_exists('getCurrentUser')) {

    function getCurrentUser()
    {
        $currentUser = null;
        if (Auth::check()) {
            $currentUser = Auth::user();
        } else if (Auth::guard('api')->check()) {
            $currentUser = Auth::guard('api')->user();
        }
        return $currentUser;
    }
}

if (!function_exists('_logger')) {
    function _logger($message, $level = 'info', $email = false): void
    {
        try {
            $message = convertBooleansToStrings($message);
            Log::$level(print_r($message, true));
        } catch (\Throwable $th) {
            //throw $th;
        }
    }
}
if (!function_exists('convertBooleansToStrings')) {

    function convertBooleansToStrings($input)
    {
        if (is_bool($input)) {
            return $input ? 'true' : 'false';
        }

        if (is_array($input)) {
            foreach ($input as &$value) {
                $value = convertBooleansToStrings($value);
            }
            return $input;
        }

        return $input;
    }
}
if (!function_exists('normalizeRequestCharacter')) {

    function normalizeRequestCharacter($request,$model=false)
    {
        $normalized = [
            'آ' => 'ا',
            'أ' => 'ا',
            'إ' => 'ا',
            'ۀ' => 'ه',
            'ة' => 'ه',
            'ؤ' => 'و',
            'ئ' => 'ی',
            'ي' => 'ی',
            'ك' => 'ک', // Arabic Kaf to Persian Kaf
            'ي' => 'ی', // Arabic Yeh to Persian Yeh
            'ة' => 'ه', // Arabic Ta Marbuta to Heh
            'ى' => 'ی', // Arabic Alef Maqsura to Yeh
            '۱' => '1', // Persian digit 1 to Latin digit 1
            '۲' => '2', // Persian digit 2 to Latin digit 2
            '۳' => '3', // Persian digit 3 to Latin digit 3
            '۴' => '4', // Persian digit 4 to Latin digit 4
            '۵' => '5', // Persian digit 5 to Latin digit 5
            '۶' => '6', // Persian digit 6 to Latin digit 6
            '۷' => '7', // Persian digit 7 to Latin digit 7
            '۸' => '8', // Persian digit 8 to Latin digit 8
            '۹' => '9', // Persian digit 9 to Latin digit 9
            '۰' => '0', // Persian digit 0 to Latin digit 0
        ];

        if($model==false){
            $modifiedRequestData = array_map(
                fn($value) => is_null($value) ? $value : (is_string($value) && $value !== '' ? strtr($value, $normalized) : $value),
                $request->all()
            );
    
            $request->merge($modifiedRequestData);
    
            return $request;
        }
        else{
            $normalized = [
                'آ' => 'ا',
                'أ' => 'ا',
                'إ' => 'ا',
                'ۀ' => 'ه',
                'ة' => 'ه',
                'ؤ' => 'و',
                'ئ' => 'ی',
                'ي' => 'ی',
                'ك' => 'ک',
                'ي' => 'ی',
                'ة' => 'ه',
                'ى' => 'ی',
                '۱' => '1',
                '۲' => '2',
                '۳' => '3',
                '۴' => '4',
                '۵' => '5',
                '۶' => '6',
                '۷' => '7',
                '۸' => '8',
                '۹' => '9',
                '۰' => '0',
            ];
        
            return strtr($request, $normalized);
        }
        
    }
}

if (!function_exists('normalizeValueCharacter')) {

    function normalizeValueCharacter($value)
    {
        $normalized = [
            'آ' => 'ا',
            'أ' => 'ا',
            'إ' => 'ا',
            'ۀ' => 'ه',
            'ة' => 'ه',
            'ؤ' => 'و',
            'ئ' => 'ی',
            'ي' => 'ی',
            'ك' => 'ک', // Arabic Kaf to Persian Kaf
            'ي' => 'ی', // Arabic Yeh to Persian Yeh
            'ة' => 'ه', // Arabic Ta Marbuta to Heh
            'ى' => 'ی', // Arabic Alef Maqsura to Yeh
            '۱' => '1', // Persian digit 1 to Latin digit 1
            '۲' => '2', // Persian digit 2 to Latin digit 2
            '۳' => '3', // Persian digit 3 to Latin digit 3
            '۴' => '4', // Persian digit 4 to Latin digit 4
            '۵' => '5', // Persian digit 5 to Latin digit 5
            '۶' => '6', // Persian digit 6 to Latin digit 6
            '۷' => '7', // Persian digit 7 to Latin digit 7
            '۸' => '8', // Persian digit 8 to Latin digit 8
            '۹' => '9', // Persian digit 9 to Latin digit 9
            '۰' => '0', // Persian digit 0 to Latin digit 0
        ];






        return is_null($value) ? $value : (is_string($value) && $value !== '' ? strtr($value, $normalized) : $value);
    }
}
