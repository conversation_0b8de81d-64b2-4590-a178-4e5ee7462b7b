<?php

namespace App\Responses;


use Illuminate\Contracts\Support\Responsable;
use Illuminate\Http\JsonResponse;

class ApiResponse implements Responsable
{
    private ServiceResponse $serviceResponse;

    public function __construct()
    {
        $this->serviceResponse = ServiceResponse::newInstance();
    }

    public function withMessage(array|string $message): self
    {
        $this->serviceResponse->message = is_array($message) ? $message : [$message];
        return $this;
    }

    public function withError(array|string $error): self
    {
        $this->serviceResponse->error = is_array($error) ? $error : [$error];
        return $this;
    }

    public function withStatusCode(int $statusCode): self
    {
        $this->serviceResponse->status_code = $statusCode;
        return $this;
    }

    public function withStatus(bool $status): self
    {
        $this->serviceResponse->status = $status;
        return $this;
    }

    public function withData(array $data): self
    {
        $this->serviceResponse->data = $data;
        return $this;
    }

    public function withFilter(array $filter): self
    {
        $this->serviceResponse->filter = $filter;
        return $this;
    }

    public function withPage(int $page): self
    {
        $this->serviceResponse->page = $page;
        return $this;
    }

    public function withPerPage(int $perPage): self
    {
        $this->serviceResponse->per_page = $perPage;
        return $this;
    }

    public function withTotalRecords(int $totalRecords): self
    {
        $this->serviceResponse->total_records = $totalRecords;
        return $this;
    }

    public function withHasMorePages(bool $hasMorePages): self
    {
        $this->serviceResponse->has_more_pages = $hasMorePages;
        return $this;
    }


    public function toResponse($request): JsonResponse
    {
        $array = $this->serviceResponse->toArray();
        $response = response()->json($array);
        $response->setStatusCode($this->serviceResponse->status_code ?? 200);
        return $response;
    }
}