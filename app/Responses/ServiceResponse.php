<?php

namespace App\Responses;

use Illuminate\Contracts\Support\Responsable;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

class ServiceResponse
{
    public $filter = null;
    public $data = null;
    public $page = null;
    public $per_page = null;
    public $total_records = null;
    public $has_more_pages = null;
    public $message = null;
    public $error = null;
    public $status = null;
    public $status_code = null;

    private function __construct()
    {
    }

    public static function newInstance(): self
    {
        return new self();
    }

    public function toJson(): false|string
    {
        $data = [
            'error' => $this->error,
            'status_code' => $this->status_code,
            'message' => $this->message,
            'data' => $this->data,
            'filter' => $this->filter,
            'page' => $this->page,
            'per_page' => $this->per_page,
            'total_records' => $this->total_records,
            'has_more_pages' => $this->has_more_pages,
        ];

        $data = array_filter($data, fn($value) => !is_null($value));

        return json_encode($data, JSON_PRETTY_PRINT);
    }

    public function toArray(): array
    {
        $data = [
            'error' => $this->error,
            'status_code' => $this->status_code ?? 200,
            'message' => $this->message ?? ['Request successfully handled'],
            'data' => $this->data,
            'filter' => $this->filter,
            'page' => $this->page,
            'per_page' => $this->per_page,
            'total_records' => $this->total_records,
            'has_more_pages' => $this->has_more_pages,
            'status' => $this->status,
        ];
        if (is_null($data['status'])) {
            $data['status'] = $data['status_code'] >= 200 && $data['status_code'] < 300;
        }

        return array_filter($data, fn($value) => !is_null($value));
    }

    public function toResponse($request): JsonResponse|Response
    {
        return response()->setStatusCode($this->status_code ?? 200)->json($this->toArray());
    }
}
