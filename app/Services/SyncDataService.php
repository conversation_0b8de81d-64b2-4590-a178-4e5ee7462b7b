<?php

namespace App\Services;

use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Plank\Mediable\Facades\MediaUploader;

class SyncDataService
{

    public function handle($request): \App\Responses\ApiResponse
    {
        DB::beginTransaction();
        $tableModelMapping = [
            'serial_panels' => \App\Models\SerialPanel::class,
            'projects' => \App\Models\Project::class,
            'project_supports' => \App\Models\ProjectSupport::class,
            'checkings' => \App\Models\Checking::class,
            'defects' => \App\Models\Defect::class,
            'execution_defects' => \App\Models\ExecutionDefect::class,
            'deposits' => \App\Models\Deposit::class,
            'visitors' => \App\Models\Visitor::class,
            'mediables' => \App\Models\Mediable::class,
            'users' => \App\Models\User::class,
        ];
        _logger(['createOrUpdate request data : ', $request->all()]);
        _logger(['createOrUpdate request file : ', $request->file()]);
        try {
            $dataString = $request->input('changes');
            $data = json_decode($dataString, true);

            // Group the records by table and table_id
            $groupedRecords = [];
            $this->handleGroupRecords($data, $groupedRecords);

            // _logger(['groupedRecords : ', $groupedRecords]);
            // Process each table
            $this->processTable($groupedRecords, $request, $tableModelMapping);
            DB::commit();
            return apiResponse()
                ->withMessage('داده ها با موفقیت دریافت شدند')
                ->withStatusCode(200);
        } catch (Exception $exception) {
            DB::rollBack();
            print_r($exception);
            return apiResponse()->withStatusCode(500)->withMessage($exception->getMessage());
        }
    }


    public function handleGroupRecords($data, &$groupedRecords): void
    {
        foreach ($data as $change) {
            // _logger(['change : ', $change]);
            $id = $change['id'];
            $table = $change['table'];
            $tableId = $change['table_id'] ?? 'new';
            $field = $change['field'];
            $isMedia = $change['is_media'] ?? 0;
            $isDeleted = $change['is_deleted'] ?? 0;

            if (!isset($groupedRecords[$change['table']])) {
                $groupedRecords[$change['table']] = [];
            }

            if (!isset($groupedRecords[$change['table']][$tableId])) {
                $groupedRecords[$change['table']][$tableId] = [];
            }

            $groupedRecords[$change['table']][$tableId][] = $change;
        }
    }

    public function processTable($groupedRecords, $request, $tableModelMapping): void
    {
        foreach ($groupedRecords as $table => $recordsById) {
            foreach ($recordsById as $tableId => $records) {
                $recordData = [];
                $is_new = false;
                $isNew = 0;
                $isDeleted = 0;
                $object = '';
                foreach ($records as $record) {
                    if (isset($record['is_new'])) {
                        $isNew = $record['is_new'] ?? 0;
                    }
                    if (isset($record['is_deleted'])) {
                        $isDeleted = $record['is_deleted'] ?? 0;
                    }

                

                    if ($table === 'mediables' && $record['field'] === 'media') {
                        if ($request->file("mediables-$tableId-media-" . $record['id'])) {
                            $media = MediaUploader::fromSource($request->file("mediables-$tableId-media-" . $record['id']))
                                ->toDirectory('panels/serial')
                                ->upload();
                            $recordData['media_id'] = $media->id;
                            $recordData['order'] = 1;
                        }
                    } else {
                        $recordData[$record['field']] = normalizeValueCharacter($record['value']);
                    }

                    if ($isNew == 1) {
                        $is_new = true;
                    }
                }

                try {
                    if ($table === 'mediables' && isset($recordData['mediable_type'])) {
                        $recordData['mediable_type'] = $tableModelMapping[$recordData['mediable_type']];
                        $mediable_object =  $recordData['mediable_type']::find($recordData['mediable_id']);
                        if ($mediable_object)
                        {
                            $mediable_object->touch();
                        

                        }
                    }

                    if ($is_new) {
                        \Log::info('Creating new record in table: ' . $table . ' with data: ', $recordData);
                        if ($table === 'mediables') {
                            $model = new $tableModelMapping[$table]();
                            foreach ($recordData as $key => $value) {
                                $model->$key = $value;
                            }
                            $model->save();
                            $object = $model;
                        } else {
                            $object = $tableModelMapping[$table]::create($recordData);
                        }
                        \Log::info('Created object media_id: ' . ($object->media_id ?? 'undefined'));
                    }
                   
                    else if($isDeleted == 0 ) { 
                        if ($table == 'mediables') {
                            $object = DB::table($table)->where('media_id', $tableId)->first();
                        }
                        else{
                            $object = DB::table($table)->where('id', $tableId)->first();
                        }
                        if ($object) {
                            if ($table == 'mediables') {
                                $model_object = $tableModelMapping[$table]::where('media_id', $object->media_id)->first();
                            }
                            else{
                                $model_object = $tableModelMapping[$table]::find($object->id);
                            }
                        
                            if($model_object){
                                $model_object->update($recordData);

                            }else{
                                info(['not found '.$table.' record id '. $tableId,auth()->user()?->id ]);
                            }
                        }
                    }
                    if ($isDeleted) {

                        if ($table == 'mediables') {
                            // DB::table($table)->where('media_id', $tableId)
                            //     ->update([
                            //         'updated_at' => now(),
                            //         'deleted_at' => now(),
                            //         'sync_update' => (string) now()->getPreciseTimestamp(6),
                                // ]);


                                $object = $tableModelMapping[$table]::where('media_id', $tableId)->first();
                                if($object){
                                    $object->delete(); // Soft delete
    
                                }else{
                                    info(['not found '.$table.' record id '. $tableId,auth()->user()?->id ]);
                                }

                        } else {
                            $object = $tableModelMapping[$table]::find($tableId);
                            if($object){
                                if($table=='execution_defects' ){
                                    continue;
                                }
                                $object->delete(); // Soft delete

                            }else{
                                info(['not found '.$table.' record id '. $tableId,auth()->user()?->id ]);
                            }
                        }
                    }
                    
                   

                   
                } catch (Exception $exception) {
                    _logger(['error', $exception]);
                }
            }
        }


    }
}