<?php

namespace App\Services;


use Illuminate\Support\Facades\Log;
use IPPanel\Errors\Error;
use IPPanel\Errors\HttpException;

class SmsService
{
    protected string $token;
    protected string $senderNumber;
    protected string $authPattern;

    public function __construct()
    {
        $this->token = config('sun.ippanel.api_key');
        $this->senderNumber = config('sun.ippanel.originator');
        $this->authPattern = config('sun.ippanel.auth_pattern');
    }

    public function send($to_number, $message)
    {
        try {
            if (is_null($to_number))
                return;
            $client = new \IPPanel\Client($this->token);
            $messageId = $client->sendPattern(
                $this->authPattern,    // pattern code
                $this->senderNumber,      // originator
                $to_number,  // recipient
                $message,  // pattern values
            );
        } catch (Error|HttpException|\Exception $e) {
            Log::error($e->getMessage());
        }


    }
}
