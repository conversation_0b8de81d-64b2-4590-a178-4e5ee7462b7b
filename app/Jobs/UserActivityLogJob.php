<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class UserActivityLogJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $name;
    protected $event;
    protected $causedBy;
    protected $performedOn;
    protected $properties;
    protected $moreProperties;
    protected $deviceInfo;

    /**
     * Create a new job instance.
     *
     * @param string $name
     * @param string $event
     * @param mixed $causedBy
     * @param mixed $performedOn
     * @param mixed $properties
     * @param mixed $moreProperties
     * @return void
     */
    public function __construct($name, $event, $causedBy = null, $performedOn = null, $properties = null, $moreProperties = null,$deviceInfo)
    {
        $this->name = $name;
        $this->event = $event;
        $this->causedBy = $causedBy;
        $this->performedOn = $performedOn;
        $this->properties = $properties;
        $this->moreProperties = $moreProperties;
        $this->deviceInfo = $deviceInfo;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {

        $activity = activity($this->name)->event($this->event);

        if ($this->causedBy) {
            $activity->causedBy($this->causedBy);
        } else {
            $activity->byAnonymous();
        }

        if ($this->performedOn) {
            $activity->performedOn($this->performedOn);
        }

        $this->setProperties($activity,$this->properties);

        $activity->log(json_encode($this->deviceInfo));
    }

    protected function setProperties($activity, $properties)
    {

        if ($properties) {
            $activity->withProperties($properties);
        }

        return $activity;
    }
}
