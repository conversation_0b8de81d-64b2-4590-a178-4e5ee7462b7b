<?php

namespace App\Jobs;

use App\Traits\UsesXlsxToArray;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Exception;

class ProcessMehrsanImport implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, UsesXlsxToArray;

    protected string $filename;

    public function __construct(string $filename)
    {
        $this->filename = $filename;
    }

    public function handle()
    {
        $filePath = storage_path('app/uploads/') . $this->filename;

        list($rows, $columns) = $this->xlsxToArray($filePath);

        $this->checkColumns($columns);

        $mehrsanStatusMap = $this->getMehrsanStatusMap();

        $processed = 0;
        $errors = 0;
        $noProjectUsers = 0;

        foreach ($rows as $row) {
            try {
                if (!empty($row['مرحله_جاری']) && !empty($row['کد_ملی'])) {
                    $statusLabel = $this->convertPersianDigitsToEnglish($row['مرحله_جاری']);
                    $national_code = $this->convertPersianDigitsToEnglish($row['کد_ملی']);

                    $mappedStatus = $mehrsanStatusMap[$statusLabel] ?? '';

                    if (empty($mappedStatus)) {
                        $errors++;
                        continue;
                    }

                    $result = $this->processInvitation((object)[
                        'status' => $mappedStatus,
                        'national_code' => $national_code,
                    ]);

                    if ($result === 'no_project') {
                        $noProjectUsers++;
                    } else {
                        $processed++;
                    }
                }
            } catch (Exception $exception) {
                logger($exception->getMessage());
                $errors++;
                continue;
            }
        }

        // Optionally, log or notify about the processing results
        logger("Mehrsan import processed: success={$processed}, errors={$errors}, no_project={$noProjectUsers}");
    }

    private function getMehrsanStatusMap(): array
    {
        $mapping = config('sun.params.mehrsanStatus', []);
        $map = [];
        foreach ($mapping as $item) {
            $map[$item['label']] = $item['value'];
        }
        return $map;
    }

    private function checkColumns($columns)
    {
        $requiredColumns = [
            'مرحله_جاری',
            'کد_ملی'
        ];

        foreach ($requiredColumns as $column) {
            abort_if(!in_array($column, $columns), 403, str_replace(':column', Str::title(str_replace('_', ' ', $column)), __(":column is required")));
        }
    }

    private function convertPersianDigitsToEnglish($input)
    {
        if (!$input) {
            return null;
        }
        $persianDigits = ['۰', '۱', '۲', '۳', '۴', '۵', '۶', '۷', '۸', '۹'];
        $englishDigits = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];

        return str_replace($persianDigits, $englishDigits, $input);
    }

    private function processInvitation($request)
    {
        $user = User::where('national_code', $request->national_code)->first();

        if ($user && $user->projects) {
            foreach ($user->projects as $project) {
                $project->mehrsan_status = $request->status;
                $project->save();
            }
            return 'processed';
        } else {
            return 'no_project';
        }
    }
}
