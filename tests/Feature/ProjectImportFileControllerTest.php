<?php

namespace Tests\Feature;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;
use App\Models\User;
use App\Models\Project;

class ProjectImportFileControllerTest extends TestCase
{
    public function setUp(): void
    {
        parent::setUp();
        Storage::fake('uploads');
    }

    /** @test */
    public function it_displays_the_import_file_form()
    {
        $response = $this->get(route('projects.create-import-file'));
        $response->assertStatus(200);
        $response->assertSee('بروزرسانی وضعیت مهرسان با بارگذاری فایل');
    }

    /** @test */
    public function it_processes_valid_file_and_shows_success_message()
    {
        // Prepare a fake file with valid rows
        $fileContent = <<<EOD
مرحله_جاری,کد_ملی
1,1234567890
2,0987654321
EOD;
        $file = UploadedFile::fake()->createWithContent('valid.xlsx', $fileContent);

        $response = $this->post(route('projects.store-import-file'), [
            'file' => $file,
        ]);

        $response->assertRedirect(route('projects.create-import-file'));
        $response->assertSessionHas('created_toast');
        $message = session('created_toast');
        $this->assertStringContainsString('تعداد موفق', $message);
    }

    /** @test */
    public function it_handles_rows_with_errors_and_shows_error_count()
    {
        // Prepare a fake file with one valid and one invalid row
        $fileContent = <<<EOD
مرحله_جاری,کد_ملی
1,1234567890
, // missing کد_ملی
EOD;
        $file = UploadedFile::fake()->createWithContent('invalid.xlsx', $fileContent);

        $response = $this->post(route('projects.store-import-file'), [
            'file' => $file,
        ]);

        $response->assertRedirect(route('projects.create-import-file'));
        $response->assertSessionHas('created_toast');
        $message = session('created_toast');
        $this->assertStringContainsString('تعداد خطا', $message);
    }

    /** @test */
    public function it_counts_users_with_no_projects_and_shows_message()
    {
        // Create a user with no projects
        $user = User::factory()->create(['national_code' => '1111111111']);

        // Prepare a fake file with that user's national code
        $fileContent = <<<EOD
مرحله_جاری,کد_ملی
1,1111111111
EOD;
        $file = UploadedFile::fake()->createWithContent('noproject.xlsx', $fileContent);

        $response = $this->post(route('projects.store-import-file'), [
            'file' => $file,
        ]);

        $response->assertRedirect(route('projects.create-import-file'));
        $response->assertSessionHas('created_toast');
        $message = session('created_toast');
        $this->assertStringContainsString('تعداد بدون پروژه', $message);
    }
}
