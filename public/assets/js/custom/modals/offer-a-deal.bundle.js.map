{"version": 3, "file": "/js/custom/modals/offer-a-deal.bundle.js", "mappings": ";;;;;;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA,IAAI,KAA6B;AACjC;AACA;;;;;;;;;;;AC/Ba;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,KAAK;AACL;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA,IAAI,KAA6B;AACjC;AACA;;;;;;;;;;;AClJa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,KAAK;AACL;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA,IAAI,KAA6B;AACjC;AACA;;;;;;;;;;;AC3Ia;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA,IAAI,KAA6B;AACjC;AACA;;;;;;;;;;;ACtDa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,KAAK;AACL;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA,IAAI,KAA6B;AACjC;AACA;;;;;;UCtGA;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;;;;;;ACtBA,mCAAmC,mBAAO,CAAC,wJAAwD;AACnG,kCAAkC,mBAAO,CAAC,sJAAuD;AACjG,kCAAkC,mBAAO,CAAC,sJAAuD;AACjG,+BAA+B,mBAAO,CAAC,gJAAoD;AAC3F,2BAA2B,mBAAO,CAAC,gJAAoD", "sources": ["webpack://keenthemes/../../../themes/metronic/html/demo3/src/js/custom/modals/offer-a-deal/bundle/complete.js", "webpack://keenthemes/../../../themes/metronic/html/demo3/src/js/custom/modals/offer-a-deal/bundle/details.js", "webpack://keenthemes/../../../themes/metronic/html/demo3/src/js/custom/modals/offer-a-deal/bundle/finance.js", "webpack://keenthemes/../../../themes/metronic/html/demo3/src/js/custom/modals/offer-a-deal/bundle/main.js", "webpack://keenthemes/../../../themes/metronic/html/demo3/src/js/custom/modals/offer-a-deal/bundle/type.js", "webpack://keenthemes/webpack/bootstrap", "webpack://keenthemes/../../../themes/metronic/html/tools/webpack/js/custom/modals/offer-a-deal.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTModalOfferADealComplete = function () {\r\n\t// Variables\r\n\tvar startButton;\r\n\tvar form;\r\n\tvar stepper;\r\n\r\n\t// Private functions\r\n\tvar handleForm = function() {\r\n\t\tstartButton.addEventListener('click', function () {\r\n\t\t\tstepper.goTo(1);\r\n\t\t});\r\n\t}\r\n\r\n\treturn {\r\n\t\t// Public functions\r\n\t\tinit: function () {\r\n\t\t\tform = KTModalOfferADeal.getForm();\r\n\t\t\tstepper = KTModalOfferADeal.getStepperObj();\r\n\t\t\tstartButton = KTModalOfferADeal.getStepper().querySelector('[data-kt-element=\"complete-start\"]');\r\n\r\n\t\t\thandleForm();\r\n\t\t}\r\n\t};\r\n}();\r\n\r\n// Webpack support\r\nif (typeof module !== 'undefined' && typeof module.exports !== 'undefined') {\r\n\tmodule.exports = KTModalOfferADealComplete;\r\n}", "\"use strict\";\r\n\r\n// Class definition\r\nvar KTModalOfferADealDetails = function () {\r\n\t// Variables\r\n\tvar nextButton;\r\n\tvar previousButton;\r\n\tvar validator;\r\n\tvar form;\r\n\tvar stepper;\r\n\r\n\t// Private functions\r\n\tvar initForm = function() {\r\n\t\t// Due date. For more info, please visit the official plugin site: https://flatpickr.js.org/\r\n\t\tvar dueDate = $(form.querySelector('[name=\"details_activation_date\"]'));\r\n\t\tdueDate.flatpickr({\r\n\t\t\tenableTime: true,\r\n\t\t\tdateFormat: \"d, M Y, H:i\",\r\n\t\t});\r\n\r\n\t\t// Expiry year. For more info, plase visit the official plugin site: https://select2.org/\r\n        $(form.querySelector('[name=\"details_customer\"]')).on('change', function() {\r\n            // Revalidate the field when an option is chosen\r\n            validator.revalidateField('details_customer');\r\n        });\r\n\t}\r\n\r\n\tvar initValidation = function() {\r\n\t\t// Init form validation rules. For more info check the FormValidation plugin's official documentation:https://formvalidation.io/\r\n\t\tvalidator = FormValidation.formValidation(\r\n\t\t\tform,\r\n\t\t\t{\r\n\t\t\t\tfields: {\r\n\t\t\t\t\t'details_customer': {\r\n\t\t\t\t\t\tvalidators: {\r\n\t\t\t\t\t\t\tnotEmpty: {\r\n\t\t\t\t\t\t\t\tmessage: 'Customer is required'\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\t'details_title': {\r\n\t\t\t\t\t\tvalidators: {\r\n\t\t\t\t\t\t\tnotEmpty: {\r\n\t\t\t\t\t\t\t\tmessage: 'Deal title is required'\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\t\t\t\t\t\r\n\t\t\t\t\t'details_activation_date': {\r\n\t\t\t\t\t\tvalidators: {\r\n\t\t\t\t\t\t\tnotEmpty: {\r\n\t\t\t\t\t\t\t\tmessage: 'Activation date is required'\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\t'details_notifications[]': {\r\n\t\t\t\t\t\tvalidators: {\r\n\t\t\t\t\t\t\tnotEmpty: {\r\n\t\t\t\t\t\t\t\tmessage: 'Notifications are required'\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\t\r\n\t\t\t\tplugins: {\r\n\t\t\t\t\ttrigger: new FormValidation.plugins.Trigger(),\r\n\t\t\t\t\tbootstrap: new FormValidation.plugins.Bootstrap5({\r\n\t\t\t\t\t\trowSelector: '.fv-row',\r\n                        eleInvalidClass: '',\r\n                        eleValidClass: ''\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t);\r\n\t}\r\n\r\n\tvar handleForm = function() {\r\n\t\tnextButton.addEventListener('click', function (e) {\r\n\t\t\t// Prevent default button action\r\n\t\t\te.preventDefault();\r\n\r\n\t\t\t// Disable button to avoid multiple click \r\n\t\t\tnextButton.disabled = true;\r\n\r\n\t\t\t// Validate form before submit\r\n\t\t\tif (validator) {\r\n\t\t\t\tvalidator.validate().then(function (status) {\r\n\t\t\t\t\tconsole.log('validated!');\r\n\r\n\t\t\t\t\tif (status == 'Valid') {\r\n\t\t\t\t\t\t// Show loading indication\r\n\t\t\t\t\t\tnextButton.setAttribute('data-kt-indicator', 'on');\r\n\r\n\t\t\t\t\t\t// Simulate form submission\r\n\t\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\t\t// Simulate form submission\r\n\t\t\t\t\t\t\tnextButton.removeAttribute('data-kt-indicator');\r\n\r\n\t\t\t\t\t\t\t// Enable button\r\n\t\t\t\t\t\t\tnextButton.disabled = false;\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t// Go to next step\r\n\t\t\t\t\t\t\tstepper.goNext();\r\n\t\t\t\t\t\t}, 1500);   \t\t\t\t\t\t\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// Enable button\r\n\t\t\t\t\t\tnextButton.disabled = false;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// Show popup warning. For more info check the plugin's official documentation: https://sweetalert2.github.io/\r\n\t\t\t\t\t\tSwal.fire({\r\n\t\t\t\t\t\t\ttext: \"Sorry, looks like there are some errors detected, please try again.\",\r\n\t\t\t\t\t\t\ticon: \"error\",\r\n\t\t\t\t\t\t\tbuttonsStyling: false,\r\n\t\t\t\t\t\t\tconfirmButtonText: \"Ok, got it!\",\r\n\t\t\t\t\t\t\tcustomClass: {\r\n\t\t\t\t\t\t\t\tconfirmButton: \"btn btn-primary\"\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t}\t\t\t\r\n\t\t});\r\n\r\n\t\tpreviousButton.addEventListener('click', function () {\r\n\t\t\t// Go to previous step\r\n\t\t\tstepper.goPrevious();\r\n\t\t});\r\n\t}\r\n\r\n\treturn {\r\n\t\t// Public functions\r\n\t\tinit: function () {\r\n\t\t\tform = KTModalOfferADeal.getForm();\r\n\t\t\tstepper = KTModalOfferADeal.getStepperObj();\r\n\t\t\tnextButton = KTModalOfferADeal.getStepper().querySelector('[data-kt-element=\"details-next\"]');\r\n\t\t\tpreviousButton = KTModalOfferADeal.getStepper().querySelector('[data-kt-element=\"details-previous\"]');\r\n\r\n\t\t\tinitForm();\r\n\t\t\tinitValidation();\r\n\t\t\thandleForm();\r\n\t\t}\r\n\t};\r\n}();\r\n\r\n// Webpack support\r\nif (typeof module !== 'undefined' && typeof module.exports !== 'undefined') {\r\n\tmodule.exports = KTModalOfferADealDetails;\r\n}", "\"use strict\";\r\n\r\n// Class definition\r\nvar KTModalOfferADealFinance = function () {\r\n\t// Variables\r\n\tvar nextButton;\r\n\tvar previousButton;\r\n\tvar validator;\r\n\tvar form;\r\n\tvar stepper;\r\n\r\n\t// Private functions\r\n\tvar initValidation = function() {\r\n\t\t// Init form validation rules. For more info check the FormValidation plugin's official documentation:https://formvalidation.io/\r\n\t\tvalidator = FormValidation.formValidation(\r\n\t\t\tform,\r\n\t\t\t{\r\n\t\t\t\tfields: {\r\n\t\t\t\t\t'finance_setup': {\r\n\t\t\t\t\t\tvalidators: {\r\n\t\t\t\t\t\t\tnotEmpty: {\r\n\t\t\t\t\t\t\t\tmessage: 'Amount is required'\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tcallback: {\r\n\t\t\t\t\t\t\t\tmessage: 'The amount must be greater than $100',\r\n\t\t\t\t\t\t\t\tcallback: function(input) {\r\n\t\t\t\t\t\t\t\t\tvar currency = input.value;\r\n\t\t\t\t\t\t\t\t\tcurrency = currency.replace(/[$,]+/g,\"\");\r\n\r\n\t\t\t\t\t\t\t\t\tif (parseFloat(currency) < 100) {\r\n\t\t\t\t\t\t\t\t\t\treturn false;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\t'finance_usage': {\r\n\t\t\t\t\t\tvalidators: {\r\n\t\t\t\t\t\t\tnotEmpty: {\r\n\t\t\t\t\t\t\t\tmessage: 'Usage type is required'\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\t'finance_allow': {\r\n\t\t\t\t\t\tvalidators: {\r\n\t\t\t\t\t\t\tnotEmpty: {\r\n\t\t\t\t\t\t\t\tmessage: 'Allowing budget is required'\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\t\r\n\t\t\t\tplugins: {\r\n\t\t\t\t\ttrigger: new FormValidation.plugins.Trigger(),\r\n\t\t\t\t\tbootstrap: new FormValidation.plugins.Bootstrap5({\r\n\t\t\t\t\t\trowSelector: '.fv-row',\r\n                        eleInvalidClass: '',\r\n                        eleValidClass: ''\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t);\r\n\r\n\t\t// Revalidate on change\r\n\t\tKTDialer.getInstance(form.querySelector('#kt_modal_finance_setup')).on('kt.dialer.changed', function() {\r\n\t\t\t// Revalidate the field when an option is chosen\r\n            validator.revalidateField('finance_setup');\r\n\t\t});\r\n\t}\r\n\r\n\tvar handleForm = function() {\r\n\t\tnextButton.addEventListener('click', function (e) {\r\n\t\t\t// Prevent default button action\r\n\t\t\te.preventDefault();\r\n\r\n\t\t\t// Disable button to avoid multiple click \r\n\t\t\tnextButton.disabled = true;\r\n\r\n\t\t\t// Validate form before submit\r\n\t\t\tif (validator) {\r\n\t\t\t\tvalidator.validate().then(function (status) {\r\n\t\t\t\t\tconsole.log('validated!');\r\n\r\n\t\t\t\t\tif (status == 'Valid') {\r\n\t\t\t\t\t\t// Show loading indication\r\n\t\t\t\t\t\tnextButton.setAttribute('data-kt-indicator', 'on');\r\n\r\n\t\t\t\t\t\t// Simulate form submission\r\n\t\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\t\t// Simulate form submission\r\n\t\t\t\t\t\t\tnextButton.removeAttribute('data-kt-indicator');\r\n\r\n\t\t\t\t\t\t\t// Enable button\r\n\t\t\t\t\t\t\tnextButton.disabled = false;\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t// Go to next step\r\n\t\t\t\t\t\t\tstepper.goNext();\r\n\t\t\t\t\t\t}, 1500);   \t\t\t\t\t\t\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// Enable button\r\n\t\t\t\t\t\tnextButton.disabled = false;\r\n\r\n\t\t\t\t\t\t// Show popup warning. For more info check the plugin's official documentation: https://sweetalert2.github.io/\r\n\t\t\t\t\t\tSwal.fire({\r\n\t\t\t\t\t\t\ttext: \"Sorry, looks like there are some errors detected, please try again.\",\r\n\t\t\t\t\t\t\ticon: \"error\",\r\n\t\t\t\t\t\t\tbuttonsStyling: false,\r\n\t\t\t\t\t\t\tconfirmButtonText: \"Ok, got it!\",\r\n\t\t\t\t\t\t\tcustomClass: {\r\n\t\t\t\t\t\t\t\tconfirmButton: \"btn btn-primary\"\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t}\t\t\t\r\n\t\t});\r\n\r\n\t\tpreviousButton.addEventListener('click', function () {\r\n\t\t\tstepper.goPrevious();\r\n\t\t});\r\n\t}\r\n\r\n\treturn {\r\n\t\t// Public functions\r\n\t\tinit: function () {\r\n\t\t\tform = KTModalOfferADeal.getForm();\r\n\t\t\tstepper = KTModalOfferADeal.getStepperObj();\r\n\t\t\tnextButton = KTModalOfferADeal.getStepper().querySelector('[data-kt-element=\"finance-next\"]');\r\n\t\t\tpreviousButton = KTModalOfferADeal.getStepper().querySelector('[data-kt-element=\"finance-previous\"]');\r\n\r\n\t\t\tinitValidation();\r\n\t\t\thandleForm();\r\n\t\t}\r\n\t};\r\n}();\r\n\r\n// Webpack support\r\nif (typeof module !== 'undefined' && typeof module.exports !== 'undefined') {\r\n\tmodule.exports = KTModalOfferADealFinance;\r\n}", "\"use strict\";\r\n\r\n// Class definition\r\nvar KTModalOfferADeal = function () {\r\n    // Private variables\r\n\tvar stepper;\r\n\tvar stepperObj;\r\n\tvar form;\t\r\n\r\n\t// Private functions\r\n\tvar initStepper = function () {\r\n\t\t// Initialize Stepper\r\n\t\tstepperObj = new KTStepper(stepper);\r\n\t}\r\n\r\n\treturn {\r\n\t\t// Public functions\r\n\t\tinit: function () {\r\n\t\t\tstepper = document.querySelector('#kt_modal_offer_a_deal_stepper');\r\n\t\t\tform = document.querySelector('#kt_modal_offer_a_deal_form');\r\n\r\n\t\t\tinitStepper();\r\n\t\t},\r\n\r\n\t\tgetStepper: function () {\r\n\t\t\treturn stepper;\r\n\t\t},\r\n\r\n\t\tgetStepperObj: function () {\r\n\t\t\treturn stepperObj;\r\n\t\t},\r\n\t\t\r\n\t\tgetForm: function () {\r\n\t\t\treturn form;\r\n\t\t}\r\n\t};\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function () {\r\n\tif (!document.querySelector('#kt_modal_offer_a_deal')) {\r\n\t\treturn;\r\n\t}\r\n\r\n    KTModalOfferADeal.init();\r\n    KTModalOfferADealType.init();\r\n    KTModalOfferADealDetails.init();\r\n    KTModalOfferADealFinance.init();\r\n    KTModalOfferADealComplete.init();\r\n});\r\n\r\n// Webpack support\r\nif (typeof module !== 'undefined' && typeof module.exports !== 'undefined') {\r\n\tmodule.exports = KTModalOfferADeal;\r\n}", "\"use strict\";\r\n\r\n// Class definition\r\nvar KTModalOfferADealType = function () {\r\n\t// Variables\r\n\tvar nextButton;\r\n\tvar validator;\r\n\tvar form;\r\n\tvar stepper;\r\n\r\n\t// Private functions\r\n\tvar initValidation = function() {\r\n\t\t// Init form validation rules. For more info check the FormValidation plugin's official documentation:https://formvalidation.io/\r\n\t\tvalidator = FormValidation.formValidation(\r\n\t\t\tform,\r\n\t\t\t{\r\n\t\t\t\tfields: {\r\n\t\t\t\t\t'offer_type': {\r\n\t\t\t\t\t\tvalidators: {\r\n\t\t\t\t\t\t\tnotEmpty: {\r\n\t\t\t\t\t\t\t\tmessage: 'Offer type is required'\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\t\r\n\t\t\t\tplugins: {\r\n\t\t\t\t\ttrigger: new FormValidation.plugins.Trigger(),\r\n\t\t\t\t\tbootstrap: new FormValidation.plugins.Bootstrap5({\r\n\t\t\t\t\t\trowSelector: '.fv-row',\r\n                        eleInvalidClass: '',\r\n                        eleValidClass: ''\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t);\r\n\t}\r\n\r\n\tvar handleForm = function() {\r\n\t\tnextButton.addEventListener('click', function (e) {\r\n\t\t\t// Prevent default button action\r\n\t\t\te.preventDefault();\r\n\r\n\t\t\t// Disable button to avoid multiple click \r\n\t\t\tnextButton.disabled = true;\r\n\r\n\t\t\t// Validate form before submit\r\n\t\t\tif (validator) {\r\n\t\t\t\tvalidator.validate().then(function (status) {\r\n\t\t\t\t\tconsole.log('validated!');\r\n\r\n\t\t\t\t\tif (status == 'Valid') {\r\n\t\t\t\t\t\t// Show loading indication\r\n\t\t\t\t\t\tnextButton.setAttribute('data-kt-indicator', 'on');\r\n\r\n\t\t\t\t\t\t// Simulate form submission\r\n\t\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\t\t// Simulate form submission\r\n\t\t\t\t\t\t\tnextButton.removeAttribute('data-kt-indicator');\r\n\r\n\t\t\t\t\t\t\t// Enable button\r\n\t\t\t\t\t\t\tnextButton.disabled = false;\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t// Go to next step\r\n\t\t\t\t\t\t\tstepper.goNext();\r\n\t\t\t\t\t\t}, 1000);   \t\t\t\t\t\t\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// Enable button\r\n\t\t\t\t\t\tnextButton.disabled = false;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// Show popup warning. For more info check the plugin's official documentation: https://sweetalert2.github.io/\r\n\t\t\t\t\t\tSwal.fire({\r\n\t\t\t\t\t\t\ttext: \"Sorry, looks like there are some errors detected, please try again.\",\r\n\t\t\t\t\t\t\ticon: \"error\",\r\n\t\t\t\t\t\t\tbuttonsStyling: false,\r\n\t\t\t\t\t\t\tconfirmButtonText: \"Ok, got it!\",\r\n\t\t\t\t\t\t\tcustomClass: {\r\n\t\t\t\t\t\t\t\tconfirmButton: \"btn btn-primary\"\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t}\t\t\t\r\n\t\t});\r\n\t}\r\n\r\n\treturn {\r\n\t\t// Public functions\r\n\t\tinit: function () {\r\n\t\t\tform = KTModalOfferADeal.getForm();\r\n\t\t\tstepper = KTModalOfferADeal.getStepperObj();\r\n\t\t\tnextButton = KTModalOfferADeal.getStepper().querySelector('[data-kt-element=\"type-next\"]');\r\n\r\n\t\t\tinitValidation();\r\n\t\t\thandleForm();\r\n\t\t}\r\n\t};\r\n}();\r\n\r\n// Webpack support\r\nif (typeof module !== 'undefined' && typeof module.exports !== 'undefined') {\r\n\tmodule.exports = KTModalOfferADealType;\r\n}", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "window.KTModalOfferADealComplete = require('@/src/js/custom/modals/offer-a-deal/bundle/complete.js');\r\nwindow.KTModalOfferADealDetails = require('@/src/js/custom/modals/offer-a-deal/bundle/details.js');\r\nwindow.KTModalOfferADealFinance = require('@/src/js/custom/modals/offer-a-deal/bundle/finance.js');\r\nwindow.KTModalOfferADealType = require('@/src/js/custom/modals/offer-a-deal/bundle/type.js');\r\nwindow.KTModalOfferADeal = require('@/src/js/custom/modals/offer-a-deal/bundle/main.js');\r\n"], "names": [], "sourceRoot": ""}