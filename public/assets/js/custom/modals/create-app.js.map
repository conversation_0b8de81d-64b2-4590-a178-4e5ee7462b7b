{"version": 3, "file": "js/custom/modals/create-app.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,mEAAmE;AACnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,OAAO;AACP;AACA,KAAK;AACL,KAAK;AACL;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,mCAAmC;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA,MAAM;AACN;AACA,IAAI;AACJ,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC,E", "sources": ["webpack://keenthemes/../../../themes/metronic/html/demo3/src/js/custom/modals/create-app.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTCreateApp = function () {\r\n\t// Elements\r\n\tvar modal;\t\r\n\tvar modalEl;\r\n\r\n\tvar stepper;\r\n\tvar form;\r\n\tvar formSubmitButton;\r\n\tvar formContinueButton;\r\n\r\n\t// Variables\r\n\tvar stepperObj;\r\n\tvar validations = [];\r\n\r\n\t// Private Functions\r\n\tvar initStepper = function () {\r\n\t\t// Initialize Stepper\r\n\t\tstepperObj = new KTStepper(stepper);\r\n\r\n\t\t// Stepper change event(handle hiding submit button for the last step)\r\n\t\tstepperObj.on('kt.stepper.changed', function (stepper) {\r\n\t\t\tif (stepperObj.getCurrentStepIndex() === 4) {\r\n\t\t\t\tformSubmitButton.classList.remove('d-none');\r\n\t\t\t\tformSubmitButton.classList.add('d-inline-block');\r\n\t\t\t\tformContinueButton.classList.add('d-none');\r\n\t\t\t} else if (stepperObj.getCurrentStepIndex() === 5) {\r\n\t\t\t\tformSubmitButton.classList.add('d-none');\r\n\t\t\t\tformContinueButton.classList.add('d-none');\r\n\t\t\t} else {\r\n\t\t\t\tformSubmitButton.classList.remove('d-inline-block');\r\n\t\t\t\tformSubmitButton.classList.remove('d-none');\r\n\t\t\t\tformContinueButton.classList.remove('d-none');\r\n\t\t\t}\r\n\t\t});\r\n\r\n\t\t// Validation before going to next page\r\n\t\tstepperObj.on('kt.stepper.next', function (stepper) {\r\n\t\t\tconsole.log('stepper.next');\r\n\r\n\t\t\t// Validate form before change stepper step\r\n\t\t\tvar validator = validations[stepper.getCurrentStepIndex() - 1]; // get validator for currnt step\r\n\r\n\t\t\tif (validator) {\r\n\t\t\t\tvalidator.validate().then(function (status) {\r\n\t\t\t\t\tconsole.log('validated!');\r\n\r\n\t\t\t\t\tif (status == 'Valid') {\r\n\t\t\t\t\t\tstepper.goNext();\r\n\r\n\t\t\t\t\t\t//KTUtil.scrollTop();\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// Show error message popup. For more info check the plugin's official documentation: https://sweetalert2.github.io/\r\n\t\t\t\t\t\tSwal.fire({\r\n\t\t\t\t\t\t\ttext: \"Sorry, looks like there are some errors detected, please try again.\",\r\n\t\t\t\t\t\t\ticon: \"error\",\r\n\t\t\t\t\t\t\tbuttonsStyling: false,\r\n\t\t\t\t\t\t\tconfirmButtonText: \"Ok, got it!\",\r\n\t\t\t\t\t\t\tcustomClass: {\r\n\t\t\t\t\t\t\t\tconfirmButton: \"btn btn-light\"\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}).then(function () {\r\n\t\t\t\t\t\t\t//KTUtil.scrollTop();\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t} else {\r\n\t\t\t\tstepper.goNext();\r\n\r\n\t\t\t\tKTUtil.scrollTop();\r\n\t\t\t}\r\n\t\t});\r\n\r\n\t\t// Prev event\r\n\t\tstepperObj.on('kt.stepper.previous', function (stepper) {\r\n\t\t\tconsole.log('stepper.previous');\r\n\r\n\t\t\tstepper.goPrevious();\r\n\t\t\tKTUtil.scrollTop();\r\n\t\t});\r\n\r\n\t\tformSubmitButton.addEventListener('click', function (e) {\r\n\t\t\t// Validate form before change stepper step\r\n\t\t\tvar validator = validations[3]; // get validator for last form\r\n\r\n\t\t\tvalidator.validate().then(function (status) {\r\n\t\t\t\tconsole.log('validated!');\r\n\r\n\t\t\t\tif (status == 'Valid') {\r\n\t\t\t\t\t// Prevent default button action\r\n\t\t\t\t\te.preventDefault();\r\n\r\n\t\t\t\t\t// Disable button to avoid multiple click \r\n\t\t\t\t\tformSubmitButton.disabled = true;\r\n\r\n\t\t\t\t\t// Show loading indication\r\n\t\t\t\t\tformSubmitButton.setAttribute('data-kt-indicator', 'on');\r\n\r\n\t\t\t\t\t// Simulate form submission\r\n\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\t// Hide loading indication\r\n\t\t\t\t\t\tformSubmitButton.removeAttribute('data-kt-indicator');\r\n\r\n\t\t\t\t\t\t// Enable button\r\n\t\t\t\t\t\tformSubmitButton.disabled = false;\r\n\r\n\t\t\t\t\t\tstepperObj.goNext();\r\n\t\t\t\t\t\t//KTUtil.scrollTop();\r\n\t\t\t\t\t}, 2000);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tSwal.fire({\r\n\t\t\t\t\t\ttext: \"Sorry, looks like there are some errors detected, please try again.\",\r\n\t\t\t\t\t\ticon: \"error\",\r\n\t\t\t\t\t\tbuttonsStyling: false,\r\n\t\t\t\t\t\tconfirmButtonText: \"Ok, got it!\",\r\n\t\t\t\t\t\tcustomClass: {\r\n\t\t\t\t\t\t\tconfirmButton: \"btn btn-light\"\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}).then(function () {\r\n\t\t\t\t\t\tKTUtil.scrollTop();\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t});\r\n\t}\r\n\r\n\t// Init form inputs\r\n\tvar initForm = function() {\r\n\t\t// Expiry month. For more info, plase visit the official plugin site: https://select2.org/\r\n        $(form.querySelector('[name=\"card_expiry_month\"]')).on('change', function() {\r\n            // Revalidate the field when an option is chosen\r\n            validations[3].revalidateField('card_expiry_month');\r\n        });\r\n\r\n\t\t// Expiry year. For more info, plase visit the official plugin site: https://select2.org/\r\n        $(form.querySelector('[name=\"card_expiry_year\"]')).on('change', function() {\r\n            // Revalidate the field when an option is chosen\r\n            validations[3].revalidateField('card_expiry_year');\r\n        });\r\n\t}\r\n\r\n\tvar initValidation = function () {\r\n\t\t// Init form validation rules. For more info check the FormValidation plugin's official documentation:https://formvalidation.io/\r\n\t\t// Step 1\r\n\t\tvalidations.push(FormValidation.formValidation(\r\n\t\t\tform,\r\n\t\t\t{\r\n\t\t\t\tfields: {\r\n\t\t\t\t\tname: {\r\n\t\t\t\t\t\tvalidators: {\r\n\t\t\t\t\t\t\tnotEmpty: {\r\n\t\t\t\t\t\t\t\tmessage: 'App name is required'\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\tcategory: {\r\n\t\t\t\t\t\tvalidators: {\r\n\t\t\t\t\t\t\tnotEmpty: {\r\n\t\t\t\t\t\t\t\tmessage: 'Category is required'\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\tplugins: {\r\n\t\t\t\t\ttrigger: new FormValidation.plugins.Trigger(),\r\n\t\t\t\t\tbootstrap: new FormValidation.plugins.Bootstrap5({\r\n\t\t\t\t\t\trowSelector: '.fv-row',\r\n                        eleInvalidClass: '',\r\n                        eleValidClass: ''\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t));\r\n\r\n\t\t// Step 2\r\n\t\tvalidations.push(FormValidation.formValidation(\r\n\t\t\tform,\r\n\t\t\t{\r\n\t\t\t\tfields: {\r\n\t\t\t\t\tframework: {\r\n\t\t\t\t\t\tvalidators: {\r\n\t\t\t\t\t\t\tnotEmpty: {\r\n\t\t\t\t\t\t\t\tmessage: 'Framework is required'\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\tplugins: {\r\n\t\t\t\t\ttrigger: new FormValidation.plugins.Trigger(),\r\n\t\t\t\t\t// Bootstrap Framework Integration\r\n\t\t\t\t\tbootstrap: new FormValidation.plugins.Bootstrap5({\r\n\t\t\t\t\t\trowSelector: '.fv-row',\r\n                        eleInvalidClass: '',\r\n                        eleValidClass: ''\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t));\r\n\r\n\t\t// Step 3\r\n\t\tvalidations.push(FormValidation.formValidation(\r\n\t\t\tform,\r\n\t\t\t{\r\n\t\t\t\tfields: {\r\n\t\t\t\t\tdbname: {\r\n\t\t\t\t\t\tvalidators: {\r\n\t\t\t\t\t\t\tnotEmpty: {\r\n\t\t\t\t\t\t\t\tmessage: 'Database name is required'\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\tdbengine: {\r\n\t\t\t\t\t\tvalidators: {\r\n\t\t\t\t\t\t\tnotEmpty: {\r\n\t\t\t\t\t\t\t\tmessage: 'Database engine is required'\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\tplugins: {\r\n\t\t\t\t\ttrigger: new FormValidation.plugins.Trigger(),\r\n\t\t\t\t\t// Bootstrap Framework Integration\r\n\t\t\t\t\tbootstrap: new FormValidation.plugins.Bootstrap5({\r\n\t\t\t\t\t\trowSelector: '.fv-row',\r\n                        eleInvalidClass: '',\r\n                        eleValidClass: ''\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t));\r\n\r\n\t\t// Step 4\r\n\t\tvalidations.push(FormValidation.formValidation(\r\n\t\t\tform,\r\n\t\t\t{\r\n\t\t\t\tfields: {\r\n\t\t\t\t\t'card_name': {\r\n\t\t\t\t\t\tvalidators: {\r\n\t\t\t\t\t\t\tnotEmpty: {\r\n\t\t\t\t\t\t\t\tmessage: 'Name on card is required'\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\t'card_number': {\r\n\t\t\t\t\t\tvalidators: {\r\n\t\t\t\t\t\t\tnotEmpty: {\r\n\t\t\t\t\t\t\t\tmessage: 'Card member is required'\r\n\t\t\t\t\t\t\t},\r\n                            creditCard: {\r\n                                message: 'Card number is not valid'\r\n                            }\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\t'card_expiry_month': {\r\n\t\t\t\t\t\tvalidators: {\r\n\t\t\t\t\t\t\tnotEmpty: {\r\n\t\t\t\t\t\t\t\tmessage: 'Month is required'\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\t'card_expiry_year': {\r\n\t\t\t\t\t\tvalidators: {\r\n\t\t\t\t\t\t\tnotEmpty: {\r\n\t\t\t\t\t\t\t\tmessage: 'Year is required'\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\t'card_cvv': {\r\n\t\t\t\t\t\tvalidators: {\r\n\t\t\t\t\t\t\tnotEmpty: {\r\n\t\t\t\t\t\t\t\tmessage: 'CVV is required'\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tdigits: {\r\n\t\t\t\t\t\t\t\tmessage: 'CVV must contain only digits'\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tstringLength: {\r\n\t\t\t\t\t\t\t\tmin: 3,\r\n\t\t\t\t\t\t\t\tmax: 4,\r\n\t\t\t\t\t\t\t\tmessage: 'CVV must contain 3 to 4 digits only'\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\r\n\t\t\t\tplugins: {\r\n\t\t\t\t\ttrigger: new FormValidation.plugins.Trigger(),\r\n\t\t\t\t\t// Bootstrap Framework Integration\r\n\t\t\t\t\tbootstrap: new FormValidation.plugins.Bootstrap5({\r\n\t\t\t\t\t\trowSelector: '.fv-row',\r\n                        eleInvalidClass: '',\r\n                        eleValidClass: ''\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t));\r\n\t}\r\n\r\n\treturn {\r\n\t\t// Public Functions\r\n\t\tinit: function () {\r\n\t\t\t// Elements\r\n\t\t\tmodalEl = document.querySelector('#kt_modal_create_app');\r\n\r\n\t\t\tif (!modalEl) {\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\tmodal = new bootstrap.Modal(modalEl);\r\n\r\n\t\t\tstepper = document.querySelector('#kt_modal_create_app_stepper');\r\n\t\t\tform = document.querySelector('#kt_modal_create_app_form');\r\n\t\t\tformSubmitButton = stepper.querySelector('[data-kt-stepper-action=\"submit\"]');\r\n\t\t\tformContinueButton = stepper.querySelector('[data-kt-stepper-action=\"next\"]');\r\n\r\n\t\t\tinitStepper();\r\n\t\t\tinitForm();\r\n\t\t\tinitValidation();\r\n\t\t}\r\n\t};\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function() {\r\n    KTCreateApp.init();\r\n});"], "names": [], "sourceRoot": ""}