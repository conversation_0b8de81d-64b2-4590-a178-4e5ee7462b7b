{"version": 3, "file": "js/custom/modals/create-api-key.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA,QAAQ;AACR;AACA,wBAAwB;AACxB,OAAO;AACP,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,KAAK;AACL;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA,mBAAmB;AACnB,mBAAmB;AACnB,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA,IAAI;AACJ,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC,E", "sources": ["webpack://keenthemes/../../../themes/metronic/html/demo3/src/js/custom/modals/create-api-key.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTModalCreateApiKey = function () {\r\n\tvar submitButton;\r\n\tvar cancelButton;\r\n\tvar validator;\r\n\tvar form;\r\n\tvar modal;\r\n\tvar modalEl;\r\n\r\n\t// Init form inputs\r\n\tvar initForm = function() {\r\n\t\t// Team assign. For more info, plase visit the official plugin site: https://select2.org/\r\n        $(form.querySelector('[name=\"category\"]')).on('change', function() {\r\n            // Revalidate the field when an option is chosen\r\n            validator.revalidateField('category');\r\n        });\r\n\t}\r\n\r\n\t// Handle form validation and submittion\r\n\tvar handleForm = function() {\r\n\t\t// Stepper custom navigation\r\n\r\n\t\t// Init form validation rules. For more info check the FormValidation plugin's official documentation:https://formvalidation.io/\r\n\t\tvalidator = FormValidation.formValidation(\r\n\t\t\tform,\r\n\t\t\t{\r\n\t\t\t\tfields: {\r\n\t\t\t\t\t'name': {\r\n\t\t\t\t\t\tvalidators: {\r\n\t\t\t\t\t\t\tnotEmpty: {\r\n\t\t\t\t\t\t\t\tmessage: 'API name is required'\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\t'description': {\r\n\t\t\t\t\t\tvalidators: {\r\n\t\t\t\t\t\t\tnotEmpty: {\r\n\t\t\t\t\t\t\t\tmessage: 'Description is required'\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\t'category': {\r\n\t\t\t\t\t\tvalidators: {\r\n\t\t\t\t\t\t\tnotEmpty: {\r\n\t\t\t\t\t\t\t\tmessage: 'Country is required'\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\t'method': {\r\n\t\t\t\t\t\tvalidators: {\r\n\t\t\t\t\t\t\tnotEmpty: {\r\n\t\t\t\t\t\t\t\tmessage: 'API method is required'\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\tplugins: {\r\n\t\t\t\t\ttrigger: new FormValidation.plugins.Trigger(),\r\n\t\t\t\t\tbootstrap: new FormValidation.plugins.Bootstrap5({\r\n\t\t\t\t\t\trowSelector: '.fv-row',\r\n                        eleInvalidClass: '',\r\n                        eleValidClass: ''\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t);\r\n\r\n\t\t// Action buttons\r\n\t\tsubmitButton.addEventListener('click', function (e) {\r\n\t\t\te.preventDefault();\r\n\r\n\t\t\t// Validate form before submit\r\n\t\t\tif (validator) {\r\n\t\t\t\tvalidator.validate().then(function (status) {\r\n\t\t\t\t\tconsole.log('validated!');\r\n\r\n\t\t\t\t\tif (status == 'Valid') {\r\n\t\t\t\t\t\tsubmitButton.setAttribute('data-kt-indicator', 'on');\r\n\r\n\t\t\t\t\t\t// Disable button to avoid multiple click \r\n\t\t\t\t\t\tsubmitButton.disabled = true;\r\n\r\n\t\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\t\tsubmitButton.removeAttribute('data-kt-indicator');\r\n\r\n\t\t\t\t\t\t\t// Enable button\r\n\t\t\t\t\t\t\tsubmitButton.disabled = false;\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\tSwal.fire({\r\n\t\t\t\t\t\t\t\ttext: \"Form has been successfully submitted!\",\r\n\t\t\t\t\t\t\t\ticon: \"success\",\r\n\t\t\t\t\t\t\t\tbuttonsStyling: false,\r\n\t\t\t\t\t\t\t\tconfirmButtonText: \"Ok, got it!\",\r\n\t\t\t\t\t\t\t\tcustomClass: {\r\n\t\t\t\t\t\t\t\t\tconfirmButton: \"btn btn-primary\"\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}).then(function (result) {\r\n\t\t\t\t\t\t\t\tif (result.isConfirmed) {\r\n\t\t\t\t\t\t\t\t\tmodal.hide();\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t});\r\n\r\n\t\t\t\t\t\t\t//form.submit(); // Submit form\r\n\t\t\t\t\t\t}, 2000);   \t\t\t\t\t\t\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// Show error popuo. For more info check the plugin's official documentation: https://sweetalert2.github.io/\r\n\t\t\t\t\t\tSwal.fire({\r\n\t\t\t\t\t\t\ttext: \"Sorry, looks like there are some errors detected, please try again.\",\r\n\t\t\t\t\t\t\ticon: \"error\",\r\n\t\t\t\t\t\t\tbuttonsStyling: false,\r\n\t\t\t\t\t\t\tconfirmButtonText: \"Ok, got it!\",\r\n\t\t\t\t\t\t\tcustomClass: {\r\n\t\t\t\t\t\t\t\tconfirmButton: \"btn btn-primary\"\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t});\r\n\r\n\t\tcancelButton.addEventListener('click', function (e) {\r\n\t\t\te.preventDefault();\r\n\r\n\t\t\t// Show confirmation popup. For more info check the plugin's official documentation: https://sweetalert2.github.io/\r\n\t\t\tSwal.fire({\r\n\t\t\t\ttext: \"Are you sure you would like to cancel?\",\r\n\t\t\t\ticon: \"warning\",\r\n\t\t\t\tshowCancelButton: true,\r\n\t\t\t\tbuttonsStyling: false,\r\n\t\t\t\tconfirmButtonText: \"Yes, cancel it!\",\r\n\t\t\t\tcancelButtonText: \"No, return\",\r\n\t\t\t\tcustomClass: {\r\n\t\t\t\t\tconfirmButton: \"btn btn-primary\",\r\n\t\t\t\t\tcancelButton: \"btn btn-active-light\"\r\n\t\t\t\t}\r\n\t\t\t}).then(function (result) {\r\n\t\t\t\tif (result.value) {\r\n\t\t\t\t\tform.reset(); // Reset form\t\r\n\t\t\t\t\tmodal.hide(); // Hide modal\t\t\t\t\r\n\t\t\t\t} else if (result.dismiss === 'cancel') {\r\n\t\t\t\t\t// Show success message. \r\n\t\t\t\t\tSwal.fire({\r\n\t\t\t\t\t\ttext: \"Your form has not been cancelled!.\",\r\n\t\t\t\t\t\ticon: \"error\",\r\n\t\t\t\t\t\tbuttonsStyling: false,\r\n\t\t\t\t\t\tconfirmButtonText: \"Ok, got it!\",\r\n\t\t\t\t\t\tcustomClass: {\r\n\t\t\t\t\t\t\tconfirmButton: \"btn btn-primary\",\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t});\r\n\t}\r\n\r\n\treturn {\r\n\t\t// Public functions\r\n\t\tinit: function () {\r\n\t\t\t// Elements\r\n\t\t\tmodalEl = document.querySelector('#kt_modal_create_api_key');\r\n\r\n\t\t\tif (!modalEl) {\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\tmodal = new bootstrap.Modal(modalEl);\r\n\r\n\t\t\tform = document.querySelector('#kt_modal_create_api_key_form');\r\n\t\t\tsubmitButton = document.getElementById('kt_modal_create_api_key_submit');\r\n\t\t\tcancelButton = document.getElementById('kt_modal_create_api_key_cancel');\r\n\r\n\t\t\tinitForm();\r\n\t\t\thandleForm();\r\n\t\t}\r\n\t};\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function () {\r\n\tKTModalCreateApiKey.init();\r\n});"], "names": [], "sourceRoot": ""}