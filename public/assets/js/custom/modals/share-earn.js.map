{"version": 3, "file": "js/custom/modals/share-earn.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,UAAU;AACvB;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC", "sources": ["webpack://keenthemes/../../../themes/metronic/html/demo3/src/js/custom/modals/share-earn.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTModalShareEarn = function () {\r\n    // Private functions\r\n    var handleForm = function() {\r\n        var button = document.querySelector('#kt_share_earn_link_copy_button');\r\n        var input = document.querySelector('#kt_share_earn_link_input');\r\n        var clipboard = new ClipboardJS(button);\r\n\r\n        if (!clipboard) {\r\n            return;\r\n        }\r\n\r\n        //  Copy text to clipboard. For more info check the plugin's documentation: https://clipboardjs.com/\r\n        clipboard.on('success', function(e) {\r\n            var buttonCaption = button.innerHTML;\r\n            //Add bgcolor\r\n            input.classList.add('bg-success');\r\n            input.classList.add('text-inverse-success');\r\n\r\n            button.innerHTML = 'Copied!';\r\n\r\n            setTimeout(function() {\r\n                button.innerHTML = buttonCaption;\r\n\r\n                // Remove bgcolor\r\n                input.classList.remove('bg-success'); \r\n                input.classList.remove('text-inverse-success'); \r\n            }, 3000);  // 3seconds\r\n\r\n            e.clearSelection();\r\n        });\r\n    }\r\n\r\n    // Public methods\r\n    return {\r\n        init: function () {\r\n            handleForm();\r\n        }\r\n    }\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function() {\r\n    KTModalShareEarn.init();\r\n});\r\n"], "names": [], "sourceRoot": ""}