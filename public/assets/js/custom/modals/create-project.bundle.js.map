{"version": 3, "file": "/js/custom/modals/create-project.bundle.js", "mappings": ";;;;;;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,KAAK;AACL;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA,IAAI,KAA6B;AACjC;AACA;;;;;;;;;;;;AC3Ia;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA,IAAI,KAA6B;AACjC;AACA;;;;;;;;;;;;AC/Ba;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA,IAAI,KAA6B;AACjC;AACA;;;;;;;;;;;;AC5Ea;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA,IAAI,KAA6B;AACjC;AACA;;;;;;;;;;;;ACzDa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,KAAK;AACL;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA,IAAI,KAA6B;AACjC;AACA;;;;;;;;;;;;AC1Ka;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,KAAK;AACL;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA,IAAI,KAA6B;AACjC;AACA;;;;;;;;;;;AC/Ka;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA,IAAI,KAA6B;AACjC;AACA;;;;;;;;;;;ACxDa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,KAAK;AACL;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA,IAAI,KAA6B;AACjC;AACA;;;;;;;UCvGA;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;;;;;;ACtBA,oCAAoC,mBAAO,CAAC,wJAAwD;AACpG,sCAAsC,mBAAO,CAAC,4JAA0D;AACxG,mCAAmC,mBAAO,CAAC,sJAAuD;AAClG,sCAAsC,mBAAO,CAAC,4JAA0D;AACxG,qCAAqC,mBAAO,CAAC,0JAAyD;AACtG,kCAAkC,mBAAO,CAAC,oJAAsD;AAChG,kCAAkC,mBAAO,CAAC,oJAAsD;AAChG,8BAA8B,mBAAO,CAAC,oJAAsD", "sources": ["webpack://keenthemes/../../../themes/metronic/html/demo3/src/js/custom/modals/create-project/bundle/budget.js", "webpack://keenthemes/../../../themes/metronic/html/demo3/src/js/custom/modals/create-project/bundle/complete.js", "webpack://keenthemes/../../../themes/metronic/html/demo3/src/js/custom/modals/create-project/bundle/files.js", "webpack://keenthemes/../../../themes/metronic/html/demo3/src/js/custom/modals/create-project/bundle/main.js", "webpack://keenthemes/../../../themes/metronic/html/demo3/src/js/custom/modals/create-project/bundle/settings.js", "webpack://keenthemes/../../../themes/metronic/html/demo3/src/js/custom/modals/create-project/bundle/targets.js", "webpack://keenthemes/../../../themes/metronic/html/demo3/src/js/custom/modals/create-project/bundle/team.js", "webpack://keenthemes/../../../themes/metronic/html/demo3/src/js/custom/modals/create-project/bundle/type.js", "webpack://keenthemes/webpack/bootstrap", "webpack://keenthemes/../../../themes/metronic/html/tools/webpack/js/custom/modals/create-project.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTModalCreateProjectBudget = function () {\r\n\t// Variables\r\n\tvar nextButton;\r\n\tvar previousButton;\r\n\tvar validator;\r\n\tvar form;\r\n\tvar stepper;\r\n\r\n\t// Private functions\r\n\tvar initValidation = function() {\r\n\t\t// Init form validation rules. For more info check the FormValidation plugin's official documentation:https://formvalidation.io/\r\n\t\tvalidator = FormValidation.formValidation(\r\n\t\t\tform,\r\n\t\t\t{\r\n\t\t\t\tfields: {\r\n\t\t\t\t\t'budget_setup': {\r\n\t\t\t\t\t\tvalidators: {\r\n\t\t\t\t\t\t\tnotEmpty: {\r\n\t\t\t\t\t\t\t\tmessage: 'Budget amount is required'\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tcallback: {\r\n\t\t\t\t\t\t\t\tmessage: 'The budget amount must be greater than $100',\r\n\t\t\t\t\t\t\t\tcallback: function(input) {\r\n\t\t\t\t\t\t\t\t\tvar currency = input.value;\r\n\t\t\t\t\t\t\t\t\tcurrency = currency.replace(/[$,]+/g,\"\");\r\n\r\n\t\t\t\t\t\t\t\t\tif (parseFloat(currency) < 100) {\r\n\t\t\t\t\t\t\t\t\t\treturn false;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\t'budget_usage': {\r\n\t\t\t\t\t\tvalidators: {\r\n\t\t\t\t\t\t\tnotEmpty: {\r\n\t\t\t\t\t\t\t\tmessage: 'Budget usage type is required'\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\t'budget_allow': {\r\n\t\t\t\t\t\tvalidators: {\r\n\t\t\t\t\t\t\tnotEmpty: {\r\n\t\t\t\t\t\t\t\tmessage: 'Allowing budget is required'\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\t\r\n\t\t\t\tplugins: {\r\n\t\t\t\t\ttrigger: new FormValidation.plugins.Trigger(),\r\n\t\t\t\t\tbootstrap: new FormValidation.plugins.Bootstrap5({\r\n\t\t\t\t\t\trowSelector: '.fv-row',\r\n                        eleInvalidClass: '',\r\n                        eleValidClass: ''\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t);\r\n\r\n\t\t// Revalidate on change\r\n\t\tKTDialer.getInstance(form.querySelector('#kt_modal_create_project_budget_setup')).on('kt.dialer.changed', function() {\r\n\t\t\t// Revalidate the field when an option is chosen\r\n            validator.revalidateField('budget_setup');\r\n\t\t});\r\n\t}\r\n\r\n\tvar handleForm = function() {\r\n\t\tnextButton.addEventListener('click', function (e) {\r\n\t\t\t// Prevent default button action\r\n\t\t\te.preventDefault();\r\n\r\n\t\t\t// Disable button to avoid multiple click \r\n\t\t\tnextButton.disabled = true;\r\n\r\n\t\t\t// Validate form before submit\r\n\t\t\tif (validator) {\r\n\t\t\t\tvalidator.validate().then(function (status) {\r\n\t\t\t\t\tconsole.log('validated!');\r\n\r\n\t\t\t\t\tif (status == 'Valid') {\r\n\t\t\t\t\t\t// Show loading indication\r\n\t\t\t\t\t\tnextButton.setAttribute('data-kt-indicator', 'on');\r\n\r\n\t\t\t\t\t\t// Simulate form submission\r\n\t\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\t\t// Simulate form submission\r\n\t\t\t\t\t\t\tnextButton.removeAttribute('data-kt-indicator');\r\n\r\n\t\t\t\t\t\t\t// Enable button\r\n\t\t\t\t\t\t\tnextButton.disabled = false;\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t// Go to next step\r\n\t\t\t\t\t\t\tstepper.goNext();\r\n\t\t\t\t\t\t}, 1500);   \t\t\t\t\t\t\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// Enable button\r\n\t\t\t\t\t\tnextButton.disabled = false;\r\n\r\n\t\t\t\t\t\t// Show popup warning. For more info check the plugin's official documentation: https://sweetalert2.github.io/\r\n\t\t\t\t\t\tSwal.fire({\r\n\t\t\t\t\t\t\ttext: \"Sorry, looks like there are some errors detected, please try again.\",\r\n\t\t\t\t\t\t\ticon: \"error\",\r\n\t\t\t\t\t\t\tbuttonsStyling: false,\r\n\t\t\t\t\t\t\tconfirmButtonText: \"Ok, got it!\",\r\n\t\t\t\t\t\t\tcustomClass: {\r\n\t\t\t\t\t\t\t\tconfirmButton: \"btn btn-primary\"\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t}\t\t\t\r\n\t\t});\r\n\r\n\t\tpreviousButton.addEventListener('click', function () {\r\n\t\t\tstepper.goPrevious();\r\n\t\t});\r\n\t}\r\n\r\n\treturn {\r\n\t\t// Public functions\r\n\t\tinit: function () {\r\n\t\t\tform = KTModalCreateProject.getForm();\r\n\t\t\tstepper = KTModalCreateProject.getStepperObj();\r\n\t\t\tnextButton = KTModalCreateProject.getStepper().querySelector('[data-kt-element=\"budget-next\"]');\r\n\t\t\tpreviousButton = KTModalCreateProject.getStepper().querySelector('[data-kt-element=\"budget-previous\"]');\r\n\r\n\t\t\tinitValidation();\r\n\t\t\thandleForm();\r\n\t\t}\r\n\t};\r\n}();\r\n\r\n// Webpack support\r\nif (typeof module !== 'undefined' && typeof module.exports !== 'undefined') {\r\n\tmodule.exports = KTModalCreateProjectBudget;\r\n}\r\n", "\"use strict\";\r\n\r\n// Class definition\r\nvar KTModalCreateProjectComplete = function () {\r\n\t// Variables\r\n\tvar startButton;\r\n\tvar form;\r\n\tvar stepper;\r\n\r\n\t// Private functions\r\n\tvar handleForm = function() {\r\n\t\tstartButton.addEventListener('click', function () {\r\n\t\t\tstepper.goTo(1);\r\n\t\t});\r\n\t}\r\n\r\n\treturn {\r\n\t\t// Public functions\r\n\t\tinit: function () {\r\n\t\t\tform = KTModalCreateProject.getForm();\r\n\t\t\tstepper = KTModalCreateProject.getStepperObj();\r\n\t\t\tstartButton = KTModalCreateProject.getStepper().querySelector('[data-kt-element=\"complete-start\"]');\r\n\r\n\t\t\thandleForm();\r\n\t\t}\r\n\t};\r\n}();\r\n\r\n// Webpack support\r\nif (typeof module !== 'undefined' && typeof module.exports !== 'undefined') {\r\n\tmodule.exports = KTModalCreateProjectComplete;\r\n}\r\n", "\"use strict\";\r\n\r\n// Class definition\r\nvar KTModalCreateProjectFiles = function () {\r\n\t// Variables\r\n\tvar nextButton;\r\n\tvar previousButton;\r\n\tvar form;\r\n\tvar stepper;\r\n\r\n\t// Private functions\r\n\tvar initForm = function() {\r\n\t\t// Project logo\r\n\t\t// For more info about Dropzone plugin visit:  https://www.dropzonejs.com/#usage\r\n\t\tvar myDropzone = new Dropzone(\"#kt_modal_create_project_files_upload\", { \r\n\t\t\turl: \"https://keenthemes.com/scripts/void.php\", // Set the url for your upload script location\r\n            paramName: \"file\", // The name that will be used to transfer the file\r\n            maxFiles: 10,\r\n            maxFilesize: 10, // MB\r\n            addRemoveLinks: true,\r\n            accept: function(file, done) {\r\n                if (file.name == \"justinbieber.jpg\") {\r\n                    done(\"Naha, you don't.\");\r\n                } else {\r\n                    done();\r\n                }\r\n            }\r\n\t\t});  \r\n\t}\r\n\r\n\tvar handleForm = function() {\r\n\t\tnextButton.addEventListener('click', function (e) {\r\n\t\t\t// Prevent default button action\r\n\t\t\te.preventDefault();\r\n\r\n\t\t\t// Disable button to avoid multiple click \r\n\t\t\tnextButton.disabled = true;\r\n\r\n\t\t\t// Show loading indication\r\n\t\t\tnextButton.setAttribute('data-kt-indicator', 'on');\r\n\r\n\t\t\t// Simulate form submission\r\n\t\t\tsetTimeout(function() {\r\n\t\t\t\t// Hide loading indication\r\n\t\t\t\tnextButton.removeAttribute('data-kt-indicator');\r\n\r\n\t\t\t\t// Enable button\r\n\t\t\t\tnextButton.disabled = false;\r\n\t\t\t\t\r\n\t\t\t\t// Go to next step\r\n\t\t\t\tstepper.goNext();\r\n\t\t\t}, 1500); \t\t\r\n\t\t});\r\n\r\n\t\tpreviousButton.addEventListener('click', function () {\r\n\t\t\tstepper.goPrevious();\r\n\t\t});\r\n\t}\r\n\r\n\treturn {\r\n\t\t// Public functions\r\n\t\tinit: function () {\r\n\t\t\tform = KTModalCreateProject.getForm();\r\n\t\t\tstepper = KTModalCreateProject.getStepperObj();\r\n\t\t\tnextButton = KTModalCreateProject.getStepper().querySelector('[data-kt-element=\"files-next\"]');\r\n\t\t\tpreviousButton = KTModalCreateProject.getStepper().querySelector('[data-kt-element=\"files-previous\"]');\r\n\r\n\t\t\tinitForm();\r\n\t\t\thandleForm();\r\n\t\t}\r\n\t};\r\n}();\r\n\r\n// Webpack support\r\nif (typeof module !== 'undefined' && typeof module.exports !== 'undefined') {\r\n\tmodule.exports = KTModalCreateProjectFiles;\r\n}\r\n", "\"use strict\";\r\n\r\n// Class definition\r\nvar KTModalCreateProject = function () {\r\n\t// Private variables\r\n\tvar stepper;\r\n\tvar stepperObj;\r\n\tvar form;\t\r\n\r\n\t// Private functions\r\n\tvar initStepper = function () {\r\n\t\t// Initialize Stepper\r\n\t\tstepperObj = new KTStepper(stepper);\r\n\t}\r\n\r\n\treturn {\r\n\t\t// Public functions\r\n\t\tinit: function () {\r\n\t\t\tstepper = document.querySelector('#kt_modal_create_project_stepper');\r\n\t\t\tform = document.querySelector('#kt_modal_create_project_form');\r\n\r\n\t\t\tinitStepper();\r\n\t\t},\r\n\r\n\t\tgetStepperObj: function () {\r\n\t\t\treturn stepperObj;\r\n\t\t},\r\n\r\n\t\tgetStepper: function () {\r\n\t\t\treturn stepper;\r\n\t\t},\r\n\t\t\r\n\t\tgetForm: function () {\r\n\t\t\treturn form;\r\n\t\t}\r\n\t};\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function () {\r\n\tif (!document.querySelector('#kt_modal_create_project')) {\r\n\t\treturn;\r\n\t}\r\n\r\n\tKTModalCreateProject.init();\r\n\tKTModalCreateProjectType.init();\r\n\tKTModalCreateProjectBudget.init();\r\n\tKTModalCreateProjectSettings.init();\r\n\tKTModalCreateProjectTeam.init();\r\n\tKTModalCreateProjectTargets.init();\r\n\tKTModalCreateProjectFiles.init();\r\n\tKTModalCreateProjectComplete.init();\r\n});\r\n\r\n// Webpack support\r\nif (typeof module !== 'undefined' && typeof module.exports !== 'undefined') {\r\n\tmodule.exports = KTModalCreateProject;\r\n}\r\n", "\"use strict\";\r\n\r\n// Class definition\r\nvar KTModalCreateProjectSettings = function () {\r\n\t// Variables\r\n\tvar nextButton;\r\n\tvar previousButton;\r\n\tvar validator;\r\n\tvar form;\r\n\tvar stepper;\r\n\r\n\t// Private functions\r\n\tvar initForm = function() {\r\n\t\t// Project logo\r\n\t\t// For more info about Dropzone plugin visit:  https://www.dropzonejs.com/#usage\r\n\t\tvar myDropzone = new Dropzone(\"#kt_modal_create_project_settings_logo\", { \r\n\t\t\turl: \"https://keenthemes.com/scripts/void.php\", // Set the url for your upload script location\r\n            paramName: \"file\", // The name that will be used to transfer the file\r\n            maxFiles: 10,\r\n            maxFilesize: 10, // MB\r\n            addRemoveLinks: true,\r\n            accept: function(file, done) {\r\n                if (file.name == \"justinbieber.jpg\") {\r\n                    done(\"Naha, you don't.\");\r\n                } else {\r\n                    done();\r\n                }\r\n            }\r\n\t\t});  \r\n\r\n\t\t// Due date. For more info, please visit the official plugin site: https://flatpickr.js.org/\r\n\t\tvar releaseDate = $(form.querySelector('[name=\"settings_release_date\"]'));\r\n\t\treleaseDate.flatpickr({\r\n\t\t\tenableTime: true,\r\n\t\t\tdateFormat: \"d, M Y, H:i\",\r\n\t\t});\r\n\r\n\t\t// Expiry year. For more info, plase visit the official plugin site: https://select2.org/\r\n        $(form.querySelector('[name=\"settings_customer\"]')).on('change', function() {\r\n            // Revalidate the field when an option is chosen\r\n            validator.revalidateField('settings_customer');\r\n        });\r\n\t}\r\n\r\n\tvar initValidation = function() {\r\n\t\t// Init form validation rules. For more info check the FormValidation plugin's official documentation:https://formvalidation.io/\r\n\t\tvalidator = FormValidation.formValidation(\r\n\t\t\tform,\r\n\t\t\t{\r\n\t\t\t\tfields: {\r\n\t\t\t\t\t'settings_name': {\r\n\t\t\t\t\t\tvalidators: {\r\n\t\t\t\t\t\t\tnotEmpty: {\r\n\t\t\t\t\t\t\t\tmessage: 'Project name is required'\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\t'settings_customer': {\r\n\t\t\t\t\t\tvalidators: {\r\n\t\t\t\t\t\t\tnotEmpty: {\r\n\t\t\t\t\t\t\t\tmessage: 'Customer is required'\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\t'settings_description': {\r\n\t\t\t\t\t\tvalidators: {\r\n\t\t\t\t\t\t\tnotEmpty: {\r\n\t\t\t\t\t\t\t\tmessage: 'Description is required'\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\t'settings_release_date': {\r\n\t\t\t\t\t\tvalidators: {\r\n\t\t\t\t\t\t\tnotEmpty: {\r\n\t\t\t\t\t\t\t\tmessage: 'Release date is required'\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\t'settings_notifications[]': {\r\n\t\t\t\t\t\tvalidators: {\r\n\t\t\t\t\t\t\tnotEmpty: {\r\n\t\t\t\t\t\t\t\tmessage: 'Notifications are required'\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\t\r\n\t\t\t\tplugins: {\r\n\t\t\t\t\ttrigger: new FormValidation.plugins.Trigger(),\r\n\t\t\t\t\tbootstrap: new FormValidation.plugins.Bootstrap5({\r\n\t\t\t\t\t\trowSelector: '.fv-row',\r\n                        eleInvalidClass: '',\r\n                        eleValidClass: ''\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t);\r\n\t}\r\n\r\n\tvar handleForm = function() {\r\n\t\tnextButton.addEventListener('click', function (e) {\r\n\t\t\t// Prevent default button action\r\n\t\t\te.preventDefault();\r\n\r\n\t\t\t// Disable button to avoid multiple click \r\n\t\t\tnextButton.disabled = true;\r\n\r\n\t\t\t// Validate form before submit\r\n\t\t\tif (validator) {\r\n\t\t\t\tvalidator.validate().then(function (status) {\r\n\t\t\t\t\tconsole.log('validated!');\r\n\r\n\t\t\t\t\tif (status == 'Valid') {\r\n\t\t\t\t\t\t// Show loading indication\r\n\t\t\t\t\t\tnextButton.setAttribute('data-kt-indicator', 'on');\r\n\r\n\t\t\t\t\t\t// Simulate form submission\r\n\t\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\t\t// Simulate form submission\r\n\t\t\t\t\t\t\tnextButton.removeAttribute('data-kt-indicator');\r\n\r\n\t\t\t\t\t\t\t// Enable button\r\n\t\t\t\t\t\t\tnextButton.disabled = false;\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t// Go to next step\r\n\t\t\t\t\t\t\tstepper.goNext();\r\n\t\t\t\t\t\t}, 1500);   \t\t\t\t\t\t\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// Enable button\r\n\t\t\t\t\t\tnextButton.disabled = false;\r\n\r\n\t\t\t\t\t\t// Show popup warning. For more info check the plugin's official documentation: https://sweetalert2.github.io/\r\n\t\t\t\t\t\tSwal.fire({\r\n\t\t\t\t\t\t\ttext: \"Sorry, looks like there are some errors detected, please try again.\",\r\n\t\t\t\t\t\t\ticon: \"error\",\r\n\t\t\t\t\t\t\tbuttonsStyling: false,\r\n\t\t\t\t\t\t\tconfirmButtonText: \"Ok, got it!\",\r\n\t\t\t\t\t\t\tcustomClass: {\r\n\t\t\t\t\t\t\t\tconfirmButton: \"btn btn-primary\"\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t}\t\t\t\r\n\t\t});\r\n\r\n\t\tpreviousButton.addEventListener('click', function () {\r\n\t\t\t// Go to previous step\r\n\t\t\tstepper.goPrevious();\r\n\t\t});\r\n\t}\r\n\r\n\treturn {\r\n\t\t// Public functions\r\n\t\tinit: function () {\r\n\t\t\tform = KTModalCreateProject.getForm();\r\n\t\t\tstepper = KTModalCreateProject.getStepperObj();\r\n\t\t\tnextButton = KTModalCreateProject.getStepper().querySelector('[data-kt-element=\"settings-next\"]');\r\n\t\t\tpreviousButton = KTModalCreateProject.getStepper().querySelector('[data-kt-element=\"settings-previous\"]');\r\n\r\n\t\t\tinitForm();\r\n\t\t\tinitValidation();\r\n\t\t\thandleForm();\r\n\t\t}\r\n\t};\r\n}();\r\n\r\n// Webpack support\r\nif (typeof module !== 'undefined' && typeof module.exports !== 'undefined') {\r\n\tmodule.exports = KTModalCreateProjectSettings;\r\n}\r\n", "\"use strict\";\r\n\r\n// Class definition\r\nvar KTModalCreateProjectTargets = function () {\r\n\t// Variables\r\n\tvar nextButton;\r\n\tvar previousButton;\r\n\tvar validator;\r\n\tvar form;\r\n\tvar stepper;\r\n\r\n\t// Private functions\r\n\tvar initForm = function() {\r\n\t\t// Tags. For more info, please visit the official plugin site: https://yaireo.github.io/tagify/\r\n\t\tvar tags = new Tagify(form.querySelector('[name=\"target_tags\"]'), {\r\n\t\t\twhitelist: [\"Important\", \"Urgent\", \"High\", \"Medium\", \"Low\"],\r\n\t\t\tmaxTags: 5,\r\n\t\t\tdropdown: {\r\n\t\t\t\tmaxItems: 10,           // <- mixumum allowed rendered suggestions\r\n\t\t\t\tenabled: 0,             // <- show suggestions on focus\r\n\t\t\t\tcloseOnSelect: false    // <- do not hide the suggestions dropdown once an item has been selected\r\n\t\t\t}\r\n\t\t});\r\n\t\ttags.on(\"change\", function(){\r\n\t\t\t// Revalidate the field when an option is chosen\r\n            validator.revalidateField('tags');\r\n\t\t});\r\n\r\n\t\t// Due date. For more info, please visit the official plugin site: https://flatpickr.js.org/\r\n\t\tvar dueDate = $(form.querySelector('[name=\"target_due_date\"]'));\r\n\t\tdueDate.flatpickr({\r\n\t\t\tenableTime: true,\r\n\t\t\tdateFormat: \"d, M Y, H:i\",\r\n\t\t});\r\n\r\n\t\t// Expiry year. For more info, plase visit the official plugin site: https://select2.org/\r\n        $(form.querySelector('[name=\"target_assign\"]')).on('change', function() {\r\n            // Revalidate the field when an option is chosen\r\n            validator.revalidateField('target_assign');\r\n        });\r\n\t}\r\n\r\n\tvar initValidation = function() {\r\n\t\t// Init form validation rules. For more info check the FormValidation plugin's official documentation:https://formvalidation.io/\r\n\t\tvalidator = FormValidation.formValidation(\r\n\t\t\tform,\r\n\t\t\t{\r\n\t\t\t\tfields: {\r\n\t\t\t\t\t'target_title': {\r\n\t\t\t\t\t\tvalidators: {\r\n\t\t\t\t\t\t\tnotEmpty: {\r\n\t\t\t\t\t\t\t\tmessage: 'Target title is required'\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\t'target_assign': {\r\n\t\t\t\t\t\tvalidators: {\r\n\t\t\t\t\t\t\tnotEmpty: {\r\n\t\t\t\t\t\t\t\tmessage: 'Customer is required'\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\t'target_due_date': {\r\n\t\t\t\t\t\tvalidators: {\r\n\t\t\t\t\t\t\tnotEmpty: {\r\n\t\t\t\t\t\t\t\tmessage: 'Due date is required'\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\t'target_tags': {\r\n\t\t\t\t\t\tvalidators: {\r\n\t\t\t\t\t\t\tnotEmpty: {\r\n\t\t\t\t\t\t\t\tmessage: 'Target tags are required'\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\t'target_allow': {\r\n\t\t\t\t\t\tvalidators: {\r\n\t\t\t\t\t\t\tnotEmpty: {\r\n\t\t\t\t\t\t\t\tmessage: 'Allowing target is required'\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\t'target_notifications[]': {\r\n\t\t\t\t\t\tvalidators: {\r\n\t\t\t\t\t\t\tnotEmpty: {\r\n\t\t\t\t\t\t\t\tmessage: 'Notifications are required'\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\t\r\n\t\t\t\tplugins: {\r\n\t\t\t\t\ttrigger: new FormValidation.plugins.Trigger(),\r\n\t\t\t\t\tbootstrap: new FormValidation.plugins.Bootstrap5({\r\n\t\t\t\t\t\trowSelector: '.fv-row',\r\n                        eleInvalidClass: '',\r\n                        eleValidClass: ''\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t);\r\n\t}\r\n\r\n\tvar handleForm = function() {\r\n\t\tnextButton.addEventListener('click', function (e) {\r\n\t\t\t// Prevent default button action\r\n\t\t\te.preventDefault();\r\n\r\n\t\t\t// Disable button to avoid multiple click \r\n\t\t\tnextButton.disabled = true;\r\n\r\n\t\t\t// Validate form before submit\r\n\t\t\tif (validator) {\r\n\t\t\t\tvalidator.validate().then(function (status) {\r\n\t\t\t\t\tconsole.log('validated!');\r\n\r\n\t\t\t\t\tif (status == 'Valid') {\r\n\t\t\t\t\t\t// Show loading indication\r\n\t\t\t\t\t\tnextButton.setAttribute('data-kt-indicator', 'on');\r\n\r\n\t\t\t\t\t\t// Simulate form submission\r\n\t\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\t\t// Simulate form submission\r\n\t\t\t\t\t\t\tnextButton.removeAttribute('data-kt-indicator');\r\n\r\n\t\t\t\t\t\t\t// Enable button\r\n\t\t\t\t\t\t\tnextButton.disabled = false;\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t// Go to next step\r\n\t\t\t\t\t\t\tstepper.goNext();\r\n\t\t\t\t\t\t}, 1500);   \t\t\t\t\t\t\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// Enable button\r\n\t\t\t\t\t\tnextButton.disabled = false;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// Show popup warning. For more info check the plugin's official documentation: https://sweetalert2.github.io/\r\n\t\t\t\t\t\tSwal.fire({\r\n\t\t\t\t\t\t\ttext: \"Sorry, looks like there are some errors detected, please try again.\",\r\n\t\t\t\t\t\t\ticon: \"error\",\r\n\t\t\t\t\t\t\tbuttonsStyling: false,\r\n\t\t\t\t\t\t\tconfirmButtonText: \"Ok, got it!\",\r\n\t\t\t\t\t\t\tcustomClass: {\r\n\t\t\t\t\t\t\t\tconfirmButton: \"btn btn-primary\"\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t}\t\t\t\r\n\t\t});\r\n\r\n\t\tpreviousButton.addEventListener('click', function () {\r\n\t\t\t// Go to previous step\r\n\t\t\tstepper.goPrevious();\r\n\t\t});\r\n\t}\r\n\r\n\treturn {\r\n\t\t// Public functions\r\n\t\tinit: function () {\r\n\t\t\tform = KTModalCreateProject.getForm();\r\n\t\t\tstepper = KTModalCreateProject.getStepperObj();\r\n\t\t\tnextButton = KTModalCreateProject.getStepper().querySelector('[data-kt-element=\"targets-next\"]');\r\n\t\t\tpreviousButton = KTModalCreateProject.getStepper().querySelector('[data-kt-element=\"targets-previous\"]');\r\n\r\n\t\t\tinitForm();\r\n\t\t\tinitValidation();\r\n\t\t\thandleForm();\r\n\t\t}\r\n\t};\r\n}();\r\n\r\n// Webpack support\r\nif (typeof module !== 'undefined' && typeof module.exports !== 'undefined') {\r\n\tmodule.exports = KTModalCreateProjectTargets;\r\n}", "\"use strict\";\r\n\r\n// Class definition\r\nvar KTModalCreateProjectTeam = function () {\r\n\t// Variables\r\n\tvar nextButton;\r\n\tvar previousButton;\r\n\tvar form;\r\n\tvar stepper;\r\n\r\n\t// Private functions\r\n\tvar handleForm = function() {\r\n\t\tnextButton.addEventListener('click', function (e) {\r\n\t\t\t// Prevent default button action\r\n\t\t\te.preventDefault();\r\n\r\n\t\t\t// Disable button to avoid multiple click \r\n\t\t\tnextButton.disabled = true;\r\n\r\n\t\t\t// Show loading indication\r\n\t\t\tnextButton.setAttribute('data-kt-indicator', 'on');\r\n\r\n\t\t\t// Simulate form submission\r\n\t\t\tsetTimeout(function() {\r\n\t\t\t\t// Enable button\r\n\t\t\t\tnextButton.disabled = false;\r\n\t\t\t\t\r\n\t\t\t\t// Simulate form submission\r\n\t\t\t\tnextButton.removeAttribute('data-kt-indicator');\r\n\t\t\t\t\r\n\t\t\t\t// Go to next step\r\n\t\t\t\tstepper.goNext();\r\n\t\t\t}, 1500); \t\t\r\n\t\t});\r\n\r\n\t\tpreviousButton.addEventListener('click', function () {\r\n\t\t\tstepper.goPrevious();\r\n\t\t});\r\n\t}\r\n\r\n\treturn {\r\n\t\t// Public functions\r\n\t\tinit: function () {\r\n\t\t\tform = KTModalCreateProject.getForm();\r\n\t\t\tstepper = KTModalCreateProject.getStepperObj();\r\n\t\t\tnextButton = KTModalCreateProject.getStepper().querySelector('[data-kt-element=\"team-next\"]');\r\n\t\t\tpreviousButton = KTModalCreateProject.getStepper().querySelector('[data-kt-element=\"team-previous\"]');\r\n\r\n\t\t\thandleForm();\r\n\t\t}\r\n\t};\r\n}();\r\n\r\n// Webpack support\r\nif (typeof module !== 'undefined' && typeof module.exports !== 'undefined') {\r\n\tmodule.exports = KTModalCreateProjectTeam;\r\n}", "\"use strict\";\r\n\r\n// Class definition\r\nvar KTModalCreateProjectType = function () {\r\n\t// Variables\r\n\tvar nextButton;\r\n\tvar validator;\r\n\tvar form;\r\n\tvar stepper;\r\n\r\n\t// Private functions\r\n\tvar initValidation = function() {\r\n\t\t// Init form validation rules. For more info check the FormValidation plugin's official documentation:https://formvalidation.io/\r\n\t\tvalidator = FormValidation.formValidation(\r\n\t\t\tform,\r\n\t\t\t{\r\n\t\t\t\tfields: {\r\n\t\t\t\t\t'project_type': {\r\n\t\t\t\t\t\tvalidators: {\r\n\t\t\t\t\t\t\tnotEmpty: {\r\n\t\t\t\t\t\t\t\tmessage: 'Project type is required'\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\t\r\n\t\t\t\tplugins: {\r\n\t\t\t\t\ttrigger: new FormValidation.plugins.Trigger(),\r\n\t\t\t\t\tbootstrap: new FormValidation.plugins.Bootstrap5({\r\n\t\t\t\t\t\trowSelector: '.fv-row',\r\n                        eleInvalidClass: '',\r\n                        eleValidClass: ''\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t);\r\n\t}\r\n\r\n\tvar handleForm = function() {\r\n\t\tnextButton.addEventListener('click', function (e) {\r\n\t\t\t// Prevent default button action\r\n\t\t\te.preventDefault();\r\n\r\n\t\t\t// Disable button to avoid multiple click \r\n\t\t\tnextButton.disabled = true;\r\n\r\n\t\t\t// Validate form before submit\r\n\t\t\tif (validator) {\r\n\t\t\t\tvalidator.validate().then(function (status) {\r\n\t\t\t\t\tconsole.log('validated!');\r\n\t\t\t\t\te.preventDefault();\r\n\r\n\t\t\t\t\tif (status == 'Valid') {\r\n\t\t\t\t\t\t// Show loading indication\r\n\t\t\t\t\t\tnextButton.setAttribute('data-kt-indicator', 'on');\r\n\r\n\t\t\t\t\t\t// Simulate form submission\r\n\t\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\t\t// Simulate form submission\r\n\t\t\t\t\t\t\tnextButton.removeAttribute('data-kt-indicator');\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t// Enable button\r\n\t\t\t\t\t\t\tnextButton.disabled = false;\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t// Go to next step\r\n\t\t\t\t\t\t\tstepper.goNext();\r\n\t\t\t\t\t\t}, 1000);   \t\t\t\t\t\t\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// Enable button\r\n\t\t\t\t\t\tnextButton.disabled = false;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// Show popup warning. For more info check the plugin's official documentation: https://sweetalert2.github.io/\r\n\t\t\t\t\t\tSwal.fire({\r\n\t\t\t\t\t\t\ttext: \"Sorry, looks like there are some errors detected, please try again.\",\r\n\t\t\t\t\t\t\ticon: \"error\",\r\n\t\t\t\t\t\t\tbuttonsStyling: false,\r\n\t\t\t\t\t\t\tconfirmButtonText: \"Ok, got it!\",\r\n\t\t\t\t\t\t\tcustomClass: {\r\n\t\t\t\t\t\t\t\tconfirmButton: \"btn btn-primary\"\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t}\t\t\t\r\n\t\t});\r\n\t}\r\n\r\n\treturn {\r\n\t\t// Public functions\r\n\t\tinit: function () {\r\n\t\t\tform = KTModalCreateProject.getForm();\r\n\t\t\tstepper = KTModalCreateProject.getStepperObj();\r\n\t\t\tnextButton = KTModalCreateProject.getStepper().querySelector('[data-kt-element=\"type-next\"]');\r\n\r\n\t\t\tinitValidation();\r\n\t\t\thandleForm();\r\n\t\t}\r\n\t};\r\n}();\r\n\r\n// Webpack support\r\nif (typeof module !== 'undefined' && typeof module.exports !== 'undefined') {\r\n\tmodule.exports = KTModalCreateProjectType;\r\n}\r\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "window.KTModalCreateProjectBudget = require('@/src/js/custom/modals/create-project/bundle/budget.js');\r\nwindow.KTModalCreateProjectComplete = require('@/src/js/custom/modals/create-project/bundle/complete.js');\r\nwindow.KTModalCreateProjectFiles = require('@/src/js/custom/modals/create-project/bundle/files.js');\r\nwindow.KTModalCreateProjectSettings = require('@/src/js/custom/modals/create-project/bundle/settings.js');\r\nwindow.KTModalCreateProjectTargets = require('@/src/js/custom/modals/create-project/bundle/targets.js');\r\nwindow.KTModalCreateProjectTeam = require('@/src/js/custom/modals/create-project/bundle/team.js');\r\nwindow.KTModalCreateProjectType = require('@/src/js/custom/modals/create-project/bundle/type.js');\r\nwindow.KTModalCreateProject = require('@/src/js/custom/modals/create-project/bundle/main.js');\r\n"], "names": [], "sourceRoot": ""}