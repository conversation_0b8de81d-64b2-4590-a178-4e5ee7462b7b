{"version": 3, "file": "js/custom/modals/two-factor-authentication.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA,2BAA2B;AAC3B,OAAO;AACP,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,KAAK;AACL;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA,4BAA4B;AAC5B,OAAO;AACP,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,KAAK;AACL;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC", "sources": ["webpack://keenthemes/../../../themes/metronic/html/demo3/src/js/custom/modals/two-factor-authentication.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTModalTwoFactorAuthentication = function () {\r\n    // Private variables\r\n    var modal;\r\n    var modalObject;\r\n\r\n    var optionsWrapper;\r\n    var optionsSelectButton;\r\n\r\n    var smsWrapper;\r\n    var smsForm;\r\n    var smsSubmitButton;\r\n    var smsCancelButton;\r\n    var smsValidator;\r\n\r\n    var appsWrapper;\r\n    var appsForm;\r\n    var appsSubmitButton;\r\n    var appsCancelButton;\r\n    var appsValidator;\r\n\r\n    // Private functions\r\n    var handleOptionsForm = function() {\r\n        // Handle options selection\r\n        optionsSelectButton.addEventListener('click', function (e) {\r\n            e.preventDefault();\r\n            var option = optionsWrapper.querySelector('[name=\"auth_option\"]:checked');\r\n\r\n            optionsWrapper.classList.add('d-none');\r\n\r\n            if (option.value == 'sms') {\r\n                smsWrapper.classList.remove('d-none');\r\n            } else {\r\n                appsWrapper.classList.remove('d-none');\r\n            }\r\n        });\r\n    }\r\n\r\n\tvar showOptionsForm = function() {\r\n\t\toptionsWrapper.classList.remove('d-none');\r\n\t\tsmsWrapper.classList.add('d-none');\r\n\t\tappsWrapper.classList.add('d-none');\r\n    }\r\n\r\n    var handleSMSForm = function() {\r\n        // Init form validation rules. For more info check the FormValidation plugin's official documentation:https://formvalidation.io/\r\n\t\tsmsValidator = FormValidation.formValidation(\r\n\t\t\tsmsForm,\r\n\t\t\t{\r\n\t\t\t\tfields: {\r\n\t\t\t\t\t'mobile': {\r\n\t\t\t\t\t\tvalidators: {\r\n\t\t\t\t\t\t\tnotEmpty: {\r\n\t\t\t\t\t\t\t\tmessage: 'Mobile no is required'\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\tplugins: {\r\n\t\t\t\t\ttrigger: new FormValidation.plugins.Trigger(),\r\n\t\t\t\t\tbootstrap: new FormValidation.plugins.Bootstrap5({\r\n\t\t\t\t\t\trowSelector: '.fv-row',\r\n                        eleInvalidClass: '',\r\n                        eleValidClass: ''\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t);\r\n\r\n        // Handle apps submition\r\n        smsSubmitButton.addEventListener('click', function (e) {\r\n            e.preventDefault();\r\n\r\n\t\t\t// Validate form before submit\r\n\t\t\tif (smsValidator) {\r\n\t\t\t\tsmsValidator.validate().then(function (status) {\r\n\t\t\t\t\tconsole.log('validated!');\r\n\r\n\t\t\t\t\tif (status == 'Valid') {\r\n\t\t\t\t\t\t// Show loading indication\r\n\t\t\t\t\t\tsmsSubmitButton.setAttribute('data-kt-indicator', 'on');\r\n\r\n\t\t\t\t\t\t// Disable button to avoid multiple click \r\n\t\t\t\t\t\tsmsSubmitButton.disabled = true;\t\t\t\t\t\t\r\n\r\n\t\t\t\t\t\t// Simulate ajax process\r\n\t\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\t\t// Remove loading indication\r\n\t\t\t\t\t\t\tsmsSubmitButton.removeAttribute('data-kt-indicator');\r\n\r\n\t\t\t\t\t\t\t// Enable button\r\n\t\t\t\t\t\t\tsmsSubmitButton.disabled = false;\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t// Show success message. For more info check the plugin's official documentation: https://sweetalert2.github.io/\r\n\t\t\t\t\t\t\tSwal.fire({\r\n\t\t\t\t\t\t\t\ttext: \"Mobile number has been successfully submitted!\",\r\n\t\t\t\t\t\t\t\ticon: \"success\",\r\n\t\t\t\t\t\t\t\tbuttonsStyling: false,\r\n\t\t\t\t\t\t\t\tconfirmButtonText: \"Ok, got it!\",\r\n\t\t\t\t\t\t\t\tcustomClass: {\r\n\t\t\t\t\t\t\t\t\tconfirmButton: \"btn btn-primary\"\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}).then(function (result) {\r\n\t\t\t\t\t\t\t\tif (result.isConfirmed) {\r\n\t\t\t\t\t\t\t\t\tmodalObject.hide();\r\n\t\t\t\t\t\t\t\t\tshowOptionsForm();\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t});\r\n\r\n\t\t\t\t\t\t\t//smsForm.submit(); // Submit form\r\n\t\t\t\t\t\t}, 2000);   \t\t\t\t\t\t\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// Show error message.\r\n\t\t\t\t\t\tSwal.fire({\r\n\t\t\t\t\t\t\ttext: \"Sorry, looks like there are some errors detected, please try again.\",\r\n\t\t\t\t\t\t\ticon: \"error\",\r\n\t\t\t\t\t\t\tbuttonsStyling: false,\r\n\t\t\t\t\t\t\tconfirmButtonText: \"Ok, got it!\",\r\n\t\t\t\t\t\t\tcustomClass: {\r\n\t\t\t\t\t\t\t\tconfirmButton: \"btn btn-primary\"\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t}\r\n        });\r\n\r\n        // Handle sms cancelation\r\n        smsCancelButton.addEventListener('click', function (e) {\r\n            e.preventDefault();\r\n            var option = optionsWrapper.querySelector('[name=\"auth_option\"]:checked');\r\n\r\n            optionsWrapper.classList.remove('d-none');\r\n            smsWrapper.classList.add('d-none');\r\n        });\r\n    }\r\n\r\n    var handleAppsForm = function() {\r\n\t\t// Init form validation rules. For more info check the FormValidation plugin's official documentation:https://formvalidation.io/\r\n\t\tappsValidator = FormValidation.formValidation(\r\n\t\t\tappsForm,\r\n\t\t\t{\r\n\t\t\t\tfields: {\r\n\t\t\t\t\t'code': {\r\n\t\t\t\t\t\tvalidators: {\r\n\t\t\t\t\t\t\tnotEmpty: {\r\n\t\t\t\t\t\t\t\tmessage: 'Code is required'\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\tplugins: {\r\n\t\t\t\t\ttrigger: new FormValidation.plugins.Trigger(),\r\n\t\t\t\t\tbootstrap: new FormValidation.plugins.Bootstrap5({\r\n\t\t\t\t\t\trowSelector: '.fv-row',\r\n                        eleInvalidClass: '',\r\n                        eleValidClass: ''\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t);\r\n\r\n        // Handle apps submition\r\n        appsSubmitButton.addEventListener('click', function (e) {\r\n            e.preventDefault();\r\n\r\n\t\t\t// Validate form before submit\r\n\t\t\tif (appsValidator) {\r\n\t\t\t\tappsValidator.validate().then(function (status) {\r\n\t\t\t\t\tconsole.log('validated!');\r\n\r\n\t\t\t\t\tif (status == 'Valid') {\r\n\t\t\t\t\t\tappsSubmitButton.setAttribute('data-kt-indicator', 'on');\r\n\r\n\t\t\t\t\t\t// Disable button to avoid multiple click \r\n\t\t\t\t\t\tappsSubmitButton.disabled = true;\r\n\r\n\t\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\t\tappsSubmitButton.removeAttribute('data-kt-indicator');\r\n\r\n\t\t\t\t\t\t\t// Enable button\r\n\t\t\t\t\t\t\tappsSubmitButton.disabled = false;\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t// Show success message.\r\n\t\t\t\t\t\t\tSwal.fire({\r\n\t\t\t\t\t\t\t\ttext: \"Code has been successfully submitted!\",\r\n\t\t\t\t\t\t\t\ticon: \"success\",\r\n\t\t\t\t\t\t\t\tbuttonsStyling: false,\r\n\t\t\t\t\t\t\t\tconfirmButtonText: \"Ok, got it!\",\r\n\t\t\t\t\t\t\t\tcustomClass: {\r\n\t\t\t\t\t\t\t\t\tconfirmButton: \"btn btn-primary\"\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}).then(function (result) {\r\n\t\t\t\t\t\t\t\tif (result.isConfirmed) {\r\n\t\t\t\t\t\t\t\t\tmodalObject.hide();\r\n\t\t\t\t\t\t\t\t\tshowOptionsForm();\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t});\r\n\r\n\t\t\t\t\t\t\t//appsForm.submit(); // Submit form\r\n\t\t\t\t\t\t}, 2000);   \t\t\t\t\t\t\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// Show error message.\r\n\t\t\t\t\t\tSwal.fire({\r\n\t\t\t\t\t\t\ttext: \"Sorry, looks like there are some errors detected, please try again.\",\r\n\t\t\t\t\t\t\ticon: \"error\",\r\n\t\t\t\t\t\t\tbuttonsStyling: false,\r\n\t\t\t\t\t\t\tconfirmButtonText: \"Ok, got it!\",\r\n\t\t\t\t\t\t\tcustomClass: {\r\n\t\t\t\t\t\t\t\tconfirmButton: \"btn btn-primary\"\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t}\r\n        });\r\n\r\n        // Handle apps cancelation\r\n        appsCancelButton.addEventListener('click', function (e) {\r\n            e.preventDefault();\r\n            var option = optionsWrapper.querySelector('[name=\"auth_option\"]:checked');\r\n\r\n            optionsWrapper.classList.remove('d-none');\r\n            appsWrapper.classList.add('d-none');\r\n        });\r\n    }\r\n\r\n    // Public methods\r\n    return {\r\n        init: function () {\r\n            // Elements\r\n            modal = document.querySelector('#kt_modal_two_factor_authentication');\r\n\r\n\t\t\tif (!modal) {\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n            modalObject = new bootstrap.Modal(modal);\r\n\r\n            optionsWrapper = modal.querySelector('[data-kt-element=\"options\"]');\r\n            optionsSelectButton = modal.querySelector('[data-kt-element=\"options-select\"]');\r\n\r\n            smsWrapper = modal.querySelector('[data-kt-element=\"sms\"]');\r\n            smsForm = modal.querySelector('[data-kt-element=\"sms-form\"]');\r\n            smsSubmitButton = modal.querySelector('[data-kt-element=\"sms-submit\"]');\r\n            smsCancelButton = modal.querySelector('[data-kt-element=\"sms-cancel\"]');\r\n\r\n            appsWrapper = modal.querySelector('[data-kt-element=\"apps\"]');\r\n            appsForm = modal.querySelector('[data-kt-element=\"apps-form\"]');\r\n            appsSubmitButton = modal.querySelector('[data-kt-element=\"apps-submit\"]');\r\n            appsCancelButton = modal.querySelector('[data-kt-element=\"apps-cancel\"]');\r\n\r\n            // Handle forms\r\n            handleOptionsForm();\r\n            handleSMSForm();\r\n            handleAppsForm();\r\n        }\r\n    }\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function() {\r\n    KTModalTwoFactorAuthentication.init();\r\n});\r\n"], "names": [], "sourceRoot": ""}