{"version": 3, "file": "js/custom/pages/profile/connections.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,CAAC,E", "sources": ["webpack://keenthemes/../../../themes/metronic/html/demo3/src/js/custom/pages/profile/connections.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTProfileConnections = function () {\r\n    // init variables\r\n    var showMoreButton = document.getElementById('kt_connections_show_more_button');\r\n    var showMoreCards = document.getElementById('kt_connections_show_more_cards');\r\n\r\n    // Private functions\r\n    var handleShowMore = function () {\r\n        // Show more click\r\n        showMoreButton.addEventListener('click', function (e) {\r\n            showMoreButton.setAttribute('data-kt-indicator', 'on');\r\n\r\n            // Disable button to avoid multiple click \r\n            showMoreButton.disabled = true;\r\n\r\n            setTimeout(function() {\r\n                // Hide loading indication\r\n                showMoreButton.removeAttribute('data-kt-indicator');\r\n\r\n                // Enable button\r\n\t\t\t\tshowMoreButton.disabled = false;\r\n\r\n                // Hide button\r\n                showMoreButton.classList.add('d-none');\r\n\r\n                // Show card\r\n                showMoreCards.classList.remove('d-none');\r\n\r\n                // Scroll to card\r\n                KTUtil.scrollTo(showMoreCards, 200);\r\n            }, 2000);\r\n        });\r\n    }\r\n\r\n    // Public methods\r\n    return {\r\n        init: function () {\r\n            handleShowMore();\r\n        }\r\n    }\r\n}();\r\n\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function() {\r\n    KTProfileConnections.init();\r\n});"], "names": [], "sourceRoot": ""}