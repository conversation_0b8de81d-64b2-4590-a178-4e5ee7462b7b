{"version": 3, "file": "js/custom/pages/company/contact.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,8BAA8B,EAAE,yBAAyB,EAAE,EAAE,EAAE,EAAE,EAAE;AACnE,iCAAiC;AACjC,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,sCAAsC,mBAAmB,mEAAmE,oBAAoB;AAChJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0CAA0C,mBAAmB,4DAA4D,oBAAoB;AAC7I;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA,iBAAiB;AACjB,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA,QAAQ;AACR;AACA,wBAAwB;AACxB,OAAO;AACP,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,OAAO;AACP;AACA,KAAK;AACL;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC,E", "sources": ["webpack://keenthemes/../../../themes/metronic/html/demo3/src/js/custom/pages/company/contact.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTContactApply = function () {\r\n\tvar submitButton;\r\n\tvar validator;\r\n\tvar form;\r\n\tvar selectedlocation;\r\n\r\n\t// Private functions\r\n    var initMap = function(elementId) {\r\n        // Check if Leaflet is included\r\n        if (!L) {\r\n            return;\r\n        }\r\n\r\n        // Define Map Location\r\n        var leaflet = L.map(elementId, {\r\n            center: [40.725, -73.985],\r\n            zoom: 30\r\n        });\r\n\r\n        // Init Leaflet Map. For more info check the plugin's documentation: https://leafletjs.com/\r\n        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {\r\n            attribution: '&copy; <a href=\"https://osm.org/copyright\">OpenStreetMap</a> contributors'\r\n        }).addTo(leaflet);\r\n\r\n        // Set Geocoding\r\n        var geocodeService;\r\n        if (typeof L.esri.Geocoding === 'undefined') {\r\n            geocodeService = L.esri.geocodeService();\r\n        } else {\r\n            geocodeService = L.esri.Geocoding.geocodeService();\r\n        }\r\n\r\n        // Define Marker Layer\r\n        var markerLayer = L.layerGroup().addTo(leaflet);\r\n\r\n        // Set Custom SVG icon marker\r\n        var leafletIcon = L.divIcon({\r\n            html: `<span class=\"svg-icon svg-icon-primary shadow svg-icon-3x\"><svg xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"24px\" height=\"24px\" viewBox=\"0 0 24 24\" version=\"1.1\"><g stroke=\"none\" stroke-width=\"1\" fill=\"none\" fill-rule=\"evenodd\"><rect x=\"0\" y=\"24\" width=\"24\" height=\"0\"/><path d=\"M5,10.5 C5,6 8,3 12.5,3 C17,3 20,6.75 20,10.5 C20,12.8325623 17.8236613,16.03566 13.470984,20.1092932 C12.9154018,20.6292577 12.0585054,20.6508331 11.4774555,20.1594925 C7.15915182,16.5078313 5,13.2880005 5,10.5 Z M12.5,12 C13.8807119,12 15,10.8807119 15,9.5 C15,8.11928813 13.8807119,7 12.5,7 C11.1192881,7 10,8.11928813 10,9.5 C10,10.8807119 11.1192881,12 12.5,12 Z\" fill=\"#000000\" fill-rule=\"nonzero\"/></g></svg></span>`,\r\n            bgPos: [10, 10],\r\n            iconAnchor: [20, 37],\r\n            popupAnchor: [0, -37],\r\n            className: 'leaflet-marker'\r\n        });\r\n\r\n\t\t// Show current address\r\n\t\tL.marker([40.724716, -73.984789], { icon: leafletIcon }).addTo(markerLayer).bindPopup('430 E 6th St, New York, 10009.', { closeButton: false }).openPopup();\r\n\r\n        // Map onClick Action\r\n        leaflet.on('click', function (e) {\r\n            geocodeService.reverse().latlng(e.latlng).run(function (error, result) {\r\n                if (error) {\r\n                    return;\r\n                }\r\n                markerLayer.clearLayers();\r\n                selectedlocation = result.address.Match_addr;\r\n                L.marker(result.latlng, { icon: leafletIcon }).addTo(markerLayer).bindPopup(result.address.Match_addr, { closeButton: false }).openPopup();\r\n\r\n                // Show popup confirmation. For more info check the plugin's official documentation: https://sweetalert2.github.io/\r\n                Swal.fire({\r\n                    html: 'Your selected - <b>\"' + selectedlocation + ' - ' + result.latlng + '\"</b>.',\r\n                    icon: \"success\",\r\n                    buttonsStyling: false,\r\n                    confirmButtonText: \"Ok, got it!\",\r\n                    customClass: {\r\n                        confirmButton: \"btn btn-primary\"\r\n                    }\r\n                }).then(function (result) {\r\n                    // Confirmed\r\n                });\r\n            });\r\n        });\r\n    }\r\n\r\n\t// Init form inputs\r\n\tvar initForm = function() {\r\n\t\t// Team assign. For more info, plase visit the official plugin site: https://select2.org/\r\n        $(form.querySelector('[name=\"position\"]')).on('change', function() {\r\n            // Revalidate the field when an option is chosen\r\n            validator.revalidateField('position');\r\n        });\t\t\r\n\t}\r\n\r\n\t// Handle form validation and submittion\r\n\tvar handleForm = function() {\r\n\t\t// Stepper custom navigation\r\n\r\n\t\t// Init form validation rules. For more info check the FormValidation plugin's official documentation:https://formvalidation.io/\r\n\t\tvalidator = FormValidation.formValidation(\r\n\t\t\tform,\r\n\t\t\t{\r\n\t\t\t\tfields: {\r\n\t\t\t\t\t'name': {\r\n\t\t\t\t\t\tvalidators: {\r\n\t\t\t\t\t\t\tnotEmpty: {\r\n\t\t\t\t\t\t\t\tmessage: 'Name is required'\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\t'email': {\r\n                        validators: {\r\n\t\t\t\t\t\t\tnotEmpty: {\r\n\t\t\t\t\t\t\t\tmessage: 'Email address is required'\r\n\t\t\t\t\t\t\t},\r\n                            emailAddress: {\r\n\t\t\t\t\t\t\t\tmessage: 'The value is not a valid email address'\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\t'message': {\r\n                        validators: {\r\n\t\t\t\t\t\t\tnotEmpty: {\r\n\t\t\t\t\t\t\t\tmessage: 'Message is required'\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\t\t \r\n\t\t\t\t},\r\n\t\t\t\tplugins: {\r\n\t\t\t\t\ttrigger: new FormValidation.plugins.Trigger(),\r\n\t\t\t\t\tbootstrap: new FormValidation.plugins.Bootstrap5({\r\n\t\t\t\t\t\trowSelector: '.fv-row',\r\n                        eleInvalidClass: '',\r\n                        eleValidClass: ''\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t);\r\n\r\n\t\t// Action buttons\r\n\t\tsubmitButton.addEventListener('click', function (e) {\r\n\t\t\te.preventDefault();\r\n\r\n\t\t\t// Validate form before submit\r\n\t\t\tif (validator) {\r\n\t\t\t\tvalidator.validate().then(function (status) {\r\n\t\t\t\t\tconsole.log('validated!');\r\n\r\n\t\t\t\t\tif (status == 'Valid') {\r\n\t\t\t\t\t\tsubmitButton.setAttribute('data-kt-indicator', 'on');\r\n\r\n\t\t\t\t\t\t// Disable button to avoid multiple click \r\n\t\t\t\t\t\tsubmitButton.disabled = true;\r\n\r\n\t\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\t\tsubmitButton.removeAttribute('data-kt-indicator');\r\n\r\n\t\t\t\t\t\t\t// Enable button\r\n\t\t\t\t\t\t\tsubmitButton.disabled = false;\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\tSwal.fire({\r\n\t\t\t\t\t\t\t\ttext: \"Form has been successfully submitted!\",\r\n\t\t\t\t\t\t\t\ticon: \"success\",\r\n\t\t\t\t\t\t\t\tbuttonsStyling: false,\r\n\t\t\t\t\t\t\t\tconfirmButtonText: \"Ok, got it!\",\r\n\t\t\t\t\t\t\t\tcustomClass: {\r\n\t\t\t\t\t\t\t\t\tconfirmButton: \"btn btn-primary\"\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}).then(function (result) {\r\n\t\t\t\t\t\t\t\tif (result.isConfirmed) {\r\n\t\t\t\t\t\t\t\t\t//form.submit();\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t});\r\n\r\n\t\t\t\t\t\t\t//form.submit(); // Submit form\r\n\t\t\t\t\t\t}, 2000);   \t\t\t\t\t\t\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// Scroll top\r\n\r\n\t\t\t\t\t\t// Show error popuo. For more info check the plugin's official documentation: https://sweetalert2.github.io/\r\n\t\t\t\t\t\tSwal.fire({\r\n\t\t\t\t\t\t\ttext: \"Sorry, looks like there are some errors detected, please try again.\",\r\n\t\t\t\t\t\t\ticon: \"error\",\r\n\t\t\t\t\t\t\tbuttonsStyling: false,\r\n\t\t\t\t\t\t\tconfirmButtonText: \"Ok, got it!\",\r\n\t\t\t\t\t\t\tcustomClass: {\r\n\t\t\t\t\t\t\t\tconfirmButton: \"btn btn-primary\"\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}).then(function (result) {\r\n\t\t\t\t\t\t\tKTUtil.scrollTop();\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t});\r\n\t}\r\n\r\n\treturn {\r\n\t\t// Public functions\r\n\t\tinit: function () {\r\n\t\t\t// Elements\r\n\t\t\tform = document.querySelector('#kt_contact_form');\r\n\t\t\tsubmitButton = document.getElementById('kt_contact_submit_button');\r\n\r\n\t\t\tinitForm();\r\n\t\t\thandleForm();\r\n\t\t\tinitMap('kt_contact_map');\r\n\t\t}\r\n\t};\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function () {\r\n\tKTContactApply.init();\r\n});"], "names": [], "sourceRoot": ""}