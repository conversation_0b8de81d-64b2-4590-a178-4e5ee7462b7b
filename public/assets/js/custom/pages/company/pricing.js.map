{"version": 3, "file": "js/custom/pages/company/pricing.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC", "sources": ["webpack://keenthemes/../../../themes/metronic/html/demo3/src/js/custom/pages/company/pricing.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTGeneralPricing = function () {\r\n    // Private variables\r\n    var element;\r\n\tvar planPeriodMonthButton;\r\n\tvar planPeriodAnnualButton;\r\n\r\n\tvar changePlanPrices = function(type) {\r\n\t\tvar items = [].slice.call(element.querySelectorAll('[data-kt-plan-price-month]'));\r\n\r\n\t\titems.map(function (item) {\r\n\t\t\tvar monthPrice = item.getAttribute('data-kt-plan-price-month');\r\n\t\t\tvar annualPrice = item.getAttribute('data-kt-plan-price-annual');\r\n\r\n\t\t\tif ( type === 'month' ) {\r\n\t\t\t\titem.innerHTML = monthPrice;\r\n\t\t\t} else if ( type === 'annual' ) {\r\n\t\t\t\titem.innerHTML = annualPrice;\r\n\t\t\t}\r\n\t\t});\r\n\t}\r\n\r\n    var handlePlanPeriodSelection = function(e) {\r\n\r\n        // Handle period change\r\n        planPeriodMonthButton.addEventListener('click', function (e) {\r\n            e.preventDefault();\r\n\r\n            changePlanPrices('month');\r\n        });\r\n\r\n\t\tplanPeriodAnnualButton.addEventListener('click', function (e) {\r\n            e.preventDefault();\r\n            \r\n            changePlanPrices('annual');\r\n        });\r\n    }\r\n\r\n    // Public methods\r\n    return {\r\n        init: function () {\r\n            element = document.querySelector('#kt_pricing');\r\n\t\t\tplanPeriodMonthButton = element.querySelector('[data-kt-plan=\"month\"]');\r\n\t\t\tplanPeriodAnnualButton = element.querySelector('[data-kt-plan=\"annual\"]');\r\n\r\n            // Handlers\r\n            handlePlanPeriodSelection();\r\n        }\r\n    }\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function() {\r\n    KTGeneralPricing.init();\r\n});\r\n"], "names": [], "sourceRoot": ""}