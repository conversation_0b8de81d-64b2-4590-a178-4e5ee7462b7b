{"version": 3, "file": "js/custom/pages/search/horizontal.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC", "sources": ["webpack://keenthemes/../../../themes/metronic/html/demo3/src/js/custom/pages/search/horizontal.js"], "sourcesContent": ["\"use strict\";\r\n \r\n// Class definition\r\nvar KTSearchHorizontal = function () {\r\n    // Private functions\r\n    var initAdvancedSearchForm = function () {\r\n       var form = document.querySelector('#kt_advanced_search_form');\r\n\r\n       // Init tags\r\n       var tags = form.querySelector('[name=\"tags\"]');\r\n       new Tagify(tags);\r\n    }\r\n\r\n    var handleAdvancedSearchToggle = function () {\r\n        var link = document.querySelector('#kt_horizontal_search_advanced_link');\r\n\r\n        link.addEventListener('click', function (e) {\r\n            e.preventDefault();\r\n            \r\n            if (link.innerHTML === \"Advanced Search\") {\r\n                link.innerHTML = \"Hide Advanced Search\";\r\n            } else {\r\n                link.innerHTML = \"Advanced Search\";\r\n            }\r\n        })\r\n    }\r\n\r\n    // Public methods\r\n    return {\r\n        init: function () {\r\n            initAdvancedSearchForm();\r\n            handleAdvancedSearchToggle();\r\n        }\r\n    }     \r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function () {\r\n    KTSearchHorizontal.init();\r\n});\r\n"], "names": [], "sourceRoot": ""}