{"version": 3, "file": "js/custom/pages/projects/list/list.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA,aAAa;AACb;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC", "sources": ["webpack://keenthemes/../../../themes/metronic/html/demo3/src/js/custom/pages/projects/list/list.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTProjectList = function () {    \r\n    var initChart = function () {\r\n        // init chart\r\n        var element = document.getElementById(\"kt_project_list_chart\");\r\n\r\n        if (!element) {\r\n            return;\r\n        }\r\n\r\n        var config = {\r\n            type: 'doughnut',\r\n            data: {\r\n                datasets: [{\r\n                    data: [30, 45, 25],\r\n                    backgroundColor: ['#00A3FF', '#50CD89', '#E4E6EF']\r\n                }],\r\n                labels: ['Active', 'Completed', 'Yet to start']\r\n            },\r\n            options: {\r\n                chart: {\r\n                    fontFamily: 'inherit'\r\n                },\r\n                cutout: '75%',\r\n                cutoutPercentage: 65,\r\n                responsive: true,\r\n                maintainAspectRatio: false,\r\n                title: {\r\n                    display: false\r\n                },\r\n                animation: {\r\n                    animateScale: true,\r\n                    animateRotate: true\r\n                },\r\n                tooltips: {\r\n                    enabled: true,\r\n                    intersect: false,\r\n                    mode: 'nearest',\r\n                    bodySpacing: 5,\r\n                    yPadding: 10,\r\n                    xPadding: 10,\r\n                    caretPadding: 0,\r\n                    displayColors: false,\r\n                    backgroundColor: '#20D489',\r\n                    titleFontColor: '#ffffff',\r\n                    cornerRadius: 4,\r\n                    footerSpacing: 0,\r\n                    titleSpacing: 0\r\n                },\r\n                plugins: {\r\n                    legend: {\r\n                        display: false\r\n                    }\r\n                }                \r\n            }\r\n        };\r\n\r\n        var ctx = element.getContext('2d');\r\n        var myDoughnut = new Chart(ctx, config);\r\n    }\r\n\r\n    // Public methods\r\n    return {\r\n        init: function () {\r\n            initChart();\r\n        }\r\n    }\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function() {\r\n    KTProjectList.init();\r\n});\r\n"], "names": [], "sourceRoot": ""}