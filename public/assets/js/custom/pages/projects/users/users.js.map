{"version": 3, "file": "js/custom/pages/projects/users/users.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC,E", "sources": ["webpack://keenthemes/../../../themes/metronic/html/demo3/src/js/custom/pages/projects/users/users.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTProjectUsers = function () {\r\n\r\n    var initTable = function () {\r\n        // Set date data order\r\n        const table = document.getElementById('kt_project_users_table');\r\n\r\n        if (!table) {\r\n            return;\r\n        }\r\n        \r\n        const tableRows = table.querySelectorAll('tbody tr');\r\n        \r\n        tableRows.forEach(row => {\r\n            const dateRow = row.querySelectorAll('td');\r\n            const realDate = moment(dateRow[1].innerHTML, \"MMM D, YYYY\").format();\r\n            dateRow[1].setAttribute('data-order', realDate);\r\n        });\r\n\r\n        // Init datatable --- more info on datatables: https://datatables.net/manual/\r\n        const datatable = $(table).DataTable({\r\n            \"info\": false,\r\n            'order': [],\r\n            \"columnDefs\": [{\r\n                \"targets\": 4,\r\n                \"orderable\": false\r\n            }]\r\n        });\r\n\r\n        // Search --- official docs reference: https://datatables.net/reference/api/search()\r\n        var filterSearch = document.getElementById('kt_filter_search');\r\n        if (filterSearch) {\r\n            filterSearch.addEventListener('keyup', function (e) {\r\n                datatable.search(e.target.value).draw();\r\n            });\r\n        }        \r\n    }\r\n\r\n    // Public methods\r\n    return {\r\n        init: function () {\r\n            initTable();\r\n        }\r\n    }\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function() {\r\n    KTProjectUsers.init();\r\n});"], "names": [], "sourceRoot": ""}