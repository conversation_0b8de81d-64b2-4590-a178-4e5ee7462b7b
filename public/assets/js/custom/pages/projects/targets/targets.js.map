{"version": 3, "file": "js/custom/pages/projects/targets/targets.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,CAAC", "sources": ["webpack://keenthemes/../../../themes/metronic/html/demo3/src/js/custom/pages/projects/targets/targets.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTProjectTargets = function () {\r\n\r\n    var initDatatable = function () {\r\n        const table = document.getElementById('kt_profile_overview_table');\r\n\r\n        // set date data order\r\n        const tableRows = table.querySelectorAll('tbody tr');\r\n        tableRows.forEach(row => {\r\n            const dateRow = row.querySelectorAll('td');\r\n            const realDate = moment(dateRow[1].innerHTML, \"MMM D, YYYY\").format();\r\n            dateRow[1].setAttribute('data-order', realDate);\r\n        });\r\n\r\n        // init datatable --- more info on datatables: https://datatables.net/manual/\r\n        const datatable = $(table).DataTable({\r\n            \"info\": false,\r\n            'order': [],\r\n            \"paging\": false,\r\n        });\r\n\r\n    }\r\n\r\n    // Public methods\r\n    return {\r\n        init: function () {\r\n            initDatatable();\r\n        }\r\n    }\r\n}();\r\n\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function() {\r\n    KTProjectTargets.init();\r\n});\r\n"], "names": [], "sourceRoot": ""}