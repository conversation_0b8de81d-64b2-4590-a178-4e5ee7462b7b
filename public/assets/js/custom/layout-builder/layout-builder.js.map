{"version": 3, "file": "js/custom/layout-builder/layout-builder.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,yBAAyB;AACzB,MAAM;AACN,KAAK;AACL;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,KAAK;AACL;AACA;AACA;AACA,IAAI;AACJ,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC;AACjC,OAAO;AACP;AACA;AACA;AACA;AACA,OAAO;AACP,MAAM;AACN,KAAK;AACL;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,KAAK;AACL,IAAI;AACJ,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,yBAAyB;AACzB,MAAM;AACN,KAAK;AACL;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL,IAAI;AACJ,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC,E", "sources": ["webpack://keenthemes/../src/js/custom/layout-builder/layout-builder.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTLayoutBuilder = function() {\r\n\tvar form = document.querySelector(\"#kt_layout_builder_form\");\r\n\tvar actionInput = document.querySelector(\"#kt_layout_builder_action\");\r\n\tvar tabInput = document.querySelector(\"#kt_layout_builder_tab\");\t\r\n\tvar url = form.getAttribute(\"action\");\r\n\r\n\tvar previewButton = document.querySelector(\"#kt_layout_builder_preview\");\r\n\tvar exportButton = document.querySelector(\"#kt_layout_builder_export\");\r\n\tvar resetButton = document.querySelector(\"#kt_layout_builder_reset\");\r\n\r\n\tvar handleTab = function() {\r\n\t\tvar tabs = [].slice.call(document.querySelectorAll('#kt_layout_builder_tabs a[data-bs-toggle=\"tab\"]'));\r\n\r\n\t\ttabs.forEach(function (tab) {\r\n\t\t\ttab.addEventListener(\"shown.bs.tab\", function (e) {\r\n\t\t\t\ttabInput.value = tab.getAttribute(\"href\").substring(1);\r\n\t\t\t});\r\n\t\t});\r\n\t};\r\n\r\n\tvar handlePreview = function() {\r\n\t\tpreviewButton.addEventListener(\"click\", function(e) {\r\n\t\t\te.preventDefault();\r\n\r\n\t\t\t// Set form action value\r\n\t\t\tactionInput.value = \"preview\";\r\n\r\n\t\t\t// Show progress\r\n\t\t\tpreviewButton.setAttribute(\"data-kt-indicator\", \"on\");\r\n\r\n\t\t\t// Prepare form data\r\n\t\t\tvar data = $(form).serialize();\r\n\r\n\t\t\t// Submit\r\n\t\t\t$.ajax({\r\n\t\t\t\ttype: \"POST\",\r\n\t\t\t\tdataType: \"html\",\r\n\t\t\t\turl: url,\r\n\t\t\t\tdata: data,\r\n\t\t\t\tsuccess: function(response, status, xhr) {\r\n\t\t\t\t\ttoastr.success(\r\n\t\t\t\t\t\t\"Preview has been updated with current configured layout.\", \r\n\t\t\t\t\t\t\"Preview updated!\", \r\n\t\t\t\t\t\t{timeOut: 0, extendedTimeOut: 0, closeButton: true, closeDuration: 0}\r\n\t\t\t\t\t);\r\n\r\n\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\tlocation.reload(); // reload page\r\n\t\t\t\t\t}, 1500);\r\n\t\t\t\t},\r\n\t\t\t\terror: function(response) {\r\n\t\t\t\t\ttoastr.error(\r\n\t\t\t\t\t\t\"Please try it again later.\", \r\n\t\t\t\t\t\t\"Something went wrong!\", \r\n\t\t\t\t\t\t{timeOut: 0, extendedTimeOut: 0, closeButton: true, closeDuration: 0}\r\n\t\t\t\t\t);\r\n\t\t\t\t},\r\n\t\t\t\tcomplete: function() {\r\n\t\t\t\t\tpreviewButton.removeAttribute(\"data-kt-indicator\");\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t});\r\n\t};\r\n\r\n\tvar handleExport = function() {\r\n\t\texportButton.addEventListener(\"click\", function(e) {\r\n\t\t\te.preventDefault();\r\n\r\n\t\t\ttoastr.success(\r\n\t\t\t\t\"Process has been started and it may take a while.\", \r\n\t\t\t\t\"Generating HTML!\", \r\n\t\t\t\t{timeOut: 0, extendedTimeOut: 0, closeButton: true, closeDuration: 0}\r\n\t\t\t);\r\n\r\n\t\t\t// Show progress\r\n\t\t\texportButton.setAttribute(\"data-kt-indicator\", \"on\");\r\n\r\n\t\t\t// Set form action value\r\n\t\t\tactionInput.value = \"export\";\r\n\t\t\t\r\n\t\t\t// Prepare form data\r\n\t\t\tvar data = $(form).serialize();\r\n\r\n\t\t\t$.ajax({\r\n\t\t\t\ttype: \"POST\",\r\n\t\t\t\tdataType: \"html\",\r\n\t\t\t\turl: url,\r\n\t\t\t\tdata: data,\r\n\t\t\t\tsuccess: function(response, status, xhr) {\r\n\t\t\t\t\tvar timer = setInterval(function() {\r\n\t\t\t\t\t\t$(\"<iframe/>\").attr({\r\n\t\t\t\t\t\t\tsrc: url + \"?layout-builder[action]=export&download=1&output=\" + response,\r\n\t\t\t\t\t\t\tstyle: \"visibility:hidden;display:none\",\r\n\t\t\t\t\t\t}).ready(function() {\r\n\t\t\t\t\t\t\t// Stop the timer\r\n\t\t\t\t\t\t\tclearInterval(timer);\r\n\r\n\t\t\t\t\t\t\texportButton.removeAttribute(\"data-kt-indicator\");\r\n\t\t\t\t\t\t}).appendTo(\"body\");\r\n\t\t\t\t\t}, 3000);\r\n\t\t\t\t},\r\n\t\t\t\terror: function(response) {\r\n\t\t\t\t\ttoastr.error(\r\n\t\t\t\t\t\t\"Please try it again later.\", \r\n\t\t\t\t\t\t\"Something went wrong!\", \r\n\t\t\t\t\t\t{timeOut: 0, extendedTimeOut: 0, closeButton: true, closeDuration: 0}\r\n\t\t\t\t\t);\r\n\r\n\t\t\t\t\texportButton.removeAttribute(\"data-kt-indicator\");\r\n\t\t\t\t},\r\n\t\t\t});\r\n\t\t});\r\n\t};\r\n\r\n\tvar handleReset = function() {\r\n\t\tresetButton.addEventListener(\"click\", function(e) {\r\n\t\t\te.preventDefault();\r\n\r\n\t\t\t// Show progress\r\n\t\t\tresetButton.setAttribute(\"data-kt-indicator\", \"on\");\r\n\r\n\t\t\t// Set form action value\r\n\t\t\tactionInput.value = \"reset\";\r\n\t\t\t\r\n\t\t\t// Prepare form data\r\n\t\t\tvar data = $(form).serialize();\r\n\r\n\t\t\t$.ajax({\r\n\t\t\t\ttype: \"POST\",\r\n\t\t\t\tdataType: \"html\",\r\n\t\t\t\turl: url,\r\n\t\t\t\tdata: data,\r\n\t\t\t\tsuccess: function(response, status, xhr) {\r\n\t\t\t\t\ttoastr.success(\r\n\t\t\t\t\t\t\"Preview has been successfully reset and the page will be reloaded.\", \r\n\t\t\t\t\t\t\"Reset Preview!\", \r\n\t\t\t\t\t\t{timeOut: 0, extendedTimeOut: 0, closeButton: true, closeDuration: 0}\r\n\t\t\t\t\t);\r\n\r\n\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\tlocation.reload(); // reload page\r\n\t\t\t\t\t}, 1500);\r\n\t\t\t\t},\r\n\t\t\t\terror: function(response) {\r\n\t\t\t\t\ttoastr.error(\r\n\t\t\t\t\t\t\"Please try it again later.\", \r\n\t\t\t\t\t\t\"Something went wrong!\", \r\n\t\t\t\t\t\t{timeOut: 0, extendedTimeOut: 0, closeButton: true, closeDuration: 0}\r\n\t\t\t\t\t);\r\n\t\t\t\t},\r\n\t\t\t\tcomplete: function() {\r\n\t\t\t\t\tresetButton.removeAttribute(\"data-kt-indicator\");\r\n\t\t\t\t},\r\n\t\t\t});\r\n\t\t});\r\n\t};\r\n\r\n\treturn {\r\n\t\t// Public functions\r\n\t\tinit: function() {\r\n\t\t\thandlePreview();\r\n\t\t\thandleExport();\r\n\t\t\thandleReset();\r\n\t\t\thandleTab();\r\n\t\t}\r\n\t};\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function() {\r\n    KTLayoutBuilder.init();\r\n});"], "names": [], "sourceRoot": ""}