{"version": 3, "file": "js/custom/documentation/charts/flotcharts/dynamic.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,iBAAiB;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA,MAAM;AACN;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC", "sources": ["webpack://keenthemes/../src/js/custom/documentation/charts/flotcharts/dynamic.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTFlotDemoDynamic = function () {\r\n    // Private functions\r\n    var exampleDynamic = function () {\r\n        var data = [];\r\n\t\tvar totalPoints = 250;\r\n\r\n\t\t// random data generator for plot charts\r\n\r\n\t\tfunction getRandomData() {\r\n\t\t\tif (data.length > 0) data = data.slice(1);\r\n\t\t\t// do a random walk\r\n\t\t\twhile (data.length < totalPoints) {\r\n\t\t\t\tvar prev = data.length > 0 ? data[data.length - 1] : 50;\r\n\t\t\t\tvar y = prev + Math.random() * 10 - 5;\r\n\t\t\t\tif (y < 0) y = 0;\r\n\t\t\t\tif (y > 100) y = 100;\r\n\t\t\t\tdata.push(y);\r\n\t\t\t}\r\n\t\t\t// zip the generated y values with the x values\r\n\t\t\tvar res = [];\r\n\t\t\tfor (var i = 0; i < data.length; ++i) {\r\n\t\t\t\tres.push([i, data[i]]);\r\n\t\t\t}\r\n\r\n\t\t\treturn res;\r\n\t\t}\r\n\r\n\t\t//server load\r\n\t\tvar options = {\r\n\t\t\tcolors: [KTUtil.getCssVariableValue('--bs-active-danger'), KTUtil.getCssVariableValue('--bs-active-primary')],\r\n\t\t\tseries: {\r\n\t\t\t\tshadowSize: 1\r\n\t\t\t},\r\n\t\t\tlines: {\r\n\t\t\t\tshow: true,\r\n\t\t\t\tlineWidth: 0.5,\r\n\t\t\t\tfill: true,\r\n\t\t\t\tfillColor: {\r\n\t\t\t\t\tcolors: [{\r\n\t\t\t\t\t\topacity: 0.1\r\n\t\t\t\t\t}, {\r\n\t\t\t\t\t\topacity: 1\r\n\t\t\t\t\t}]\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tyaxis: {\r\n\t\t\t\tmin: 0,\r\n\t\t\t\tmax: 100,\r\n\t\t\t\ttickColor: KTUtil.getCssVariableValue('--bs-light-dark'),\r\n\t\t\t\ttickFormatter: function(v) {\r\n\t\t\t\t\treturn v + \"%\";\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\txaxis: {\r\n\t\t\t\tshow: false,\r\n\t\t\t},\r\n\t\t\tcolors: [KTUtil.getCssVariableValue('--bs-active-primary')],\r\n\t\t\tgrid: {\r\n\t\t\t\ttickColor: KTUtil.getCssVariableValue('--bs-light-dark'),\r\n\t\t\t\tborderWidth: 0,\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\tvar updateInterval = 30;\r\n\t\tvar plot = $.plot($(\"#kt_docs_flot_dynamic\"), [getRandomData()], options);\r\n\r\n\t\tfunction update() {\r\n\t\t\tplot.setData([getRandomData()]);\r\n\t\t\tplot.draw();\r\n\t\t\tsetTimeout(update, updateInterval);\r\n\t\t}\r\n\r\n\t\tupdate();\r\n    }\r\n\r\n    return {\r\n        // Public Functions\r\n        init: function () {\r\n            exampleDynamic();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function () {\r\n    KTFlotDemoDynamic.init();\r\n});\r\n"], "names": [], "sourceRoot": ""}