{"version": 3, "file": "js/custom/documentation/charts/flotcharts/pie.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,kFAAkF;AAChG,cAAc,oFAAoF;AAClG,cAAc,iFAAiF;AAC/F,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC", "sources": ["webpack://keenthemes/../src/js/custom/documentation/charts/flotcharts/pie.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTFlotDemoPie = function () {\r\n    // Private functions\r\n    var examplePie = function () {\r\n        var data = [\r\n            { label: \"CSS\", data: 10, color: KTUtil.getCssVariableValue('--bs-active-primary') },\r\n            { label: \"HTML5\", data: 40, color: KTUtil.getCssVariableValue('--bs-active-success') },\r\n            { label: \"PHP\", data: 30, color: KTUtil.getCssVariableValue('--bs-active-danger') },\r\n            { label: \"Angular\", data: 20, color: KTUtil.getCssVariableValue('--bs-active-warning') }\r\n        ];\r\n\r\n        $.plot($(\"#kt_docs_flot_pie\"), data, {\r\n            series: {\r\n                pie: {\r\n                    show: true\r\n                }\r\n            }\r\n        });\r\n    }\r\n\r\n    return {\r\n        // Public Functions\r\n        init: function () {\r\n            examplePie();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function () {\r\n    KTFlotDemoPie.init();\r\n});\r\n"], "names": [], "sourceRoot": ""}