{"version": 3, "file": "js/custom/documentation/charts/flotcharts/basic.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,iBAAiB;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,iBAAiB;AACzC;AACA;AACA;AACA,wBAAwB,iBAAiB;AACzC;AACA;AACA;AACA,wBAAwB,iBAAiB;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,aAAa;AACb;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,aAAa;AACb;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC", "sources": ["webpack://keenthemes/../src/js/custom/documentation/charts/flotcharts/basic.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTFlotDemoBasic = function () {\r\n    // Private functions\r\n    var exampleBasic = function () {\r\n        var data = [];\r\n        var totalPoints = 250;\r\n\r\n        // random data generator for plot charts\r\n\r\n        function getRandomData() {\r\n            if (data.length > 0) data = data.slice(1);\r\n            // do a random walk\r\n            while (data.length < totalPoints) {\r\n                var prev = data.length > 0 ? data[data.length - 1] : 50;\r\n                var y = prev + Math.random() * 10 - 5;\r\n                if (y < 0) y = 0;\r\n                if (y > 100) y = 100;\r\n                data.push(y);\r\n            }\r\n            // zip the generated y values with the x values\r\n            var res = [];\r\n            for (var i = 0; i < data.length; ++i) {\r\n                res.push([i, data[i]]);\r\n            }\r\n\r\n            return res;\r\n        }\r\n\r\n        var d1 = [];\r\n        for (var i = 0; i < Math.PI * 2; i += 0.25)\r\n            d1.push([i, Math.sin(i)]);\r\n\r\n        var d2 = [];\r\n        for (var i = 0; i < Math.PI * 2; i += 0.25)\r\n            d2.push([i, Math.cos(i)]);\r\n\r\n        var d3 = [];\r\n        for (var i = 0; i < Math.PI * 2; i += 0.1)\r\n            d3.push([i, Math.tan(i)]);\r\n\r\n        $.plot($(\"#kt_docs_flot_basic\"), [{\r\n            label: \"sin(x)\",\r\n            data: d1,\r\n            lines: {\r\n                lineWidth: 1,\r\n            },\r\n            shadowSize: 0\r\n        }, {\r\n            label: \"cos(x)\",\r\n            data: d2,\r\n            lines: {\r\n                lineWidth: 1,\r\n            },\r\n            shadowSize: 0\r\n        }, {\r\n            label: \"tan(x)\",\r\n            data: d3,\r\n            lines: {\r\n                lineWidth: 1,\r\n            },\r\n            shadowSize: 0\r\n        }], {\r\n            colors: [KTUtil.getCssVariableValue('--bs-active-success'), KTUtil.getCssVariableValue('--bs-active-primary'), KTUtil.getCssVariableValue('--bs-active-danger')],\r\n            series: {\r\n                lines: {\r\n                    show: true,\r\n                },\r\n                points: {\r\n                    show: true,\r\n                    fill: true,\r\n                    radius: 3,\r\n                    lineWidth: 1\r\n                }\r\n            },\r\n            xaxis: {\r\n                tickColor: KTUtil.getCssVariableValue('--bs-light-dark'),\r\n                ticks: [0, [Math.PI / 2, \"\\u03c0/2\"],\r\n                    [Math.PI, \"\\u03c0\"],\r\n                    [Math.PI * 3 / 2, \"3\\u03c0/2\"],\r\n                    [Math.PI * 2, \"2\\u03c0\"]\r\n                ]\r\n            },\r\n            yaxis: {\r\n                tickColor: KTUtil.getCssVariableValue('--bs-light-dark'),\r\n                ticks: 10,\r\n                min: -2,\r\n                max: 2\r\n            },\r\n            grid: {\r\n                borderColor: KTUtil.getCssVariableValue('--bs-light-dark'),\r\n                borderWidth: 1\r\n            }\r\n        });\r\n    }\r\n\r\n    return {\r\n        // Public Functions\r\n        init: function () {\r\n            exampleBasic();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function () {\r\n    KTFlotDemoBasic.init();\r\n});\r\n"], "names": [], "sourceRoot": ""}