{"version": 3, "file": "js/custom/documentation/charts/flotcharts/stack.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,SAAS;AAC3B;AACA;AACA;AACA,kBAAkB,SAAS;AAC3B;AACA;AACA;AACA,kBAAkB,SAAS;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,MAAM;AACN;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,MAAM;AACN;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC", "sources": ["webpack://keenthemes/../src/js/custom/documentation/charts/flotcharts/stack.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTFlotDemoStack = function () {\r\n    // Private functions\r\n    var exampleStack = function () {\r\n        var d1 = [];\r\n\t\tfor (var i = 0; i <= 10; i += 1)\r\n\t\t\td1.push([i, parseInt(Math.random() * 30)]);\r\n\r\n\t\tvar d2 = [];\r\n\t\tfor (var i = 0; i <= 10; i += 1)\r\n\t\t\td2.push([i, parseInt(Math.random() * 30)]);\r\n\r\n\t\tvar d3 = [];\r\n\t\tfor (var i = 0; i <= 10; i += 1)\r\n\t\t\td3.push([i, parseInt(Math.random() * 30)]);\r\n\r\n\t\tvar stack = 0,\r\n\t\t\tbars = true,\r\n\t\t\tlines = false,\r\n\t\t\tsteps = false;\r\n\r\n\t\tfunction plotWithOptions() {\r\n\t\t\t$.plot($(\"#kt_docs_flot_stack\"),\r\n\r\n\t\t\t\t[{\r\n\t\t\t\t\tlabel: \"sales\",\r\n\t\t\t\t\tdata: d1,\r\n\t\t\t\t\tlines: {\r\n\t\t\t\t\t\tlineWidth: 1,\r\n\t\t\t\t\t},\r\n\t\t\t\t\tshadowSize: 0\r\n\t\t\t\t}, {\r\n\t\t\t\t\tlabel: \"tax\",\r\n\t\t\t\t\tdata: d2,\r\n\t\t\t\t\tlines: {\r\n\t\t\t\t\t\tlineWidth: 1,\r\n\t\t\t\t\t},\r\n\t\t\t\t\tshadowSize: 0\r\n\t\t\t\t}, {\r\n\t\t\t\t\tlabel: \"profit\",\r\n\t\t\t\t\tdata: d3,\r\n\t\t\t\t\tlines: {\r\n\t\t\t\t\t\tlineWidth: 1,\r\n\t\t\t\t\t},\r\n\t\t\t\t\tshadowSize: 0\r\n\t\t\t\t}], {\r\n\t\t\t\t\tcolors: [KTUtil.getCssVariableValue('--bs-active-danger'), KTUtil.getCssVariableValue('--bs-active-primary')],\r\n\t\t\t\t\tseries: {\r\n\t\t\t\t\t\tstack: stack,\r\n\t\t\t\t\t\tlines: {\r\n\t\t\t\t\t\t\tshow: lines,\r\n\t\t\t\t\t\t\tfill: true,\r\n\t\t\t\t\t\t\tsteps: steps,\r\n\t\t\t\t\t\t\tlineWidth: 0, // in pixels\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tbars: {\r\n\t\t\t\t\t\t\tshow: bars,\r\n\t\t\t\t\t\t\tbarWidth: 0.5,\r\n\t\t\t\t\t\t\tlineWidth: 0, // in pixels\r\n\t\t\t\t\t\t\tshadowSize: 0,\r\n\t\t\t\t\t\t\talign: 'center'\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\tgrid: {\r\n\t\t\t\t\t\ttickColor: KTUtil.getCssVariableValue('--bs-light-dark'),\r\n\t\t\t\t\t\tborderColor: KTUtil.getCssVariableValue('--bs-light-dark'),\r\n\t\t\t\t\t\tborderWidth: 1\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t);\r\n\t\t}\r\n\r\n\t\t$(\".stackControls input\").click(function(e) {\r\n\t\t\te.preventDefault();\r\n\t\t\tstack = $(this).val() == \"With stacking\" ? true : null;\r\n\t\t\tplotWithOptions();\r\n\t\t});\r\n\r\n\t\t$(\".graphControls input\").click(function(e) {\r\n\t\t\te.preventDefault();\r\n\t\t\tbars = $(this).val().indexOf(\"Bars\") != -1;\r\n\t\t\tlines = $(this).val().indexOf(\"Lines\") != -1;\r\n\t\t\tsteps = $(this).val().indexOf(\"steps\") != -1;\r\n\t\t\tplotWithOptions();\r\n\t\t});\r\n\r\n\t\tplotWithOptions();\r\n    }\r\n\r\n    return {\r\n        // Public Functions\r\n        init: function () {\r\n            exampleStack();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function () {\r\n    KTFlotDemoStack.init();\r\n});\r\n"], "names": [], "sourceRoot": ""}