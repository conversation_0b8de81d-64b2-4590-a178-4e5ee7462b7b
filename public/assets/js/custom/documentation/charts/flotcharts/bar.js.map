{"version": 3, "file": "js/custom/documentation/charts/flotcharts/bar.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC", "sources": ["webpack://keenthemes/../src/js/custom/documentation/charts/flotcharts/bar.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTFlotDemoBar = function () {\r\n    // Private functions\r\n    var exampleBar = function () {\r\n        // horizontal bar chart:\r\n\t\tvar data1 = [\r\n\t\t\t[10, 10],\r\n\t\t\t[20, 20],\r\n\t\t\t[30, 30],\r\n\t\t\t[40, 40],\r\n\t\t\t[50, 50],\r\n            [60, 60],\r\n            [70, 70],\r\n            [80, 80],\r\n            [90, 90],\r\n            [100, 100],\r\n\t\t];\r\n\r\n\t\tvar options = {\r\n\t\t\tcolors: [KTUtil.getCssVariableValue('--bs-active-primary')],\r\n\t\t\tseries: {\r\n\t\t\t\tbars: {\r\n\t\t\t\t\tshow: true\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tbars: {\r\n\t\t\t\thorizontal: true,\r\n\t\t\t\tbarWidth: 6,\r\n\t\t\t\tlineWidth: 0, // in pixels\r\n\t\t\t\tshadowSize: 0,\r\n\t\t\t\talign: 'left'\r\n\t\t\t},\r\n\t\t\tgrid: {\r\n\t\t\t\ttickColor: KTUtil.getCssVariableValue('--bs-light-dark'),\r\n\t\t\t\tborderColor: KTUtil.getCssVariableValue('--bs-light-dark'),\r\n\t\t\t\tborderWidth: 1\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\t$.plot($(\"#kt_docs_flot_bar\"), [data1], options);\r\n    }\r\n\r\n    return {\r\n        // Public Functions\r\n        init: function () {\r\n            exampleBar();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function () {\r\n    KTFlotDemoBar.init();\r\n});\r\n"], "names": [], "sourceRoot": ""}