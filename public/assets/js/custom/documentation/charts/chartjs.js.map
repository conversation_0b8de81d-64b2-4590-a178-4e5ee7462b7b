{"version": 3, "file": "js/custom/documentation/charts/chartjs.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,WAAW;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC", "sources": ["webpack://keenthemes/../src/js/custom/documentation/charts/chartjs.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTGeneralChartJS = function () {\r\n    // Randomizer function\r\n    function getRandom(min = 1, max = 100) {\r\n        return Math.floor(Math.random() * (max - min) + min);\r\n    }\r\n\r\n    function generateRandomData(min = 1, max = 100, count = 10) {\r\n        var arr = [];\r\n        for (var i = 0; i < count; i++) {\r\n            arr.push(getRandom(min, max));\r\n        }\r\n        return arr;\r\n    }\r\n\r\n    // Private functions\r\n    var example1 = function () {\r\n        // Define chart element\r\n        var ctx = document.getElementById('kt_chartjs_1');\r\n\r\n        // Define colors\r\n        var primaryColor = KTUtil.getCssVariableValue('--bs-primary');\r\n        var dangerColor = KTUtil.getCssVariableValue('--bs-danger');\r\n        var successColor = KTUtil.getCssVariableValue('--bs-success');\r\n\r\n        // Define fonts\r\n        var fontFamily = KTUtil.getCssVariableValue('--bs-font-sans-serif');\r\n\r\n        // Chart labels\r\n        const labels = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];\r\n\r\n        // Chart data\r\n        const data = {\r\n            labels: labels,\r\n            datasets: [\r\n                {\r\n                    label: 'Dataset 1',\r\n                    data: generateRandomData(1, 100, 12),\r\n                    backgroundColor: primaryColor,\r\n                    stack: 'Stack 0',\r\n                },\r\n                {\r\n                    label: 'Dataset 2',\r\n                    data: generateRandomData(1, 100, 12),\r\n                    backgroundColor: dangerColor,\r\n                    stack: 'Stack 1',\r\n                },\r\n                {\r\n                    label: 'Dataset 3',\r\n                    data: generateRandomData(1, 100, 12),\r\n                    backgroundColor: successColor,\r\n                    stack: 'Stack 2',\r\n                },\r\n            ]\r\n        };\r\n\r\n        // Chart config\r\n        const config = {\r\n            type: 'bar',\r\n            data: data,\r\n            options: {\r\n                plugins: {\r\n                    title: {\r\n                        display: false,\r\n                    }\r\n                },\r\n                responsive: true,\r\n                interaction: {\r\n                    intersect: false,\r\n                },\r\n                scales: {\r\n                    x: {\r\n                        stacked: true,\r\n                    },\r\n                    y: {\r\n                        stacked: true\r\n                    }\r\n                }\r\n            }\r\n        };\r\n\r\n        // Init ChartJS -- for more info, please visit: https://www.chartjs.org/docs/latest/\r\n        var myChart = new Chart(ctx, config);\r\n    }\r\n\r\n    var example2 = function () {\r\n        // Define chart element\r\n        var ctx = document.getElementById('kt_chartjs_2');\r\n\r\n        // Define colors\r\n        var primaryColor = KTUtil.getCssVariableValue('--bs-primary');\r\n        var dangerColor = KTUtil.getCssVariableValue('--bs-danger');\r\n        var successColor = KTUtil.getCssVariableValue('--bs-success');\r\n\r\n        // Define fonts\r\n        var fontFamily = KTUtil.getCssVariableValue('--bs-font-sans-serif');\r\n\r\n        // Chart labels\r\n        const labels = ['January', 'February', 'March', 'April', 'May', 'June', 'July'];\r\n\r\n        // Chart data\r\n        const data = {\r\n            labels: labels,\r\n            datasets: [\r\n                {\r\n                    label: 'Dataset 1',\r\n                    data: generateRandomData(1, 50, 7),\r\n                    borderColor: primaryColor,\r\n                    backgroundColor: 'transparent'\r\n                },\r\n                {\r\n                    label: 'Dataset 2',\r\n                    data: generateRandomData(1, 50, 7),\r\n                    borderColor: dangerColor,\r\n                    backgroundColor: 'transparent'\r\n                },\r\n            ]\r\n        };\r\n\r\n        // Chart config\r\n        const config = {\r\n            type: 'line',\r\n            data: data,\r\n            options: {\r\n                plugins: {\r\n                    title: {\r\n                        display: false,\r\n                    }\r\n                },\r\n                responsive: true,\r\n            }\r\n        };\r\n\r\n        // Init ChartJS -- for more info, please visit: https://www.chartjs.org/docs/latest/\r\n        var myChart = new Chart(ctx, config);\r\n    }\r\n\r\n    var example3 = function () {\r\n        // Define chart element\r\n        var ctx = document.getElementById('kt_chartjs_3');\r\n\r\n        // Define colors\r\n        var primaryColor = KTUtil.getCssVariableValue('--bs-primary');\r\n        var dangerColor = KTUtil.getCssVariableValue('--bs-danger');\r\n        var successColor = KTUtil.getCssVariableValue('--bs-success');\r\n        var warningColor = KTUtil.getCssVariableValue('--bs-warning');\r\n        var infoColor = KTUtil.getCssVariableValue('--bs-info');\r\n\r\n        // Chart labels\r\n        const labels = ['January', 'February', 'March', 'April', 'May'];\r\n\r\n        // Chart data\r\n        const data = {\r\n            labels: labels,\r\n            datasets: [\r\n                {\r\n                    label: 'Dataset 1',\r\n                    data: generateRandomData(1, 100, 5),\r\n                    backgroundColor: [primaryColor, dangerColor, successColor, warningColor, infoColor]\r\n                },\r\n            ]\r\n        };\r\n\r\n        // Chart config\r\n        const config = {\r\n            type: 'pie',\r\n            data: data,\r\n            options: {\r\n                plugins: {\r\n                    title: {\r\n                        display: false,\r\n                    }\r\n                },\r\n                responsive: true,\r\n            }\r\n        };\r\n\r\n        // Init ChartJS -- for more info, please visit: https://www.chartjs.org/docs/latest/\r\n        var myChart = new Chart(ctx, config);\r\n    }\r\n\r\n    var example4 = function () {\r\n        // Define chart element\r\n        var ctx = document.getElementById('kt_chartjs_4');\r\n\r\n        // Define colors\r\n        var primaryColor = KTUtil.getCssVariableValue('--bs-primary');\r\n        var dangerColor = KTUtil.getCssVariableValue('--bs-danger');\r\n        var dangerLightColor = KTUtil.getCssVariableValue('--bs-light-danger');\r\n\r\n        // Define fonts\r\n        var fontFamily = KTUtil.getCssVariableValue('--bs-font-sans-serif');\r\n\r\n        // Chart labels\r\n        const labels = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];\r\n\r\n        // Chart data\r\n        const data = {\r\n            labels: labels,\r\n            datasets: [\r\n                {\r\n                    label: 'Dataset 1',\r\n                    data: generateRandomData(50, 100, 12),\r\n                    borderColor: primaryColor,\r\n                    backgroundColor: 'transparent',\r\n                    stack: 'combined'\r\n                },\r\n                {\r\n                    label: 'Dataset 2',\r\n                    data: generateRandomData(1, 60, 12),\r\n                    backgroundColor: dangerColor,\r\n                    borderColor: dangerColor,\r\n                    stack: 'combined',\r\n                    type: 'bar'\r\n                },\r\n                \r\n            ]\r\n        };\r\n\r\n        // Chart config\r\n        const config = {\r\n            type: 'line',\r\n            data: data,\r\n            options: {\r\n                plugins: {\r\n                    title: {\r\n                        display: false,\r\n                    }\r\n                },\r\n                responsive: true,\r\n                interaction: {\r\n                    intersect: false,\r\n                },\r\n                scales: {\r\n                    y: {\r\n                        stacked: true\r\n                    }\r\n                }\r\n            },\r\n            defaults: {\r\n                font: {\r\n                    family: 'inherit',\r\n                }\r\n            }\r\n        };\r\n\r\n        // Init ChartJS -- for more info, please visit: https://www.chartjs.org/docs/latest/\r\n        var myChart = new Chart(ctx, config);\r\n    }\r\n\r\n    var example5 = function () {\r\n        // Define chart element\r\n        var ctx = document.getElementById('kt_chartjs_5');\r\n\r\n        // Define colors\r\n        var infoColor = KTUtil.getCssVariableValue('--bs-info');\r\n        var infoLightColor = KTUtil.getCssVariableValue('--bs-light-info');\r\n        var warningColor = KTUtil.getCssVariableValue('--bs-warning');\r\n        var warningLightColor = KTUtil.getCssVariableValue('--bs-light-warning');\r\n        var primaryColor = KTUtil.getCssVariableValue('--bs-primary');\r\n        var primaryLightColor = KTUtil.getCssVariableValue('--bs-light-primary');\r\n\r\n        // Define fonts\r\n        var fontFamily = KTUtil.getCssVariableValue('--bs-font-sans-serif');\r\n\r\n        // Chart labels\r\n        const labels = ['January', 'February', 'March', 'April', 'May', 'June'];\r\n\r\n        // Chart data\r\n        const data = {\r\n            labels: labels,\r\n            datasets: [\r\n                {\r\n                    label: 'Dataset 1',\r\n                    data: generateRandomData(20, 80, 6),\r\n                    borderColor: infoColor,\r\n                    backgroundColor: infoLightColor,\r\n                },\r\n                {\r\n                    label: 'Dataset 2',\r\n                    data: generateRandomData(10, 60, 6),\r\n                    backgroundColor: warningLightColor,\r\n                    borderColor: warningColor,\r\n                },\r\n                {\r\n                    label: 'Dataset 3',\r\n                    data: generateRandomData(0, 80, 6),\r\n                    backgroundColor: primaryLightColor,\r\n                    borderColor: primaryColor,\r\n                },                \r\n            ]\r\n        };\r\n\r\n        // Chart config\r\n        const config = {\r\n            type: 'radar',\r\n            data: data,\r\n            options: {\r\n                plugins: {\r\n                    title: {\r\n                        display: false,\r\n                    }\r\n                },\r\n                responsive: true,\r\n            }\r\n        };\r\n\r\n        // Init ChartJS -- for more info, please visit: https://www.chartjs.org/docs/latest/\r\n        var myChart = new Chart(ctx, config);\r\n    }\r\n\r\n    return {\r\n        // Public Functions\r\n        init: function () {\r\n            // Global font settings: https://www.chartjs.org/docs/latest/general/fonts.html\r\n            Chart.defaults.font.size = 13;\r\n            Chart.defaults.font.family = KTUtil.getCssVariableValue('--bs-font-sans-serif');\r\n\r\n            example1();\r\n            example2();\r\n            example3();\r\n            example4();\r\n            example5();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function () {\r\n    KTGeneralChartJS.init();\r\n});\r\n"], "names": [], "sourceRoot": ""}