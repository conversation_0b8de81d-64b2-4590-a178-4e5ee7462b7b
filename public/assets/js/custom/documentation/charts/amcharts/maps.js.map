{"version": 3, "file": "js/custom/documentation/charts/amcharts/maps.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4CAA4C,KAAK;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2DAA2D,MAAM;AACjE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,kDAAkD,uEAAuE,IAAI,qCAAqC;AAClK;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,aAAa;AACb;AACA,SAAS,GAAG;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC,0CAA0C;AAC5E,oCAAoC,4CAA4C;AAChF,+BAA+B,wCAAwC;AACvE,mCAAmC,kCAAkC;AACrE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6CAA6C,6BAA6B;AAC1E;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6CAA6C,+BAA+B;AAC5E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,iBAAiB,EAAE;AACnB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,GAAG;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,GAAG;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yCAAyC,GAAG;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,2BAA2B,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,aAAa;AACb;AACA,SAAS,GAAG;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC", "sources": ["webpack://keenthemes/../../../themes/metronic/html/demo3/src/js/custom/documentation/charts/amcharts/maps.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTGeneralAmChartsMaps = function () {\r\n    // Shared variables\r\n    var chart;\r\n\r\n    // Private functions\r\n    var _demo1 = function () {\r\n        // Init AmChart -- for more info, please visit the official documentiation: https://www.amcharts.com/docs/v4/\r\n        am4core.ready(function () {\r\n\r\n            // Themes begin\r\n            am4core.useTheme(am4themes_animated);\r\n            // Themes end\r\n\r\n            // Create map instance\r\n            chart = am4core.create(\"kt_amcharts_1\", am4maps.MapChart);\r\n\r\n            // Set map definition\r\n            chart.geodata = am4geodata_worldLow;\r\n\r\n            // Set projection\r\n            chart.projection = new am4maps.projections.Miller();\r\n\r\n            // Create map polygon series\r\n            var polygonSeries = chart.series.push(new am4maps.MapPolygonSeries());\r\n\r\n            // Exclude Antartica\r\n            polygonSeries.exclude = [\"AQ\"];\r\n\r\n            // Make map load polygon (like country names) data from GeoJSON\r\n            polygonSeries.useGeodata = true;\r\n\r\n            // Configure series\r\n            var polygonTemplate = polygonSeries.mapPolygons.template;\r\n            polygonTemplate.tooltipText = \"{name}\";\r\n            polygonTemplate.polygon.fillOpacity = 0.6;\r\n\r\n\r\n            // Create hover state and set alternative fill color\r\n            var hs = polygonTemplate.states.create(\"hover\");\r\n            hs.properties.fill = chart.colors.getIndex(0);\r\n\r\n            // Add image series\r\n            var imageSeries = chart.series.push(new am4maps.MapImageSeries());\r\n            imageSeries.mapImages.template.propertyFields.longitude = \"longitude\";\r\n            imageSeries.mapImages.template.propertyFields.latitude = \"latitude\";\r\n            imageSeries.mapImages.template.tooltipText = \"{title}\";\r\n            imageSeries.mapImages.template.propertyFields.url = \"url\";\r\n\r\n            var circle = imageSeries.mapImages.template.createChild(am4core.Circle);\r\n            circle.radius = 3;\r\n            circle.propertyFields.fill = \"color\";\r\n            circle.nonScaling = true;\r\n\r\n            var circle2 = imageSeries.mapImages.template.createChild(am4core.Circle);\r\n            circle2.radius = 3;\r\n            circle2.propertyFields.fill = \"color\";\r\n\r\n\r\n            circle2.events.on(\"inited\", function (event) {\r\n                animateBullet(event.target);\r\n            })\r\n\r\n\r\n            function animateBullet(circle) {\r\n                var animation = circle.animate([{ property: \"scale\", from: 1 / chart.zoomLevel, to: 5 / chart.zoomLevel }, { property: \"opacity\", from: 1, to: 0 }], 1000, am4core.ease.circleOut);\r\n                animation.events.on(\"animationended\", function (event) {\r\n                    animateBullet(event.target.object);\r\n                })\r\n            }\r\n\r\n            var colorSet = new am4core.ColorSet();\r\n\r\n            imageSeries.data = [{\r\n                \"title\": \"Brussels\",\r\n                \"latitude\": 50.8371,\r\n                \"longitude\": 4.3676,\r\n                \"color\": colorSet.next()\r\n            }, {\r\n                \"title\": \"Copenhagen\",\r\n                \"latitude\": 55.6763,\r\n                \"longitude\": 12.5681,\r\n                \"color\": colorSet.next()\r\n            }, {\r\n                \"title\": \"Paris\",\r\n                \"latitude\": 48.8567,\r\n                \"longitude\": 2.3510,\r\n                \"color\": colorSet.next()\r\n            }, {\r\n                \"title\": \"Reykjavik\",\r\n                \"latitude\": 64.1353,\r\n                \"longitude\": -21.8952,\r\n                \"color\": colorSet.next()\r\n            }, {\r\n                \"title\": \"Moscow\",\r\n                \"latitude\": 55.7558,\r\n                \"longitude\": 37.6176,\r\n                \"color\": colorSet.next()\r\n            }, {\r\n                \"title\": \"Madrid\",\r\n                \"latitude\": 40.4167,\r\n                \"longitude\": -3.7033,\r\n                \"color\": colorSet.next()\r\n            }, {\r\n                \"title\": \"London\",\r\n                \"latitude\": 51.5002,\r\n                \"longitude\": -0.1262,\r\n                \"url\": \"http://www.google.co.uk\",\r\n                \"color\": colorSet.next()\r\n            }, {\r\n                \"title\": \"Peking\",\r\n                \"latitude\": 39.9056,\r\n                \"longitude\": 116.3958,\r\n                \"color\": colorSet.next()\r\n            }, {\r\n                \"title\": \"New Delhi\",\r\n                \"latitude\": 28.6353,\r\n                \"longitude\": 77.2250,\r\n                \"color\": colorSet.next()\r\n            }, {\r\n                \"title\": \"Tokyo\",\r\n                \"latitude\": 35.6785,\r\n                \"longitude\": 139.6823,\r\n                \"url\": \"http://www.google.co.jp\",\r\n                \"color\": colorSet.next()\r\n            }, {\r\n                \"title\": \"Ankara\",\r\n                \"latitude\": 39.9439,\r\n                \"longitude\": 32.8560,\r\n                \"color\": colorSet.next()\r\n            }, {\r\n                \"title\": \"Buenos Aires\",\r\n                \"latitude\": -34.6118,\r\n                \"longitude\": -58.4173,\r\n                \"color\": colorSet.next()\r\n            }, {\r\n                \"title\": \"Brasilia\",\r\n                \"latitude\": -15.7801,\r\n                \"longitude\": -47.9292,\r\n                \"color\": colorSet.next()\r\n            }, {\r\n                \"title\": \"Ottawa\",\r\n                \"latitude\": 45.4235,\r\n                \"longitude\": -75.6979,\r\n                \"color\": colorSet.next()\r\n            }, {\r\n                \"title\": \"Washington\",\r\n                \"latitude\": 38.8921,\r\n                \"longitude\": -77.0241,\r\n                \"color\": colorSet.next()\r\n            }, {\r\n                \"title\": \"Kinshasa\",\r\n                \"latitude\": -4.3369,\r\n                \"longitude\": 15.3271,\r\n                \"color\": colorSet.next()\r\n            }, {\r\n                \"title\": \"Cairo\",\r\n                \"latitude\": 30.0571,\r\n                \"longitude\": 31.2272,\r\n                \"color\": colorSet.next()\r\n            }, {\r\n                \"title\": \"Pretoria\",\r\n                \"latitude\": -25.7463,\r\n                \"longitude\": 28.1876,\r\n                \"color\": colorSet.next()\r\n            }];\r\n\r\n        }); // end am4core.ready()\r\n    }\r\n\r\n    var _demo2 = function () {\r\n        // Init AmChart -- for more info, please visit the official documentiation: https://www.amcharts.com/docs/v4/\r\n        am4core.ready(function () {\r\n\r\n            // Themes begin\r\n            am4core.useTheme(am4themes_animated);\r\n            // Themes end\r\n\r\n            // Create map instance\r\n            chart = am4core.create(\"kt_amcharts_2\", am4maps.MapChart);\r\n            chart.geodata = am4geodata_worldLow;\r\n            chart.projection = new am4maps.projections.Miller();\r\n            chart.homeZoomLevel = 2.5;\r\n            chart.homeGeoPoint = {\r\n                latitude: 38,\r\n                longitude: -60\r\n            };\r\n\r\n            // Create map polygon series\r\n            var polygonSeries = chart.series.push(new am4maps.MapPolygonSeries());\r\n            polygonSeries.useGeodata = true;\r\n            polygonSeries.mapPolygons.template.fill = chart.colors.getIndex(0).lighten(0.5);\r\n            polygonSeries.mapPolygons.template.nonScalingStroke = true;\r\n            polygonSeries.exclude = [\"AQ\"];\r\n\r\n            // Add line bullets\r\n            var cities = chart.series.push(new am4maps.MapImageSeries());\r\n            cities.mapImages.template.nonScaling = true;\r\n\r\n            var city = cities.mapImages.template.createChild(am4core.Circle);\r\n            city.radius = 6;\r\n            city.fill = chart.colors.getIndex(0).brighten(-0.2);\r\n            city.strokeWidth = 2;\r\n            city.stroke = am4core.color(\"#fff\");\r\n\r\n            function addCity(coords, title) {\r\n                var city = cities.mapImages.create();\r\n                city.latitude = coords.latitude;\r\n                city.longitude = coords.longitude;\r\n                city.tooltipText = title;\r\n                return city;\r\n            }\r\n\r\n            var paris = addCity({ \"latitude\": 48.8567, \"longitude\": 2.3510 }, \"Paris\");\r\n            var toronto = addCity({ \"latitude\": 43.8163, \"longitude\": -79.4287 }, \"Toronto\");\r\n            var la = addCity({ \"latitude\": 34.3, \"longitude\": -118.15 }, \"Los Angeles\");\r\n            var havana = addCity({ \"latitude\": 23, \"longitude\": -82 }, \"Havana\");\r\n\r\n            // Add lines\r\n            var lineSeries = chart.series.push(new am4maps.MapArcSeries());\r\n            lineSeries.mapLines.template.line.strokeWidth = 2;\r\n            lineSeries.mapLines.template.line.strokeOpacity = 0.5;\r\n            lineSeries.mapLines.template.line.stroke = city.fill;\r\n            lineSeries.mapLines.template.line.nonScalingStroke = true;\r\n            lineSeries.mapLines.template.line.strokeDasharray = \"1,1\";\r\n            lineSeries.zIndex = 10;\r\n\r\n            var shadowLineSeries = chart.series.push(new am4maps.MapLineSeries());\r\n            shadowLineSeries.mapLines.template.line.strokeOpacity = 0;\r\n            shadowLineSeries.mapLines.template.line.nonScalingStroke = true;\r\n            shadowLineSeries.mapLines.template.shortestDistance = false;\r\n            shadowLineSeries.zIndex = 5;\r\n\r\n            function addLine(from, to) {\r\n                var line = lineSeries.mapLines.create();\r\n                line.imagesToConnect = [from, to];\r\n                line.line.controlPointDistance = -0.3;\r\n\r\n                var shadowLine = shadowLineSeries.mapLines.create();\r\n                shadowLine.imagesToConnect = [from, to];\r\n\r\n                return line;\r\n            }\r\n\r\n            addLine(paris, toronto);\r\n            addLine(toronto, la);\r\n            addLine(la, havana);\r\n\r\n            // Add plane\r\n            var plane = lineSeries.mapLines.getIndex(0).lineObjects.create();\r\n            plane.position = 0;\r\n            plane.width = 48;\r\n            plane.height = 48;\r\n\r\n            plane.adapter.add(\"scale\", function (scale, target) {\r\n                return 0.5 * (1 - (Math.abs(0.5 - target.position)));\r\n            })\r\n\r\n            var planeImage = plane.createChild(am4core.Sprite);\r\n            planeImage.scale = 0.08;\r\n            planeImage.horizontalCenter = \"middle\";\r\n            planeImage.verticalCenter = \"middle\";\r\n            planeImage.path = \"m2,106h28l24,30h72l-44,-133h35l80,132h98c21,0 21,34 0,34l-98,0 -80,134h-35l43,-133h-71l-24,30h-28l15,-47\";\r\n            planeImage.fill = chart.colors.getIndex(2).brighten(-0.2);\r\n            planeImage.strokeOpacity = 0;\r\n\r\n            var shadowPlane = shadowLineSeries.mapLines.getIndex(0).lineObjects.create();\r\n            shadowPlane.position = 0;\r\n            shadowPlane.width = 48;\r\n            shadowPlane.height = 48;\r\n\r\n            var shadowPlaneImage = shadowPlane.createChild(am4core.Sprite);\r\n            shadowPlaneImage.scale = 0.05;\r\n            shadowPlaneImage.horizontalCenter = \"middle\";\r\n            shadowPlaneImage.verticalCenter = \"middle\";\r\n            shadowPlaneImage.path = \"m2,106h28l24,30h72l-44,-133h35l80,132h98c21,0 21,34 0,34l-98,0 -80,134h-35l43,-133h-71l-24,30h-28l15,-47\";\r\n            shadowPlaneImage.fill = am4core.color(\"#000\");\r\n            shadowPlaneImage.strokeOpacity = 0;\r\n\r\n            shadowPlane.adapter.add(\"scale\", function (scale, target) {\r\n                target.opacity = (0.6 - (Math.abs(0.5 - target.position)));\r\n                return 0.5 - 0.3 * (1 - (Math.abs(0.5 - target.position)));\r\n            })\r\n\r\n            // Plane animation\r\n            var currentLine = 0;\r\n            var direction = 1;\r\n            function flyPlane() {\r\n\r\n                // Get current line to attach plane to\r\n                plane.mapLine = lineSeries.mapLines.getIndex(currentLine);\r\n                plane.parent = lineSeries;\r\n                shadowPlane.mapLine = shadowLineSeries.mapLines.getIndex(currentLine);\r\n                shadowPlane.parent = shadowLineSeries;\r\n                shadowPlaneImage.rotation = planeImage.rotation;\r\n\r\n                // Set up animation\r\n                var from, to;\r\n                var numLines = lineSeries.mapLines.length;\r\n                if (direction == 1) {\r\n                    from = 0\r\n                    to = 1;\r\n                    if (planeImage.rotation != 0) {\r\n                        planeImage.animate({ to: 0, property: \"rotation\" }, 1000).events.on(\"animationended\", flyPlane);\r\n                        return;\r\n                    }\r\n                }\r\n                else {\r\n                    from = 1;\r\n                    to = 0;\r\n                    if (planeImage.rotation != 180) {\r\n                        planeImage.animate({ to: 180, property: \"rotation\" }, 1000).events.on(\"animationended\", flyPlane);\r\n                        return;\r\n                    }\r\n                }\r\n\r\n                // Start the animation\r\n                var animation = plane.animate({\r\n                    from: from,\r\n                    to: to,\r\n                    property: \"position\"\r\n                }, 5000, am4core.ease.sinInOut);\r\n                animation.events.on(\"animationended\", flyPlane)\r\n                /*animation.events.on(\"animationprogress\", function(ev) {\r\n                  var progress = Math.abs(ev.progress - 0.5);\r\n                  //console.log(progress);\r\n                  //planeImage.scale += 0.2;\r\n                });*/\r\n\r\n                shadowPlane.animate({\r\n                    from: from,\r\n                    to: to,\r\n                    property: \"position\"\r\n                }, 5000, am4core.ease.sinInOut);\r\n\r\n                // Increment line, or reverse the direction\r\n                currentLine += direction;\r\n                if (currentLine < 0) {\r\n                    currentLine = 0;\r\n                    direction = 1;\r\n                }\r\n                else if ((currentLine + 1) > numLines) {\r\n                    currentLine = numLines - 1;\r\n                    direction = -1;\r\n                }\r\n\r\n            }\r\n\r\n            // Go!\r\n            flyPlane();\r\n\r\n        }); // end am4core.ready()\r\n    }\r\n\r\n    var _demo3 = function () {\r\n        // Init AmChart -- for more info, please visit the official documentiation: https://www.amcharts.com/docs/v4/\r\n        am4core.ready(function () {\r\n\r\n            // Themes begin\r\n            am4core.useTheme(am4themes_animated);\r\n            // Themes end\r\n\r\n            // Create map instance\r\n            chart = am4core.create(\"kt_amcharts_3\", am4maps.MapChart);\r\n            var interfaceColors = new am4core.InterfaceColorSet();\r\n\r\n            try {\r\n                chart.geodata = am4geodata_worldLow;\r\n            }\r\n            catch (e) {\r\n                chart.raiseCriticalError(new Error(\"Map geodata could not be loaded. Please download the latest <a href=\\\"https://www.amcharts.com/download/download-v4/\\\">amcharts geodata</a> and extract its contents into the same directory as your amCharts files.\"));\r\n            }\r\n\r\n\r\n            var label = chart.createChild(am4core.Label)\r\n            label.text = \"12 months (3/7/2019 data) rolling measles\\nincidence per 1'000'000 total population. \\n Bullet size uses logarithmic scale.\";\r\n            label.fontSize = 12;\r\n            label.align = \"left\";\r\n            label.valign = \"bottom\"\r\n            label.fill = am4core.color(\"#927459\");\r\n            label.background = new am4core.RoundedRectangle()\r\n            label.background.cornerRadius(10, 10, 10, 10);\r\n            label.padding(10, 10, 10, 10);\r\n            label.marginLeft = 30;\r\n            label.marginBottom = 30;\r\n            label.background.strokeOpacity = 0.3;\r\n            label.background.stroke = am4core.color(\"#927459\");\r\n            label.background.fill = am4core.color(\"#f9e3ce\");\r\n            label.background.fillOpacity = 0.6;\r\n\r\n            var dataSource = chart.createChild(am4core.TextLink)\r\n            dataSource.text = \"Data source: WHO\";\r\n            dataSource.fontSize = 12;\r\n            dataSource.align = \"left\";\r\n            dataSource.valign = \"top\"\r\n            dataSource.url = \"https://www.who.int/immunization/monitoring_surveillance/burden/vpd/surveillance_type/active/measles_monthlydata/en/\"\r\n            dataSource.urlTarget = \"_blank\";\r\n            dataSource.fill = am4core.color(\"#927459\");\r\n            dataSource.padding(10, 10, 10, 10);\r\n            dataSource.marginLeft = 30;\r\n            dataSource.marginTop = 30;\r\n\r\n            // Set projection\r\n            chart.projection = new am4maps.projections.Orthographic();\r\n            chart.panBehavior = \"rotateLongLat\";\r\n            chart.padding(20, 20, 20, 20);\r\n\r\n            // Add zoom control\r\n            chart.zoomControl = new am4maps.ZoomControl();\r\n\r\n            var homeButton = new am4core.Button();\r\n            homeButton.events.on(\"hit\", function () {\r\n                chart.goHome();\r\n            });\r\n\r\n            homeButton.icon = new am4core.Sprite();\r\n            homeButton.padding(7, 5, 7, 5);\r\n            homeButton.width = 30;\r\n            homeButton.icon.path = \"M16,8 L14,8 L14,16 L10,16 L10,10 L6,10 L6,16 L2,16 L2,8 L0,8 L8,0 L16,8 Z M16,8\";\r\n            homeButton.marginBottom = 10;\r\n            homeButton.parent = chart.zoomControl;\r\n            homeButton.insertBefore(chart.zoomControl.plusButton);\r\n\r\n            chart.backgroundSeries.mapPolygons.template.polygon.fill = am4core.color(\"#bfa58d\");\r\n            chart.backgroundSeries.mapPolygons.template.polygon.fillOpacity = 1;\r\n            chart.deltaLongitude = 20;\r\n            chart.deltaLatitude = -20;\r\n\r\n            // limits vertical rotation\r\n            chart.adapter.add(\"deltaLatitude\", function (delatLatitude) {\r\n                return am4core.math.fitToRange(delatLatitude, -90, 90);\r\n            })\r\n\r\n            // Create map polygon series\r\n\r\n            var shadowPolygonSeries = chart.series.push(new am4maps.MapPolygonSeries());\r\n            shadowPolygonSeries.geodata = am4geodata_continentsLow;\r\n\r\n            try {\r\n                shadowPolygonSeries.geodata = am4geodata_continentsLow;\r\n            }\r\n            catch (e) {\r\n                shadowPolygonSeries.raiseCriticalError(new Error(\"Map geodata could not be loaded. Please download the latest <a href=\\\"https://www.amcharts.com/download/download-v4/\\\">amcharts geodata</a> and extract its contents into the same directory as your amCharts files.\"));\r\n            }\r\n\r\n            shadowPolygonSeries.useGeodata = true;\r\n            shadowPolygonSeries.dx = 2;\r\n            shadowPolygonSeries.dy = 2;\r\n            shadowPolygonSeries.mapPolygons.template.fill = am4core.color(\"#000\");\r\n            shadowPolygonSeries.mapPolygons.template.fillOpacity = 0.2;\r\n            shadowPolygonSeries.mapPolygons.template.strokeOpacity = 0;\r\n            shadowPolygonSeries.fillOpacity = 0.1;\r\n            shadowPolygonSeries.fill = am4core.color(\"#000\");\r\n\r\n\r\n            // Create map polygon series\r\n            var polygonSeries = chart.series.push(new am4maps.MapPolygonSeries());\r\n            polygonSeries.useGeodata = true;\r\n\r\n            polygonSeries.calculateVisualCenter = true;\r\n            polygonSeries.tooltip.background.fillOpacity = 0.2;\r\n            polygonSeries.tooltip.background.cornerRadius = 20;\r\n\r\n            var template = polygonSeries.mapPolygons.template;\r\n            template.nonScalingStroke = true;\r\n            template.fill = am4core.color(\"#f9e3ce\");\r\n            template.stroke = am4core.color(\"#e2c9b0\");\r\n\r\n            polygonSeries.calculateVisualCenter = true;\r\n            template.propertyFields.id = \"id\";\r\n            template.tooltipPosition = \"fixed\";\r\n            template.fillOpacity = 1;\r\n\r\n            template.events.on(\"over\", function (event) {\r\n                if (event.target.dummyData) {\r\n                    event.target.dummyData.isHover = true;\r\n                }\r\n            })\r\n            template.events.on(\"out\", function (event) {\r\n                if (event.target.dummyData) {\r\n                    event.target.dummyData.isHover = false;\r\n                }\r\n            })\r\n\r\n            var hs = polygonSeries.mapPolygons.template.states.create(\"hover\");\r\n            hs.properties.fillOpacity = 1;\r\n            hs.properties.fill = am4core.color(\"#deb7ad\");\r\n\r\n\r\n            var graticuleSeries = chart.series.push(new am4maps.GraticuleSeries());\r\n            graticuleSeries.mapLines.template.stroke = am4core.color(\"#fff\");\r\n            graticuleSeries.fitExtent = false;\r\n            graticuleSeries.mapLines.template.strokeOpacity = 0.2;\r\n            graticuleSeries.mapLines.template.stroke = am4core.color(\"#fff\");\r\n\r\n\r\n            var measelsSeries = chart.series.push(new am4maps.MapPolygonSeries())\r\n            measelsSeries.tooltip.background.fillOpacity = 0;\r\n            measelsSeries.tooltip.background.cornerRadius = 20;\r\n            measelsSeries.tooltip.autoTextColor = false;\r\n            measelsSeries.tooltip.label.fill = am4core.color(\"#000\");\r\n            measelsSeries.tooltip.dy = -5;\r\n\r\n            var measelTemplate = measelsSeries.mapPolygons.template;\r\n            measelTemplate.fill = am4core.color(\"#bf7569\");\r\n            measelTemplate.strokeOpacity = 0;\r\n            measelTemplate.fillOpacity = 0.75;\r\n            measelTemplate.tooltipPosition = \"fixed\";\r\n\r\n\r\n\r\n            var hs2 = measelsSeries.mapPolygons.template.states.create(\"hover\");\r\n            hs2.properties.fillOpacity = 1;\r\n            hs2.properties.fill = am4core.color(\"#86240c\");\r\n\r\n            polygonSeries.events.on(\"inited\", function () {\r\n                polygonSeries.mapPolygons.each(function (mapPolygon) {\r\n                    var count = data[mapPolygon.id];\r\n\r\n                    if (count > 0) {\r\n                        var polygon = measelsSeries.mapPolygons.create();\r\n                        polygon.multiPolygon = am4maps.getCircle(mapPolygon.visualLongitude, mapPolygon.visualLatitude, Math.max(0.2, Math.log(count) * Math.LN10 / 10));\r\n                        polygon.tooltipText = mapPolygon.dataItem.dataContext.name + \": \" + count;\r\n                        mapPolygon.dummyData = polygon;\r\n                        polygon.events.on(\"over\", function () {\r\n                            mapPolygon.isHover = true;\r\n                        })\r\n                        polygon.events.on(\"out\", function () {\r\n                            mapPolygon.isHover = false;\r\n                        })\r\n                    }\r\n                    else {\r\n                        mapPolygon.tooltipText = mapPolygon.dataItem.dataContext.name + \": no data\";\r\n                        mapPolygon.fillOpacity = 0.9;\r\n                    }\r\n\r\n                })\r\n            })\r\n\r\n\r\n            var data = {\r\n                \"AL\": 504.38,\r\n                \"AM\": 6.5,\r\n                \"AO\": 2.98,\r\n                \"AR\": 0.32,\r\n                \"AT\": 10.9,\r\n                \"AU\": 5.02,\r\n                \"AZ\": 17.38,\r\n                \"BA\": 24.45,\r\n                \"BD\": 13.4,\r\n                \"BE\": 12.06,\r\n                \"BF\": 93.37,\r\n                \"BG\": 1.68,\r\n                \"BI\": 0.95,\r\n                \"BJ\": 93.36,\r\n                \"BR\": 49.42,\r\n                \"BT\": 10.03,\r\n                \"BY\": 26.16,\r\n                \"CA\": 0.96,\r\n                \"CD\": 69.71,\r\n                \"CF\": 4.57,\r\n                \"CG\": 19.7,\r\n                \"CH\": 6.19,\r\n                \"CI\": 14.1,\r\n                \"CL\": 1.4,\r\n                \"CM\": 41.26,\r\n                \"CN\": 2.6,\r\n                \"CO\": 4.48,\r\n                \"CY\": 7.69,\r\n                \"CZ\": 23.09,\r\n                \"DK\": 1.58,\r\n                \"EE\": 9.91,\r\n                \"EG\": 0.63,\r\n                \"ES\": 4.96,\r\n                \"FI\": 3.27,\r\n                \"FR\": 43.26,\r\n                \"GA\": 3.03,\r\n                \"GB\": 14.3,\r\n                \"GE\": 809.09,\r\n                \"GH\": 39.78,\r\n                \"GM\": 2.45,\r\n                \"GN\": 45.98,\r\n                \"GQ\": 23.74,\r\n                \"GR\": 154.42,\r\n                \"HR\": 5.46,\r\n                \"HU\": 1.44,\r\n                \"ID\": 16.87,\r\n                \"IE\": 17.56,\r\n                \"IL\": 412.24,\r\n                \"IN\": 47.85,\r\n                \"IQ\": 12.96,\r\n                \"IR\": 1.13,\r\n                \"IT\": 44.29,\r\n                \"JP\": 3.27,\r\n                \"KE\": 16.8,\r\n                \"KG\": 253.37,\r\n                \"KH\": 0.44,\r\n                \"KM\": 1.26,\r\n                \"KZ\": 116.3,\r\n                \"LA\": 1.33,\r\n                \"LK\": 0.53,\r\n                \"LR\": 692.27,\r\n                \"LS\": 5.9,\r\n                \"LT\": 14.44,\r\n                \"LU\": 6.95,\r\n                \"LV\": 6.09,\r\n                \"MA\": 0.2,\r\n                \"MD\": 83.75,\r\n                \"ME\": 319.75,\r\n                \"MG\": 2386.35,\r\n                \"MK\": 28.83,\r\n                \"ML\": 48.68,\r\n                \"MM\": 40.31,\r\n                \"MN\": 0.66,\r\n                \"MR\": 14.65,\r\n                \"MT\": 11.65,\r\n                \"MV\": 9.35,\r\n                \"MX\": 0.04,\r\n                \"MY\": 86.41,\r\n                \"MZ\": 13.49,\r\n                \"NA\": 12.9,\r\n                \"NE\": 80.88,\r\n                \"NG\": 31.44,\r\n                \"NL\": 1.47,\r\n                \"NO\": 2.47,\r\n                \"NP\": 10.8,\r\n                \"NZ\": 9.23,\r\n                \"PE\": 1.29,\r\n                \"PK\": 159.14,\r\n                \"PL\": 8.24,\r\n                \"PT\": 16.68,\r\n                \"RO\": 63.05,\r\n                \"RS\": 473.46,\r\n                \"RU\": 14.24,\r\n                \"RW\": 5.45,\r\n                \"SE\": 2.64,\r\n                \"SG\": 8.18,\r\n                \"SI\": 3.37,\r\n                \"SK\": 112.78,\r\n                \"SN\": 3.37,\r\n                \"SO\": 8.03,\r\n                \"SS\": 19.3,\r\n                \"TD\": 75.63,\r\n                \"TG\": 34.84,\r\n                \"TH\": 81.02,\r\n                \"TL\": 9.46,\r\n                \"TN\": 7.8,\r\n                \"TR\": 7.08,\r\n                \"UA\": 1439.02,\r\n                \"UG\": 62.55,\r\n                \"US\": 1.32,\r\n                \"UZ\": 0.99,\r\n                \"VE\": 179.55,\r\n                \"ZA\": 3.09,\r\n                \"ZM\": 9.82,\r\n                \"ZW\": 0.06\r\n            }\r\n\r\n        }); // end am4core.ready()\r\n    }\r\n\r\n    var _demo4 = function () {\r\n        // Init AmChart -- for more info, please visit the official documentiation: https://www.amcharts.com/docs/v4/\r\n        am4core.ready(function () {\r\n\r\n            // Themes begin\r\n            am4core.useTheme(am4themes_frozen);\r\n            am4core.useTheme(am4themes_animated);\r\n            // Themes end\r\n\r\n            // Create map instance\r\n            chart = am4core.create(\"kt_amcharts_4\", am4maps.MapChart);\r\n            // Set map definition\r\n            chart.geodata = am4geodata_worldTimeZoneAreasHigh;\r\n            // Set projection\r\n            chart.projection = new am4maps.projections.Miller();\r\n            chart.panBehavior = \"move\";\r\n\r\n            var startColor = chart.colors.getIndex(0)\r\n            var middleColor = chart.colors.getIndex(7)\r\n            var endColor = chart.colors.getIndex(14)\r\n\r\n            var interfaceColors = new am4core.InterfaceColorSet();\r\n\r\n            // Create map polygon series\r\n            var polygonSeries = chart.series.push(new am4maps.MapPolygonSeries());\r\n            // Make map load polygon (like country names) data from GeoJSON\r\n            polygonSeries.useGeodata = true;\r\n            polygonSeries.calculateVisualCenter = true;\r\n\r\n            var polygonTemplate = polygonSeries.mapPolygons.template;\r\n            polygonTemplate.fillOpacity = 0.6;\r\n            polygonTemplate.nonScalingStroke = true;\r\n            polygonTemplate.strokeWidth = 1;\r\n            polygonTemplate.stroke = interfaceColors.getFor(\"background\");\r\n            polygonTemplate.strokeOpacity = 1;\r\n\r\n            polygonTemplate.adapter.add(\"fill\", function (fill, target) {\r\n\r\n                if (target.dataItem.index > 0) {\r\n                    return chart.colors.getIndex(target.dataItem.index);\r\n                }\r\n                return fill;\r\n            })\r\n            /// add country borders\r\n            // Create map polygon series\r\n            \r\n            var countrySeries = chart.series.push(new am4maps.MapPolygonSeries());\r\n            // Make map load polygon (like country names) data from GeoJSON\r\n            countrySeries.useGeodata = true;\r\n            countrySeries.geodata = am4geodata_worldLow;\r\n            \r\n            var countryPolygonTemplate = countrySeries.mapPolygons.template;\r\n            countryPolygonTemplate.fillOpacity = 0;\r\n            countryPolygonTemplate.nonScalingStroke = true;\r\n            countryPolygonTemplate.strokeWidth = 1;\r\n            countryPolygonTemplate.stroke = interfaceColors.getFor(\"background\");\r\n            countryPolygonTemplate.strokeOpacity = 0.3;\r\n            \r\n\r\n            // Create map polygon series\r\n            var boundsSeries = chart.series.push(new am4maps.MapPolygonSeries());\r\n            boundsSeries.geodata = am4geodata_worldTimeZonesHigh;\r\n            boundsSeries.useGeodata = true;\r\n            boundsSeries.mapPolygons.template.fill = am4core.color(interfaceColors.getFor(\"alternativeBackground\"));\r\n            boundsSeries.mapPolygons.template.fillOpacity = 0.07;\r\n            boundsSeries.mapPolygons.template.nonScalingStroke = true;\r\n            boundsSeries.mapPolygons.template.strokeWidth = 0.5;\r\n            boundsSeries.mapPolygons.template.strokeOpacity = 1;\r\n            boundsSeries.mapPolygons.template.stroke = interfaceColors.getFor(\"background\");\r\n            boundsSeries.tooltipText = \"{id}\";\r\n\r\n\r\n            var hs = polygonTemplate.states.create(\"hover\");\r\n            hs.properties.fillOpacity = 1;\r\n\r\n            var bhs = boundsSeries.mapPolygons.template.states.create(\"hover\");\r\n            bhs.properties.fillOpacity = 0.3;\r\n\r\n\r\n            polygonSeries.mapPolygons.template.events.on(\"over\", function (event) {\r\n                var polygon = boundsSeries.getPolygonById(event.target.dataItem.dataContext.id);\r\n                if (polygon) {\r\n                    polygon.isHover = true;\r\n                }\r\n            })\r\n\r\n            polygonSeries.mapPolygons.template.events.on(\"out\", function (event) {\r\n                var polygon = boundsSeries.getPolygonById(event.target.dataItem.dataContext.id);\r\n                if (polygon) {\r\n                    polygon.isHover = false;\r\n                }\r\n            })\r\n\r\n\r\n            var labelSeries = chart.series.push(new am4maps.MapImageSeries());\r\n            var label = labelSeries.mapImages.template.createChild(am4core.Label);\r\n            label.text = \"{id}\";\r\n            label.strokeOpacity = 0;\r\n            label.fill = am4core.color(\"#000000\");\r\n            label.horizontalCenter = \"middle\";\r\n            label.fontSize = 9;\r\n            label.nonScaling = true;\r\n\r\n\r\n            labelSeries.mapImages.template.adapter.add(\"longitude\", (longitude, target) => {\r\n                target.zIndex = 100000;\r\n\r\n                var polygon = polygonSeries.getPolygonById(target.dataItem.dataContext.id);\r\n                if (polygon) {\r\n                    return polygon.visualLongitude\r\n                }\r\n                return longitude;\r\n            })\r\n\r\n            labelSeries.mapImages.template.adapter.add(\"latitude\", (latitude, target) => {\r\n                var polygon = polygonSeries.getPolygonById(target.dataItem.dataContext.id);\r\n                if (polygon) {\r\n                    return polygon.visualLatitude\r\n                }\r\n                return latitude;\r\n            })\r\n\r\n\r\n            var button = chart.createChild(am4core.SwitchButton);\r\n            button.align = \"right\";\r\n            button.marginTop = 40;\r\n            button.marginRight = 40;\r\n            button.valign = \"top\";\r\n            button.leftLabel.text = \"Map\";\r\n            button.rightLabel.text = \"Globe\";\r\n\r\n            button.events.on(\"toggled\", function () {\r\n\r\n                chart.deltaLatitude = 0;\r\n                chart.deltaLongitude = 0;\r\n                chart.deltaGamma = 0;\r\n\r\n                if (button.isActive) {\r\n                    chart.projection = new am4maps.projections.Orthographic;\r\n                    chart.panBehavior = \"rotateLongLat\";\r\n                }\r\n                else {\r\n                    chart.projection = new am4maps.projections.Miller;\r\n                    chart.panBehavior = \"move\";\r\n                }\r\n            })\r\n\r\n            polygonSeries.events.on(\"datavalidated\", function () {\r\n                labelSeries.data = polygonSeries.data;\r\n            })\r\n\r\n        }); // end am4core.ready()\r\n    }\r\n\r\n    return {\r\n        // Public Functions\r\n        init: function () {\r\n            _demo1();\r\n            _demo2();\r\n            _demo3();\r\n            _demo4();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function () {\r\n    KTGeneralAmChartsMaps.init();\r\n});\r\n"], "names": [], "sourceRoot": ""}