{"version": 3, "file": "js/custom/documentation/charts/amcharts/charts.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sCAAsC,OAAO;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6CAA6C,wBAAwB;AACrE,8DAA8D,wBAAwB;AACtF,yBAAyB;AACzB;AACA;AACA;AACA;AACA,SAAS,GAAG;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,SAAS;AACrC;AACA,4BAA4B,6CAA6C;AACzE;AACA,4BAA4B,SAAS;AACrC;AACA,4BAA4B,6CAA6C;AACzE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mCAAmC,aAAa;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC,aAAa;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,GAAG;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sDAAsD;AACtD;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,GAAG;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sCAAsC;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,qDAAqD,KAAK,SAAS,UAAU,aAAa,MAAM;AAChG;AACA;AACA;AACA;AACA,oEAAoE;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,GAAG;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yEAAyE,gDAAgD;AACzH;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2CAA2C,QAAQ,GAAG,KAAK,GAAG,sCAAsC;AACpG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC,wGAAwG;AAC5I;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,GAAG;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC", "sources": ["webpack://keenthemes/../../../themes/metronic/html/demo3/src/js/custom/documentation/charts/amcharts/charts.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTGeneralAmCharts = function () {\r\n    // Shared variable\r\n    var chart;\r\n\r\n    // Private functions\r\n    var _demo1 = function () {\r\n        // Init AmChart -- for more info, please visit the official documentiation: https://www.amcharts.com/docs/v4/\r\n        am4core.ready(function () {\r\n\r\n            // Themes begin\r\n            am4core.useTheme(am4themes_animated);\r\n            // Themes end\r\n\r\n            chart = am4core.create('kt_amcharts_1', am4charts.XYChart)\r\n            chart.colors.step = 2;\r\n\r\n            chart.legend = new am4charts.Legend()\r\n            chart.legend.position = 'top'\r\n            chart.legend.paddingBottom = 20\r\n            chart.legend.labels.template.maxWidth = 95\r\n\r\n            var xAxis = chart.xAxes.push(new am4charts.CategoryAxis())\r\n            xAxis.dataFields.category = 'category'\r\n            xAxis.renderer.cellStartLocation = 0.1\r\n            xAxis.renderer.cellEndLocation = 0.9\r\n            xAxis.renderer.grid.template.location = 0;\r\n\r\n            var yAxis = chart.yAxes.push(new am4charts.ValueAxis());\r\n            yAxis.min = 0;\r\n\r\n            function createSeries(value, name) {\r\n                var series = chart.series.push(new am4charts.ColumnSeries())\r\n                series.dataFields.valueY = value\r\n                series.dataFields.categoryX = 'category'\r\n                series.name = name\r\n\r\n                series.events.on(\"hidden\", arrangeColumns);\r\n                series.events.on(\"shown\", arrangeColumns);\r\n\r\n                var bullet = series.bullets.push(new am4charts.LabelBullet())\r\n                bullet.interactionsEnabled = false\r\n                bullet.dy = 30;\r\n                bullet.label.text = '{valueY}'\r\n                bullet.label.fill = am4core.color('#ffffff')\r\n\r\n                return series;\r\n            }\r\n\r\n            chart.data = [\r\n                {\r\n                    category: 'Place #1',\r\n                    first: 40,\r\n                    second: 55,\r\n                    third: 60\r\n                },\r\n                {\r\n                    category: 'Place #2',\r\n                    first: 30,\r\n                    second: 78,\r\n                    third: 69\r\n                },\r\n                {\r\n                    category: 'Place #3',\r\n                    first: 27,\r\n                    second: 40,\r\n                    third: 45\r\n                },\r\n                {\r\n                    category: 'Place #4',\r\n                    first: 50,\r\n                    second: 33,\r\n                    third: 22\r\n                }\r\n            ]\r\n\r\n\r\n            createSeries('first', 'The First');\r\n            createSeries('second', 'The Second');\r\n            createSeries('third', 'The Third');\r\n\r\n            function arrangeColumns() {\r\n\r\n                var series = chart.series.getIndex(0);\r\n\r\n                var w = 1 - xAxis.renderer.cellStartLocation - (1 - xAxis.renderer.cellEndLocation);\r\n                if (series.dataItems.length > 1) {\r\n                    var x0 = xAxis.getX(series.dataItems.getIndex(0), \"categoryX\");\r\n                    var x1 = xAxis.getX(series.dataItems.getIndex(1), \"categoryX\");\r\n                    var delta = ((x1 - x0) / chart.series.length) * w;\r\n                    if (am4core.isNumber(delta)) {\r\n                        var middle = chart.series.length / 2;\r\n\r\n                        var newIndex = 0;\r\n                        chart.series.each(function (series) {\r\n                            if (!series.isHidden && !series.isHiding) {\r\n                                series.dummyData = newIndex;\r\n                                newIndex++;\r\n                            }\r\n                            else {\r\n                                series.dummyData = chart.series.indexOf(series);\r\n                            }\r\n                        })\r\n                        var visibleCount = newIndex;\r\n                        var newMiddle = visibleCount / 2;\r\n\r\n                        chart.series.each(function (series) {\r\n                            var trueIndex = chart.series.indexOf(series);\r\n                            var newIndex = series.dummyData;\r\n\r\n                            var dx = (newIndex - trueIndex + middle - newMiddle) * delta\r\n\r\n                            series.animate({ property: \"dx\", to: dx }, series.interpolationDuration, series.interpolationEasing);\r\n                            series.bulletsContainer.animate({ property: \"dx\", to: dx }, series.interpolationDuration, series.interpolationEasing);\r\n                        })\r\n                    }\r\n                }\r\n            }\r\n\r\n        }); // end am4core.ready()\r\n    }\r\n\r\n    var _demo2 = function () {\r\n        // Init AmChart -- for more info, please visit the official documentiation: https://www.amcharts.com/docs/v4/\r\n        am4core.ready(function () {\r\n\r\n            // Themes begin\r\n            am4core.useTheme(am4themes_animated);\r\n            // Themes end\r\n\r\n            // Create chart\r\n            chart = am4core.create(\"kt_amcharts_2\", am4charts.XYChart);\r\n\r\n            var data = [];\r\n            var price1 = 1000, price2 = 1200;\r\n            var quantity = 30000;\r\n            for (var i = 0; i < 360; i++) {\r\n                price1 += Math.round((Math.random() < 0.5 ? 1 : -1) * Math.random() * 100);\r\n                data.push({ date1: new Date(2015, 0, i), price1: price1 });\r\n            }\r\n            for (var i = 0; i < 360; i++) {\r\n                price2 += Math.round((Math.random() < 0.5 ? 1 : -1) * Math.random() * 100);\r\n                data.push({ date2: new Date(2017, 0, i), price2: price2 });\r\n            }\r\n\r\n            chart.data = data;\r\n\r\n            var dateAxis = chart.xAxes.push(new am4charts.DateAxis());\r\n            dateAxis.renderer.grid.template.location = 0;\r\n            dateAxis.renderer.labels.template.fill = am4core.color(\"#e59165\");\r\n\r\n            var dateAxis2 = chart.xAxes.push(new am4charts.DateAxis());\r\n            dateAxis2.renderer.grid.template.location = 0;\r\n            dateAxis2.renderer.labels.template.fill = am4core.color(\"#dfcc64\");\r\n\r\n            var valueAxis = chart.yAxes.push(new am4charts.ValueAxis());\r\n            valueAxis.tooltip.disabled = true;\r\n            valueAxis.renderer.labels.template.fill = am4core.color(\"#e59165\");\r\n\r\n            valueAxis.renderer.minWidth = 60;\r\n\r\n            var valueAxis2 = chart.yAxes.push(new am4charts.ValueAxis());\r\n            valueAxis2.tooltip.disabled = true;\r\n            valueAxis2.renderer.labels.template.fill = am4core.color(\"#dfcc64\");\r\n            valueAxis2.renderer.minWidth = 60;\r\n            valueAxis2.syncWithAxis = valueAxis;\r\n\r\n            var series = chart.series.push(new am4charts.LineSeries());\r\n            series.name = \"2015\";\r\n            series.dataFields.dateX = \"date1\";\r\n            series.dataFields.valueY = \"price1\";\r\n            series.tooltipText = \"{valueY.value}\";\r\n            series.fill = am4core.color(\"#e59165\");\r\n            series.stroke = am4core.color(\"#e59165\");\r\n            //series.strokeWidth = 3;\r\n\r\n            var series2 = chart.series.push(new am4charts.LineSeries());\r\n            series2.name = \"2017\";\r\n            series2.dataFields.dateX = \"date2\";\r\n            series2.dataFields.valueY = \"price2\";\r\n            series2.yAxis = valueAxis2;\r\n            series2.xAxis = dateAxis2;\r\n            series2.tooltipText = \"{valueY.value}\";\r\n            series2.fill = am4core.color(\"#dfcc64\");\r\n            series2.stroke = am4core.color(\"#dfcc64\");\r\n            //series2.strokeWidth = 3;\r\n\r\n            chart.cursor = new am4charts.XYCursor();\r\n            chart.cursor.xAxis = dateAxis2;\r\n\r\n            var scrollbarX = new am4charts.XYChartScrollbar();\r\n            scrollbarX.series.push(series);\r\n            chart.scrollbarX = scrollbarX;\r\n\r\n            chart.legend = new am4charts.Legend();\r\n            chart.legend.parent = chart.plotContainer;\r\n            chart.legend.zIndex = 100;\r\n\r\n            valueAxis2.renderer.grid.template.strokeOpacity = 0.07;\r\n            dateAxis2.renderer.grid.template.strokeOpacity = 0.07;\r\n            dateAxis.renderer.grid.template.strokeOpacity = 0.07;\r\n            valueAxis.renderer.grid.template.strokeOpacity = 0.07;\r\n\r\n        }); // end am4core.ready()\r\n    }\r\n\r\n    var _demo3 = function () {\r\n        // Init AmChart -- for more info, please visit the official documentiation: https://www.amcharts.com/docs/v4/\r\n        am4core.ready(function () {\r\n\r\n            // Themes begin\r\n            am4core.useTheme(am4themes_dataviz);\r\n            am4core.useTheme(am4themes_animated);\r\n            // Themes end\r\n\r\n            // Create chart\r\n            chart = am4core.create(\"kt_amcharts_3\", am4charts.PieChart);\r\n            chart.hiddenState.properties.opacity = 0; // this creates initial fade-in\r\n\r\n            chart.data = [\r\n                {\r\n                    country: \"Lithuania\",\r\n                    value: 260\r\n                },\r\n                {\r\n                    country: \"Czechia\",\r\n                    value: 230\r\n                },\r\n                {\r\n                    country: \"Ireland\",\r\n                    value: 200\r\n                },\r\n                {\r\n                    country: \"Germany\",\r\n                    value: 165\r\n                },\r\n                {\r\n                    country: \"Australia\",\r\n                    value: 139\r\n                },\r\n                {\r\n                    country: \"Austria\",\r\n                    value: 128\r\n                }\r\n            ];\r\n\r\n            var series = chart.series.push(new am4charts.PieSeries());\r\n            series.dataFields.value = \"value\";\r\n            series.dataFields.radiusValue = \"value\";\r\n            series.dataFields.category = \"country\";\r\n            series.slices.template.cornerRadius = 6;\r\n            series.colors.step = 3;\r\n\r\n            series.hiddenState.properties.endAngle = -90;\r\n\r\n            chart.legend = new am4charts.Legend();\r\n\r\n        }); // end am4core.ready()\r\n    }\r\n\r\n    var _demo4 = function () {\r\n        // Init AmChart -- for more info, please visit the official documentiation: https://www.amcharts.com/docs/v4/\r\n        am4core.ready(function () {\r\n\r\n            // Themes begin\r\n            am4core.useTheme(am4themes_frozen);\r\n            am4core.useTheme(am4themes_animated);\r\n            // Themes end\r\n\r\n            chart = am4core.create(\"kt_amcharts_4\", am4plugins_timeline.SerpentineChart);\r\n            chart.curveContainer.padding(20, 20, 20, 20);\r\n            chart.levelCount = 8;\r\n            chart.orientation = \"horizontal\";\r\n            chart.fontSize = 11;\r\n\r\n            var colorSet = new am4core.ColorSet();\r\n            colorSet.saturation = 0.6;\r\n\r\n            chart.data = [{\r\n                \"category\": \"Module #1\",\r\n                \"start\": \"2016-01-10\",\r\n                \"end\": \"2016-01-13\",\r\n                \"color\": colorSet.getIndex(0),\r\n                \"task\": \"Gathering requirements\"\r\n            }, {\r\n                \"category\": \"Module #1\",\r\n                \"start\": \"2016-02-05\",\r\n                \"end\": \"2016-04-18\",\r\n                \"color\": colorSet.getIndex(0),\r\n                \"task\": \"Development\"\r\n            }, {\r\n                \"category\": \"Module #2\",\r\n                \"start\": \"2016-01-08\",\r\n                \"end\": \"2016-01-10\",\r\n                \"color\": colorSet.getIndex(5),\r\n                \"task\": \"Gathering requirements\"\r\n            }, {\r\n                \"category\": \"Module #2\",\r\n                \"start\": \"2016-01-12\",\r\n                \"end\": \"2016-01-15\",\r\n                \"color\": colorSet.getIndex(5),\r\n                \"task\": \"Producing specifications\"\r\n            }, {\r\n                \"category\": \"Module #2\",\r\n                \"start\": \"2016-01-16\",\r\n                \"end\": \"2016-02-05\",\r\n                \"color\": colorSet.getIndex(5),\r\n                \"task\": \"Development\"\r\n            }, {\r\n                \"category\": \"Module #2\",\r\n                \"start\": \"2016-02-10\",\r\n                \"end\": \"2016-02-18\",\r\n                \"color\": colorSet.getIndex(5),\r\n                \"task\": \"Testing and QA\"\r\n            }, {\r\n                \"category\": \"\",\r\n                \"task\": \"\"\r\n            }, {\r\n                \"category\": \"Module #3\",\r\n                \"start\": \"2016-01-01\",\r\n                \"end\": \"2016-01-19\",\r\n                \"color\": colorSet.getIndex(9),\r\n                \"task\": \"Gathering requirements\"\r\n            }, {\r\n                \"category\": \"Module #3\",\r\n                \"start\": \"2016-02-01\",\r\n                \"end\": \"2016-02-10\",\r\n                \"color\": colorSet.getIndex(9),\r\n                \"task\": \"Producing specifications\"\r\n            }, {\r\n                \"category\": \"Module #3\",\r\n                \"start\": \"2016-03-10\",\r\n                \"end\": \"2016-04-15\",\r\n                \"color\": colorSet.getIndex(9),\r\n                \"task\": \"Development\"\r\n            }, {\r\n                \"category\": \"Module #3\",\r\n                \"start\": \"2016-04-20\",\r\n                \"end\": \"2016-04-30\",\r\n                \"color\": colorSet.getIndex(9),\r\n                \"task\": \"Testing and QA\"\r\n            }, {\r\n                \"category\": \"Module #4\",\r\n                \"start\": \"2016-01-15\",\r\n                \"end\": \"2016-02-12\",\r\n                \"color\": colorSet.getIndex(15),\r\n                \"task\": \"Gathering requirements\"\r\n            }, {\r\n                \"category\": \"Module #4\",\r\n                \"start\": \"2016-02-25\",\r\n                \"end\": \"2016-03-10\",\r\n                \"color\": colorSet.getIndex(15),\r\n                \"task\": \"Development\"\r\n            }, {\r\n                \"category\": \"Module #4\",\r\n                \"start\": \"2016-03-23\",\r\n                \"end\": \"2016-04-29\",\r\n                \"color\": colorSet.getIndex(15),\r\n                \"task\": \"Testing and QA\"\r\n            }];\r\n\r\n            chart.dateFormatter.dateFormat = \"yyyy-MM-dd\";\r\n            chart.dateFormatter.inputDateFormat = \"yyyy-MM-dd\";\r\n\r\n            var categoryAxis = chart.yAxes.push(new am4charts.CategoryAxis());\r\n            categoryAxis.dataFields.category = \"category\";\r\n            categoryAxis.renderer.grid.template.disabled = true;\r\n            categoryAxis.renderer.labels.template.paddingRight = 25;\r\n            categoryAxis.renderer.minGridDistance = 10;\r\n            categoryAxis.renderer.innerRadius = -60;\r\n            categoryAxis.renderer.radius = 60;\r\n\r\n            var dateAxis = chart.xAxes.push(new am4charts.DateAxis());\r\n            dateAxis.renderer.minGridDistance = 70;\r\n            dateAxis.baseInterval = { count: 1, timeUnit: \"day\" };\r\n\r\n            dateAxis.renderer.tooltipLocation = 0;\r\n            dateAxis.startLocation = -0.5;\r\n            dateAxis.renderer.line.strokeDasharray = \"1,4\";\r\n            dateAxis.renderer.line.strokeOpacity = 0.7;\r\n            dateAxis.tooltip.background.fillOpacity = 0.2;\r\n            dateAxis.tooltip.background.cornerRadius = 5;\r\n            dateAxis.tooltip.label.fill = new am4core.InterfaceColorSet().getFor(\"alternativeBackground\");\r\n            dateAxis.tooltip.label.paddingTop = 7;\r\n\r\n            var labelTemplate = dateAxis.renderer.labels.template;\r\n            labelTemplate.verticalCenter = \"middle\";\r\n            labelTemplate.fillOpacity = 0.7;\r\n            labelTemplate.background.fill = new am4core.InterfaceColorSet().getFor(\"background\");\r\n            labelTemplate.background.fillOpacity = 1;\r\n            labelTemplate.padding(7, 7, 7, 7);\r\n\r\n            var categoryAxisLabelTemplate = categoryAxis.renderer.labels.template;\r\n            categoryAxisLabelTemplate.horizontalCenter = \"left\";\r\n            categoryAxisLabelTemplate.adapter.add(\"rotation\", function (rotation, target) {\r\n                var position = dateAxis.valueToPosition(dateAxis.min);\r\n                return dateAxis.renderer.positionToAngle(position) + 90;\r\n            })\r\n\r\n            var series1 = chart.series.push(new am4plugins_timeline.CurveColumnSeries());\r\n            series1.columns.template.height = am4core.percent(20);\r\n            series1.columns.template.tooltipText = \"{task}: [bold]{openDateX}[/] - [bold]{dateX}[/]\";\r\n\r\n            series1.dataFields.openDateX = \"start\";\r\n            series1.dataFields.dateX = \"end\";\r\n            series1.dataFields.categoryY = \"category\";\r\n            series1.columns.template.propertyFields.fill = \"color\"; // get color from data\r\n            series1.columns.template.propertyFields.stroke = \"color\";\r\n            series1.columns.template.strokeOpacity = 0;\r\n\r\n            var bullet = new am4charts.CircleBullet();\r\n            series1.bullets.push(bullet);\r\n            bullet.circle.radius = 3;\r\n            bullet.circle.strokeOpacity = 0;\r\n            bullet.propertyFields.fill = \"color\";\r\n            bullet.locationX = 0;\r\n\r\n\r\n            var bullet2 = new am4charts.CircleBullet();\r\n            series1.bullets.push(bullet2);\r\n            bullet2.circle.radius = 3;\r\n            bullet2.circle.strokeOpacity = 0;\r\n            bullet2.propertyFields.fill = \"color\";\r\n            bullet2.locationX = 1;\r\n\r\n            chart.scrollbarX = new am4core.Scrollbar();\r\n            chart.scrollbarX.align = \"center\"\r\n            chart.scrollbarX.width = am4core.percent(90);\r\n\r\n            var cursor = new am4plugins_timeline.CurveCursor();\r\n            chart.cursor = cursor;\r\n            cursor.xAxis = dateAxis;\r\n            cursor.yAxis = categoryAxis;\r\n            cursor.lineY.disabled = true;\r\n            cursor.lineX.strokeDasharray = \"1,4\";\r\n            cursor.lineX.strokeOpacity = 1;\r\n\r\n            dateAxis.renderer.tooltipLocation2 = 0;\r\n            categoryAxis.cursorTooltipEnabled = false;\r\n\r\n        }); // end am4core.ready()\r\n    }\r\n\r\n    var _demo5 = function () {\r\n        // Init AmChart -- for more info, please visit the official documentiation: https://www.amcharts.com/docs/v4/\r\n        am4core.ready(function () {\r\n\r\n            // Themes begin\r\n            am4core.useTheme(am4themes_animated);\r\n            // Themes end\r\n\r\n            chart = am4core.create(\"kt_amcharts_5\", am4charts.RadarChart);\r\n            chart.innerRadius = am4core.percent(30);\r\n            chart.fontSize = 11;\r\n\r\n            var xAxis = chart.xAxes.push(new am4charts.CategoryAxis());\r\n            var yAxis = chart.yAxes.push(new am4charts.CategoryAxis());\r\n            yAxis.renderer.minGridDistance = 5;\r\n\r\n            xAxis.renderer.labels.template.location = 0.5;\r\n            xAxis.renderer.labels.template.bent = true;\r\n            xAxis.renderer.labels.template.radius = 5;\r\n\r\n            xAxis.dataFields.category = \"hour\";\r\n            yAxis.dataFields.category = \"weekday\";\r\n\r\n            xAxis.renderer.grid.template.disabled = true;\r\n            xAxis.renderer.minGridDistance = 10;\r\n\r\n            yAxis.renderer.grid.template.disabled = true;\r\n            yAxis.renderer.inversed = true;\r\n\r\n            // this makes the y axis labels to be bent. By default y Axis labels are regular AxisLabels, so we replace them with AxisLabelCircular\r\n            // and call fixPosition for them to be bent\r\n            var yAxisLabel = new am4charts.AxisLabelCircular();\r\n            yAxisLabel.bent = true;\r\n            yAxisLabel.events.on(\"validated\", function (event) {\r\n                event.target.fixPosition(-90, am4core.math.getDistance({ x: event.target.pixelX, y: event.target.pixelY }) - 5);\r\n                event.target.dx = -event.target.pixelX;\r\n                event.target.dy = -event.target.pixelY;\r\n            })\r\n            yAxis.renderer.labels.template = yAxisLabel;\r\n\r\n            var series = chart.series.push(new am4charts.RadarColumnSeries());\r\n            series.dataFields.categoryX = \"hour\";\r\n            series.dataFields.categoryY = \"weekday\";\r\n            series.dataFields.value = \"value\";\r\n            series.sequencedInterpolation = true;\r\n\r\n            var columnTemplate = series.columns.template;\r\n            columnTemplate.strokeWidth = 2;\r\n            columnTemplate.strokeOpacity = 1;\r\n            columnTemplate.stroke = am4core.color(\"#ffffff\");\r\n            columnTemplate.tooltipText = \"{weekday}, {hour}: {value.workingValue.formatNumber('#.')}\";\r\n            columnTemplate.width = am4core.percent(100);\r\n            columnTemplate.height = am4core.percent(100);\r\n\r\n            chart.seriesContainer.zIndex = -5;\r\n\r\n            columnTemplate.hiddenState.properties.opacity = 0;\r\n\r\n            // heat rule, this makes columns to change color depending on value\r\n            series.heatRules.push({ target: columnTemplate, property: \"fill\", min: am4core.color(\"#fffb77\"), max: am4core.color(\"#fe131a\") });\r\n\r\n            // heat legend\r\n\r\n            var heatLegend = chart.bottomAxesContainer.createChild(am4charts.HeatLegend);\r\n            heatLegend.width = am4core.percent(100);\r\n            heatLegend.series = series;\r\n            heatLegend.valueAxis.renderer.labels.template.fontSize = 9;\r\n            heatLegend.valueAxis.renderer.minGridDistance = 30;\r\n\r\n            // heat legend behavior\r\n            series.columns.template.events.on(\"over\", function (event) {\r\n                handleHover(event.target);\r\n            })\r\n\r\n            series.columns.template.events.on(\"hit\", function (event) {\r\n                handleHover(event.target);\r\n            })\r\n\r\n            function handleHover(column) {\r\n                if (!isNaN(column.dataItem.value)) {\r\n                    heatLegend.valueAxis.showTooltipAt(column.dataItem.value)\r\n                }\r\n                else {\r\n                    heatLegend.valueAxis.hideTooltip();\r\n                }\r\n            }\r\n\r\n            series.columns.template.events.on(\"out\", function (event) {\r\n                heatLegend.valueAxis.hideTooltip();\r\n            })\r\n\r\n            chart.data = [\r\n                {\r\n                    \"hour\": \"12pm\",\r\n                    \"weekday\": \"Sunday\",\r\n                    \"value\": 2990\r\n                },\r\n                {\r\n                    \"hour\": \"1am\",\r\n                    \"weekday\": \"Sunday\",\r\n                    \"value\": 2520\r\n                },\r\n                {\r\n                    \"hour\": \"2am\",\r\n                    \"weekday\": \"Sunday\",\r\n                    \"value\": 2334\r\n                },\r\n                {\r\n                    \"hour\": \"3am\",\r\n                    \"weekday\": \"Sunday\",\r\n                    \"value\": 2230\r\n                },\r\n                {\r\n                    \"hour\": \"4am\",\r\n                    \"weekday\": \"Sunday\",\r\n                    \"value\": 2325\r\n                },\r\n                {\r\n                    \"hour\": \"5am\",\r\n                    \"weekday\": \"Sunday\",\r\n                    \"value\": 2019\r\n                },\r\n                {\r\n                    \"hour\": \"6am\",\r\n                    \"weekday\": \"Sunday\",\r\n                    \"value\": 2128\r\n                },\r\n                {\r\n                    \"hour\": \"7am\",\r\n                    \"weekday\": \"Sunday\",\r\n                    \"value\": 2246\r\n                },\r\n                {\r\n                    \"hour\": \"8am\",\r\n                    \"weekday\": \"Sunday\",\r\n                    \"value\": 2421\r\n                },\r\n                {\r\n                    \"hour\": \"9am\",\r\n                    \"weekday\": \"Sunday\",\r\n                    \"value\": 2788\r\n                },\r\n                {\r\n                    \"hour\": \"10am\",\r\n                    \"weekday\": \"Sunday\",\r\n                    \"value\": 2959\r\n                },\r\n                {\r\n                    \"hour\": \"11am\",\r\n                    \"weekday\": \"Sunday\",\r\n                    \"value\": 3018\r\n                },\r\n                {\r\n                    \"hour\": \"12am\",\r\n                    \"weekday\": \"Sunday\",\r\n                    \"value\": 3154\r\n                },\r\n                {\r\n                    \"hour\": \"1pm\",\r\n                    \"weekday\": \"Sunday\",\r\n                    \"value\": 3172\r\n                },\r\n                {\r\n                    \"hour\": \"2pm\",\r\n                    \"weekday\": \"Sunday\",\r\n                    \"value\": 3368\r\n                },\r\n                {\r\n                    \"hour\": \"3pm\",\r\n                    \"weekday\": \"Sunday\",\r\n                    \"value\": 3464\r\n                },\r\n                {\r\n                    \"hour\": \"4pm\",\r\n                    \"weekday\": \"Sunday\",\r\n                    \"value\": 3746\r\n                },\r\n                {\r\n                    \"hour\": \"5pm\",\r\n                    \"weekday\": \"Sunday\",\r\n                    \"value\": 3656\r\n                },\r\n                {\r\n                    \"hour\": \"6pm\",\r\n                    \"weekday\": \"Sunday\",\r\n                    \"value\": 3336\r\n                },\r\n                {\r\n                    \"hour\": \"7pm\",\r\n                    \"weekday\": \"Sunday\",\r\n                    \"value\": 3292\r\n                },\r\n                {\r\n                    \"hour\": \"8pm\",\r\n                    \"weekday\": \"Sunday\",\r\n                    \"value\": 3269\r\n                },\r\n                {\r\n                    \"hour\": \"9pm\",\r\n                    \"weekday\": \"Sunday\",\r\n                    \"value\": 3300\r\n                },\r\n                {\r\n                    \"hour\": \"10pm\",\r\n                    \"weekday\": \"Sunday\",\r\n                    \"value\": 3403\r\n                },\r\n                {\r\n                    \"hour\": \"11pm\",\r\n                    \"weekday\": \"Sunday\",\r\n                    \"value\": 3323\r\n                },\r\n                {\r\n                    \"hour\": \"12pm\",\r\n                    \"weekday\": \"Monday\",\r\n                    \"value\": 3346\r\n                },\r\n                {\r\n                    \"hour\": \"1am\",\r\n                    \"weekday\": \"Monday\",\r\n                    \"value\": 2725\r\n                },\r\n                {\r\n                    \"hour\": \"2am\",\r\n                    \"weekday\": \"Monday\",\r\n                    \"value\": 3052\r\n                },\r\n                {\r\n                    \"hour\": \"3am\",\r\n                    \"weekday\": \"Monday\",\r\n                    \"value\": 3876\r\n                },\r\n                {\r\n                    \"hour\": \"4am\",\r\n                    \"weekday\": \"Monday\",\r\n                    \"value\": 4453\r\n                },\r\n                {\r\n                    \"hour\": \"5am\",\r\n                    \"weekday\": \"Monday\",\r\n                    \"value\": 3972\r\n                },\r\n                {\r\n                    \"hour\": \"6am\",\r\n                    \"weekday\": \"Monday\",\r\n                    \"value\": 4644\r\n                },\r\n                {\r\n                    \"hour\": \"7am\",\r\n                    \"weekday\": \"Monday\",\r\n                    \"value\": 5715\r\n                },\r\n                {\r\n                    \"hour\": \"8am\",\r\n                    \"weekday\": \"Monday\",\r\n                    \"value\": 7080\r\n                },\r\n                {\r\n                    \"hour\": \"9am\",\r\n                    \"weekday\": \"Monday\",\r\n                    \"value\": 8022\r\n                },\r\n                {\r\n                    \"hour\": \"10am\",\r\n                    \"weekday\": \"Monday\",\r\n                    \"value\": 8446\r\n                },\r\n                {\r\n                    \"hour\": \"11am\",\r\n                    \"weekday\": \"Monday\",\r\n                    \"value\": 9313\r\n                },\r\n                {\r\n                    \"hour\": \"12am\",\r\n                    \"weekday\": \"Monday\",\r\n                    \"value\": 9011\r\n                },\r\n                {\r\n                    \"hour\": \"1pm\",\r\n                    \"weekday\": \"Monday\",\r\n                    \"value\": 8508\r\n                },\r\n                {\r\n                    \"hour\": \"2pm\",\r\n                    \"weekday\": \"Monday\",\r\n                    \"value\": 8515\r\n                },\r\n                {\r\n                    \"hour\": \"3pm\",\r\n                    \"weekday\": \"Monday\",\r\n                    \"value\": 8399\r\n                },\r\n                {\r\n                    \"hour\": \"4pm\",\r\n                    \"weekday\": \"Monday\",\r\n                    \"value\": 8649\r\n                },\r\n                {\r\n                    \"hour\": \"5pm\",\r\n                    \"weekday\": \"Monday\",\r\n                    \"value\": 7869\r\n                },\r\n                {\r\n                    \"hour\": \"6pm\",\r\n                    \"weekday\": \"Monday\",\r\n                    \"value\": 6933\r\n                },\r\n                {\r\n                    \"hour\": \"7pm\",\r\n                    \"weekday\": \"Monday\",\r\n                    \"value\": 5969\r\n                },\r\n                {\r\n                    \"hour\": \"8pm\",\r\n                    \"weekday\": \"Monday\",\r\n                    \"value\": 5552\r\n                },\r\n                {\r\n                    \"hour\": \"9pm\",\r\n                    \"weekday\": \"Monday\",\r\n                    \"value\": 5434\r\n                },\r\n                {\r\n                    \"hour\": \"10pm\",\r\n                    \"weekday\": \"Monday\",\r\n                    \"value\": 5070\r\n                },\r\n                {\r\n                    \"hour\": \"11pm\",\r\n                    \"weekday\": \"Monday\",\r\n                    \"value\": 4851\r\n                },\r\n                {\r\n                    \"hour\": \"12pm\",\r\n                    \"weekday\": \"Tuesday\",\r\n                    \"value\": 4468\r\n                },\r\n                {\r\n                    \"hour\": \"1am\",\r\n                    \"weekday\": \"Tuesday\",\r\n                    \"value\": 3306\r\n                },\r\n                {\r\n                    \"hour\": \"2am\",\r\n                    \"weekday\": \"Tuesday\",\r\n                    \"value\": 3906\r\n                },\r\n                {\r\n                    \"hour\": \"3am\",\r\n                    \"weekday\": \"Tuesday\",\r\n                    \"value\": 4413\r\n                },\r\n                {\r\n                    \"hour\": \"4am\",\r\n                    \"weekday\": \"Tuesday\",\r\n                    \"value\": 4726\r\n                },\r\n                {\r\n                    \"hour\": \"5am\",\r\n                    \"weekday\": \"Tuesday\",\r\n                    \"value\": 4584\r\n                },\r\n                {\r\n                    \"hour\": \"6am\",\r\n                    \"weekday\": \"Tuesday\",\r\n                    \"value\": 5717\r\n                },\r\n                {\r\n                    \"hour\": \"7am\",\r\n                    \"weekday\": \"Tuesday\",\r\n                    \"value\": 6504\r\n                },\r\n                {\r\n                    \"hour\": \"8am\",\r\n                    \"weekday\": \"Tuesday\",\r\n                    \"value\": 8104\r\n                },\r\n                {\r\n                    \"hour\": \"9am\",\r\n                    \"weekday\": \"Tuesday\",\r\n                    \"value\": 8813\r\n                },\r\n                {\r\n                    \"hour\": \"10am\",\r\n                    \"weekday\": \"Tuesday\",\r\n                    \"value\": 9278\r\n                },\r\n                {\r\n                    \"hour\": \"11am\",\r\n                    \"weekday\": \"Tuesday\",\r\n                    \"value\": 10425\r\n                },\r\n                {\r\n                    \"hour\": \"12am\",\r\n                    \"weekday\": \"Tuesday\",\r\n                    \"value\": 10137\r\n                },\r\n                {\r\n                    \"hour\": \"1pm\",\r\n                    \"weekday\": \"Tuesday\",\r\n                    \"value\": 9290\r\n                },\r\n                {\r\n                    \"hour\": \"2pm\",\r\n                    \"weekday\": \"Tuesday\",\r\n                    \"value\": 9255\r\n                },\r\n                {\r\n                    \"hour\": \"3pm\",\r\n                    \"weekday\": \"Tuesday\",\r\n                    \"value\": 9614\r\n                },\r\n                {\r\n                    \"hour\": \"4pm\",\r\n                    \"weekday\": \"Tuesday\",\r\n                    \"value\": 9713\r\n                },\r\n                {\r\n                    \"hour\": \"5pm\",\r\n                    \"weekday\": \"Tuesday\",\r\n                    \"value\": 9667\r\n                },\r\n                {\r\n                    \"hour\": \"6pm\",\r\n                    \"weekday\": \"Tuesday\",\r\n                    \"value\": 8774\r\n                },\r\n                {\r\n                    \"hour\": \"7pm\",\r\n                    \"weekday\": \"Tuesday\",\r\n                    \"value\": 8649\r\n                },\r\n                {\r\n                    \"hour\": \"8pm\",\r\n                    \"weekday\": \"Tuesday\",\r\n                    \"value\": 9937\r\n                },\r\n                {\r\n                    \"hour\": \"9pm\",\r\n                    \"weekday\": \"Tuesday\",\r\n                    \"value\": 10286\r\n                },\r\n                {\r\n                    \"hour\": \"10pm\",\r\n                    \"weekday\": \"Tuesday\",\r\n                    \"value\": 9175\r\n                },\r\n                {\r\n                    \"hour\": \"11pm\",\r\n                    \"weekday\": \"Tuesday\",\r\n                    \"value\": 8581\r\n                },\r\n                {\r\n                    \"hour\": \"12pm\",\r\n                    \"weekday\": \"Wednesday\",\r\n                    \"value\": 8145\r\n                },\r\n                {\r\n                    \"hour\": \"1am\",\r\n                    \"weekday\": \"Wednesday\",\r\n                    \"value\": 7177\r\n                },\r\n                {\r\n                    \"hour\": \"2am\",\r\n                    \"weekday\": \"Wednesday\",\r\n                    \"value\": 5657\r\n                },\r\n                {\r\n                    \"hour\": \"3am\",\r\n                    \"weekday\": \"Wednesday\",\r\n                    \"value\": 6802\r\n                },\r\n                {\r\n                    \"hour\": \"4am\",\r\n                    \"weekday\": \"Wednesday\",\r\n                    \"value\": 8159\r\n                },\r\n                {\r\n                    \"hour\": \"5am\",\r\n                    \"weekday\": \"Wednesday\",\r\n                    \"value\": 8449\r\n                },\r\n                {\r\n                    \"hour\": \"6am\",\r\n                    \"weekday\": \"Wednesday\",\r\n                    \"value\": 9453\r\n                },\r\n                {\r\n                    \"hour\": \"7am\",\r\n                    \"weekday\": \"Wednesday\",\r\n                    \"value\": 9947\r\n                },\r\n                {\r\n                    \"hour\": \"8am\",\r\n                    \"weekday\": \"Wednesday\",\r\n                    \"value\": 11471\r\n                },\r\n                {\r\n                    \"hour\": \"9am\",\r\n                    \"weekday\": \"Wednesday\",\r\n                    \"value\": 12492\r\n                },\r\n                {\r\n                    \"hour\": \"10am\",\r\n                    \"weekday\": \"Wednesday\",\r\n                    \"value\": 9388\r\n                },\r\n                {\r\n                    \"hour\": \"11am\",\r\n                    \"weekday\": \"Wednesday\",\r\n                    \"value\": 9928\r\n                },\r\n                {\r\n                    \"hour\": \"12am\",\r\n                    \"weekday\": \"Wednesday\",\r\n                    \"value\": 9644\r\n                },\r\n                {\r\n                    \"hour\": \"1pm\",\r\n                    \"weekday\": \"Wednesday\",\r\n                    \"value\": 9034\r\n                },\r\n                {\r\n                    \"hour\": \"2pm\",\r\n                    \"weekday\": \"Wednesday\",\r\n                    \"value\": 8964\r\n                },\r\n                {\r\n                    \"hour\": \"3pm\",\r\n                    \"weekday\": \"Wednesday\",\r\n                    \"value\": 9069\r\n                },\r\n                {\r\n                    \"hour\": \"4pm\",\r\n                    \"weekday\": \"Wednesday\",\r\n                    \"value\": 8898\r\n                },\r\n                {\r\n                    \"hour\": \"5pm\",\r\n                    \"weekday\": \"Wednesday\",\r\n                    \"value\": 8322\r\n                },\r\n                {\r\n                    \"hour\": \"6pm\",\r\n                    \"weekday\": \"Wednesday\",\r\n                    \"value\": 6909\r\n                },\r\n                {\r\n                    \"hour\": \"7pm\",\r\n                    \"weekday\": \"Wednesday\",\r\n                    \"value\": 5810\r\n                },\r\n                {\r\n                    \"hour\": \"8pm\",\r\n                    \"weekday\": \"Wednesday\",\r\n                    \"value\": 5151\r\n                },\r\n                {\r\n                    \"hour\": \"9pm\",\r\n                    \"weekday\": \"Wednesday\",\r\n                    \"value\": 4911\r\n                },\r\n                {\r\n                    \"hour\": \"10pm\",\r\n                    \"weekday\": \"Wednesday\",\r\n                    \"value\": 4487\r\n                },\r\n                {\r\n                    \"hour\": \"11pm\",\r\n                    \"weekday\": \"Wednesday\",\r\n                    \"value\": 4118\r\n                },\r\n                {\r\n                    \"hour\": \"12pm\",\r\n                    \"weekday\": \"Thursday\",\r\n                    \"value\": 3689\r\n                },\r\n                {\r\n                    \"hour\": \"1am\",\r\n                    \"weekday\": \"Thursday\",\r\n                    \"value\": 3081\r\n                },\r\n                {\r\n                    \"hour\": \"2am\",\r\n                    \"weekday\": \"Thursday\",\r\n                    \"value\": 6525\r\n                },\r\n                {\r\n                    \"hour\": \"3am\",\r\n                    \"weekday\": \"Thursday\",\r\n                    \"value\": 6228\r\n                },\r\n                {\r\n                    \"hour\": \"4am\",\r\n                    \"weekday\": \"Thursday\",\r\n                    \"value\": 6917\r\n                },\r\n                {\r\n                    \"hour\": \"5am\",\r\n                    \"weekday\": \"Thursday\",\r\n                    \"value\": 6568\r\n                },\r\n                {\r\n                    \"hour\": \"6am\",\r\n                    \"weekday\": \"Thursday\",\r\n                    \"value\": 6405\r\n                },\r\n                {\r\n                    \"hour\": \"7am\",\r\n                    \"weekday\": \"Thursday\",\r\n                    \"value\": 8106\r\n                },\r\n                {\r\n                    \"hour\": \"8am\",\r\n                    \"weekday\": \"Thursday\",\r\n                    \"value\": 8542\r\n                },\r\n                {\r\n                    \"hour\": \"9am\",\r\n                    \"weekday\": \"Thursday\",\r\n                    \"value\": 8501\r\n                },\r\n                {\r\n                    \"hour\": \"10am\",\r\n                    \"weekday\": \"Thursday\",\r\n                    \"value\": 8802\r\n                },\r\n                {\r\n                    \"hour\": \"11am\",\r\n                    \"weekday\": \"Thursday\",\r\n                    \"value\": 9420\r\n                },\r\n                {\r\n                    \"hour\": \"12am\",\r\n                    \"weekday\": \"Thursday\",\r\n                    \"value\": 8966\r\n                },\r\n                {\r\n                    \"hour\": \"1pm\",\r\n                    \"weekday\": \"Thursday\",\r\n                    \"value\": 8135\r\n                },\r\n                {\r\n                    \"hour\": \"2pm\",\r\n                    \"weekday\": \"Thursday\",\r\n                    \"value\": 8224\r\n                },\r\n                {\r\n                    \"hour\": \"3pm\",\r\n                    \"weekday\": \"Thursday\",\r\n                    \"value\": 8387\r\n                },\r\n                {\r\n                    \"hour\": \"4pm\",\r\n                    \"weekday\": \"Thursday\",\r\n                    \"value\": 8218\r\n                },\r\n                {\r\n                    \"hour\": \"5pm\",\r\n                    \"weekday\": \"Thursday\",\r\n                    \"value\": 7641\r\n                },\r\n                {\r\n                    \"hour\": \"6pm\",\r\n                    \"weekday\": \"Thursday\",\r\n                    \"value\": 6469\r\n                },\r\n                {\r\n                    \"hour\": \"7pm\",\r\n                    \"weekday\": \"Thursday\",\r\n                    \"value\": 5441\r\n                },\r\n                {\r\n                    \"hour\": \"8pm\",\r\n                    \"weekday\": \"Thursday\",\r\n                    \"value\": 4952\r\n                },\r\n                {\r\n                    \"hour\": \"9pm\",\r\n                    \"weekday\": \"Thursday\",\r\n                    \"value\": 4643\r\n                },\r\n                {\r\n                    \"hour\": \"10pm\",\r\n                    \"weekday\": \"Thursday\",\r\n                    \"value\": 4393\r\n                },\r\n                {\r\n                    \"hour\": \"11pm\",\r\n                    \"weekday\": \"Thursday\",\r\n                    \"value\": 4017\r\n                },\r\n                {\r\n                    \"hour\": \"12pm\",\r\n                    \"weekday\": \"Friday\",\r\n                    \"value\": 4022\r\n                },\r\n                {\r\n                    \"hour\": \"1am\",\r\n                    \"weekday\": \"Friday\",\r\n                    \"value\": 3063\r\n                },\r\n                {\r\n                    \"hour\": \"2am\",\r\n                    \"weekday\": \"Friday\",\r\n                    \"value\": 3638\r\n                },\r\n                {\r\n                    \"hour\": \"3am\",\r\n                    \"weekday\": \"Friday\",\r\n                    \"value\": 3968\r\n                },\r\n                {\r\n                    \"hour\": \"4am\",\r\n                    \"weekday\": \"Friday\",\r\n                    \"value\": 4070\r\n                },\r\n                {\r\n                    \"hour\": \"5am\",\r\n                    \"weekday\": \"Friday\",\r\n                    \"value\": 4019\r\n                },\r\n                {\r\n                    \"hour\": \"6am\",\r\n                    \"weekday\": \"Friday\",\r\n                    \"value\": 4548\r\n                },\r\n                {\r\n                    \"hour\": \"7am\",\r\n                    \"weekday\": \"Friday\",\r\n                    \"value\": 5465\r\n                },\r\n                {\r\n                    \"hour\": \"8am\",\r\n                    \"weekday\": \"Friday\",\r\n                    \"value\": 6909\r\n                },\r\n                {\r\n                    \"hour\": \"9am\",\r\n                    \"weekday\": \"Friday\",\r\n                    \"value\": 7706\r\n                },\r\n                {\r\n                    \"hour\": \"10am\",\r\n                    \"weekday\": \"Friday\",\r\n                    \"value\": 7867\r\n                },\r\n                {\r\n                    \"hour\": \"11am\",\r\n                    \"weekday\": \"Friday\",\r\n                    \"value\": 8615\r\n                },\r\n                {\r\n                    \"hour\": \"12am\",\r\n                    \"weekday\": \"Friday\",\r\n                    \"value\": 8218\r\n                },\r\n                {\r\n                    \"hour\": \"1pm\",\r\n                    \"weekday\": \"Friday\",\r\n                    \"value\": 7604\r\n                },\r\n                {\r\n                    \"hour\": \"2pm\",\r\n                    \"weekday\": \"Friday\",\r\n                    \"value\": 7429\r\n                },\r\n                {\r\n                    \"hour\": \"3pm\",\r\n                    \"weekday\": \"Friday\",\r\n                    \"value\": 7488\r\n                },\r\n                {\r\n                    \"hour\": \"4pm\",\r\n                    \"weekday\": \"Friday\",\r\n                    \"value\": 7493\r\n                },\r\n                {\r\n                    \"hour\": \"5pm\",\r\n                    \"weekday\": \"Friday\",\r\n                    \"value\": 6998\r\n                },\r\n                {\r\n                    \"hour\": \"6pm\",\r\n                    \"weekday\": \"Friday\",\r\n                    \"value\": 5941\r\n                },\r\n                {\r\n                    \"hour\": \"7pm\",\r\n                    \"weekday\": \"Friday\",\r\n                    \"value\": 5068\r\n                },\r\n                {\r\n                    \"hour\": \"8pm\",\r\n                    \"weekday\": \"Friday\",\r\n                    \"value\": 4636\r\n                },\r\n                {\r\n                    \"hour\": \"9pm\",\r\n                    \"weekday\": \"Friday\",\r\n                    \"value\": 4241\r\n                },\r\n                {\r\n                    \"hour\": \"10pm\",\r\n                    \"weekday\": \"Friday\",\r\n                    \"value\": 3858\r\n                },\r\n                {\r\n                    \"hour\": \"11pm\",\r\n                    \"weekday\": \"Friday\",\r\n                    \"value\": 3833\r\n                },\r\n                {\r\n                    \"hour\": \"12pm\",\r\n                    \"weekday\": \"Saturday\",\r\n                    \"value\": 3503\r\n                },\r\n                {\r\n                    \"hour\": \"1am\",\r\n                    \"weekday\": \"Saturday\",\r\n                    \"value\": 2842\r\n                },\r\n                {\r\n                    \"hour\": \"2am\",\r\n                    \"weekday\": \"Saturday\",\r\n                    \"value\": 2808\r\n                },\r\n                {\r\n                    \"hour\": \"3am\",\r\n                    \"weekday\": \"Saturday\",\r\n                    \"value\": 2399\r\n                },\r\n                {\r\n                    \"hour\": \"4am\",\r\n                    \"weekday\": \"Saturday\",\r\n                    \"value\": 2280\r\n                },\r\n                {\r\n                    \"hour\": \"5am\",\r\n                    \"weekday\": \"Saturday\",\r\n                    \"value\": 2139\r\n                },\r\n                {\r\n                    \"hour\": \"6am\",\r\n                    \"weekday\": \"Saturday\",\r\n                    \"value\": 2527\r\n                },\r\n                {\r\n                    \"hour\": \"7am\",\r\n                    \"weekday\": \"Saturday\",\r\n                    \"value\": 2940\r\n                },\r\n                {\r\n                    \"hour\": \"8am\",\r\n                    \"weekday\": \"Saturday\",\r\n                    \"value\": 3066\r\n                },\r\n                {\r\n                    \"hour\": \"9am\",\r\n                    \"weekday\": \"Saturday\",\r\n                    \"value\": 3494\r\n                },\r\n                {\r\n                    \"hour\": \"10am\",\r\n                    \"weekday\": \"Saturday\",\r\n                    \"value\": 3287\r\n                },\r\n                {\r\n                    \"hour\": \"11am\",\r\n                    \"weekday\": \"Saturday\",\r\n                    \"value\": 3416\r\n                },\r\n                {\r\n                    \"hour\": \"12am\",\r\n                    \"weekday\": \"Saturday\",\r\n                    \"value\": 3432\r\n                },\r\n                {\r\n                    \"hour\": \"1pm\",\r\n                    \"weekday\": \"Saturday\",\r\n                    \"value\": 3523\r\n                },\r\n                {\r\n                    \"hour\": \"2pm\",\r\n                    \"weekday\": \"Saturday\",\r\n                    \"value\": 3542\r\n                },\r\n                {\r\n                    \"hour\": \"3pm\",\r\n                    \"weekday\": \"Saturday\",\r\n                    \"value\": 3347\r\n                },\r\n                {\r\n                    \"hour\": \"4pm\",\r\n                    \"weekday\": \"Saturday\",\r\n                    \"value\": 3292\r\n                },\r\n                {\r\n                    \"hour\": \"5pm\",\r\n                    \"weekday\": \"Saturday\",\r\n                    \"value\": 3416\r\n                },\r\n                {\r\n                    \"hour\": \"6pm\",\r\n                    \"weekday\": \"Saturday\",\r\n                    \"value\": 3131\r\n                },\r\n                {\r\n                    \"hour\": \"7pm\",\r\n                    \"weekday\": \"Saturday\",\r\n                    \"value\": 3057\r\n                },\r\n                {\r\n                    \"hour\": \"8pm\",\r\n                    \"weekday\": \"Saturday\",\r\n                    \"value\": 3227\r\n                },\r\n                {\r\n                    \"hour\": \"9pm\",\r\n                    \"weekday\": \"Saturday\",\r\n                    \"value\": 3060\r\n                },\r\n                {\r\n                    \"hour\": \"10pm\",\r\n                    \"weekday\": \"Saturday\",\r\n                    \"value\": 2855\r\n                },\r\n                {\r\n                    \"hour\": \"11pm\",\r\n                    \"weekday\": \"Saturday\",\r\n                    \"value\": 2625\r\n                }\r\n\r\n            ];\r\n\r\n        }); // end am4core.ready()\r\n    }\r\n\r\n    return {\r\n        // Public Functions\r\n        init: function () {\r\n            _demo1();\r\n            _demo2();\r\n            _demo3();\r\n            _demo4();\r\n            _demo5();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function () {\r\n    KTGeneralAmCharts.init();\r\n});\r\n"], "names": [], "sourceRoot": ""}