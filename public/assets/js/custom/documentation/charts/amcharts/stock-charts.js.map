{"version": 3, "file": "js/custom/documentation/charts/amcharts/stock-charts.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mCAAmC,aAAa;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC,aAAa;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,GAAG;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B,UAAU;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,gGAAgG;AAC5H;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC,KAAK,GAAG,8DAA8D;AAC1G;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC,KAAK,GAAG,8DAA8D;AAC1G;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC,KAAK,GAAG,8DAA8D;AAC1G;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iDAAiD,aAAa;AAC9D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,GAAG;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,4BAA4B,6BAA6B;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,GAAG;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC", "sources": ["webpack://keenthemes/../../../themes/metronic/html/demo3/src/js/custom/documentation/charts/amcharts/stock-charts.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTGeneralAmChartsMaps = function () {\r\n    // Shared variables\r\n    var chart;\r\n\r\n    // Private functions\r\n    var _demo1 = function () {\r\n        // Init AmChart -- for more info, please visit the official documentiation: https://www.amcharts.com/docs/v4/\r\n        am4core.ready(function () {\r\n\r\n            // Themes begin\r\n            am4core.useTheme(am4themes_animated);\r\n            // Themes end\r\n\r\n            // Create chart\r\n            chart = am4core.create(\"kt_amcharts_1\", am4charts.XYChart);\r\n            chart.padding(0, 15, 0, 15);\r\n\r\n            // Load external data\r\n            chart.dataSource.url = \"https://www.amcharts.com/wp-content/uploads/assets/stock/MSFT.csv\";\r\n            chart.dataSource.parser = new am4core.CSVParser();\r\n            chart.dataSource.parser.options.useColumnNames = true;\r\n            chart.dataSource.parser.options.reverse = true;\r\n\r\n            // the following line makes value axes to be arranged vertically.\r\n            chart.leftAxesContainer.layout = \"vertical\";\r\n\r\n            // uncomment this line if you want to change order of axes\r\n            //chart.bottomAxesContainer.reverseOrder = true;\r\n\r\n            var dateAxis = chart.xAxes.push(new am4charts.DateAxis());\r\n            dateAxis.renderer.grid.template.location = 0;\r\n            dateAxis.renderer.ticks.template.length = 8;\r\n            dateAxis.renderer.ticks.template.strokeOpacity = 0.1;\r\n            dateAxis.renderer.grid.template.disabled = true;\r\n            dateAxis.renderer.ticks.template.disabled = false;\r\n            dateAxis.renderer.ticks.template.strokeOpacity = 0.2;\r\n            dateAxis.renderer.minLabelPosition = 0.01;\r\n            dateAxis.renderer.maxLabelPosition = 0.99;\r\n            dateAxis.keepSelection = true;\r\n            dateAxis.minHeight = 30;\r\n\r\n            dateAxis.groupData = true;\r\n            dateAxis.minZoomCount = 5;\r\n\r\n            // these two lines makes the axis to be initially zoomed-in\r\n            // dateAxis.start = 0.7;\r\n            // dateAxis.keepSelection = true;\r\n\r\n            var valueAxis = chart.yAxes.push(new am4charts.ValueAxis());\r\n            valueAxis.tooltip.disabled = true;\r\n            valueAxis.zIndex = 1;\r\n            valueAxis.renderer.baseGrid.disabled = true;\r\n            // height of axis\r\n            valueAxis.height = am4core.percent(65);\r\n\r\n            valueAxis.renderer.gridContainer.background.fill = am4core.color(\"#000000\");\r\n            valueAxis.renderer.gridContainer.background.fillOpacity = 0.05;\r\n            valueAxis.renderer.inside = true;\r\n            valueAxis.renderer.labels.template.verticalCenter = \"bottom\";\r\n            valueAxis.renderer.labels.template.padding(2, 2, 2, 2);\r\n\r\n            //valueAxis.renderer.maxLabelPosition = 0.95;\r\n            valueAxis.renderer.fontSize = \"0.8em\"\r\n\r\n            var series = chart.series.push(new am4charts.LineSeries());\r\n            series.dataFields.dateX = \"Date\";\r\n            series.dataFields.valueY = \"Adj Close\";\r\n            series.tooltipText = \"{valueY.value}\";\r\n            series.name = \"MSFT: Value\";\r\n            series.defaultState.transitionDuration = 0;\r\n\r\n            var valueAxis2 = chart.yAxes.push(new am4charts.ValueAxis());\r\n            valueAxis2.tooltip.disabled = true;\r\n            // height of axis\r\n            valueAxis2.height = am4core.percent(35);\r\n            valueAxis2.zIndex = 3\r\n            // this makes gap between panels\r\n            valueAxis2.marginTop = 30;\r\n            valueAxis2.renderer.baseGrid.disabled = true;\r\n            valueAxis2.renderer.inside = true;\r\n            valueAxis2.renderer.labels.template.verticalCenter = \"bottom\";\r\n            valueAxis2.renderer.labels.template.padding(2, 2, 2, 2);\r\n            //valueAxis.renderer.maxLabelPosition = 0.95;\r\n            valueAxis2.renderer.fontSize = \"0.8em\"\r\n\r\n            valueAxis2.renderer.gridContainer.background.fill = am4core.color(\"#000000\");\r\n            valueAxis2.renderer.gridContainer.background.fillOpacity = 0.05;\r\n\r\n            var series2 = chart.series.push(new am4charts.ColumnSeries());\r\n            series2.dataFields.dateX = \"Date\";\r\n            series2.dataFields.valueY = \"Volume\";\r\n            series2.yAxis = valueAxis2;\r\n            series2.tooltipText = \"{valueY.value}\";\r\n            series2.name = \"MSFT: Volume\";\r\n            // volume should be summed\r\n            series2.groupFields.valueY = \"sum\";\r\n            series2.defaultState.transitionDuration = 0;\r\n\r\n            chart.cursor = new am4charts.XYCursor();\r\n\r\n            var scrollbarX = new am4charts.XYChartScrollbar();\r\n            scrollbarX.series.push(series);\r\n            scrollbarX.marginBottom = 20;\r\n            scrollbarX.scrollbarChart.xAxes.getIndex(0).minHeight = undefined;\r\n            chart.scrollbarX = scrollbarX;\r\n\r\n        }); // end am4core.ready()\r\n    }\r\n\r\n\r\n    var _demo2 = function () {\r\n        // Init AmChart -- for more info, please visit the official documentiation: https://www.amcharts.com/docs/v4/\r\n        am4core.ready(function () {\r\n\r\n            // Themes begin\r\n            am4core.useTheme(am4themes_animated);\r\n            // Themes end\r\n\r\n            chart = am4core.create(\"kt_amcharts_2\", am4charts.XYChart);\r\n            chart.padding(0, 15, 0, 15);\r\n            chart.colors.step = 3;\r\n\r\n            var data = [];\r\n            var price1 = 1000;\r\n            var price2 = 2000;\r\n            var price3 = 3000;\r\n            var quantity = 1000;\r\n            for (var i = 15; i < 3000; i++) {\r\n                price1 += Math.round((Math.random() < 0.5 ? 1 : -1) * Math.random() * 100);\r\n                price2 += Math.round((Math.random() < 0.5 ? 1 : -1) * Math.random() * 100);\r\n                price3 += Math.round((Math.random() < 0.5 ? 1 : -1) * Math.random() * 100);\r\n\r\n                if (price1 < 100) {\r\n                    price1 = 100;\r\n                }\r\n\r\n                if (price2 < 100) {\r\n                    price2 = 100;\r\n                }\r\n\r\n                if (price3 < 100) {\r\n                    price3 = 100;\r\n                }\r\n\r\n                quantity += Math.round((Math.random() < 0.5 ? 1 : -1) * Math.random() * 500);\r\n\r\n                if (quantity < 0) {\r\n                    quantity *= -1;\r\n                }\r\n                data.push({ date: new Date(2000, 0, i), price1: price1, price2: price2, price3: price3, quantity: quantity });\r\n            }\r\n\r\n\r\n            chart.data = data;\r\n            // the following line makes value axes to be arranged vertically.\r\n            chart.leftAxesContainer.layout = \"vertical\";\r\n\r\n            // uncomment this line if you want to change order of axes\r\n            //chart.bottomAxesContainer.reverseOrder = true;\r\n\r\n            var dateAxis = chart.xAxes.push(new am4charts.DateAxis());\r\n            dateAxis.renderer.grid.template.location = 0;\r\n            dateAxis.renderer.ticks.template.length = 8;\r\n            dateAxis.renderer.ticks.template.strokeOpacity = 0.1;\r\n            dateAxis.renderer.grid.template.disabled = true;\r\n            dateAxis.renderer.ticks.template.disabled = false;\r\n            dateAxis.renderer.ticks.template.strokeOpacity = 0.2;\r\n            dateAxis.renderer.minLabelPosition = 0.01;\r\n            dateAxis.renderer.maxLabelPosition = 0.99;\r\n            dateAxis.keepSelection = true;\r\n\r\n            dateAxis.groupData = true;\r\n            dateAxis.minZoomCount = 5;\r\n\r\n            // these two lines makes the axis to be initially zoomed-in\r\n            // dateAxis.start = 0.7;\r\n            // dateAxis.keepSelection = true;\r\n\r\n            var valueAxis = chart.yAxes.push(new am4charts.ValueAxis());\r\n            valueAxis.tooltip.disabled = true;\r\n            valueAxis.zIndex = 1;\r\n            valueAxis.renderer.baseGrid.disabled = true;\r\n            // height of axis\r\n            valueAxis.height = am4core.percent(65);\r\n\r\n            valueAxis.renderer.gridContainer.background.fill = am4core.color(\"#000000\");\r\n            valueAxis.renderer.gridContainer.background.fillOpacity = 0.05;\r\n            valueAxis.renderer.inside = true;\r\n            valueAxis.renderer.labels.template.verticalCenter = \"bottom\";\r\n            valueAxis.renderer.labels.template.padding(2, 2, 2, 2);\r\n\r\n            //valueAxis.renderer.maxLabelPosition = 0.95;\r\n            valueAxis.renderer.fontSize = \"0.8em\"\r\n\r\n            var series1 = chart.series.push(new am4charts.LineSeries());\r\n            series1.dataFields.dateX = \"date\";\r\n            series1.dataFields.valueY = \"price1\";\r\n            series1.dataFields.valueYShow = \"changePercent\";\r\n            series1.tooltipText = \"{name}: {valueY.changePercent.formatNumber('[#0c0]+#.00|[#c00]#.##|0')}%\";\r\n            series1.name = \"Stock A\";\r\n            series1.tooltip.getFillFromObject = false;\r\n            series1.tooltip.getStrokeFromObject = true;\r\n            series1.tooltip.background.fill = am4core.color(\"#fff\");\r\n            series1.tooltip.background.strokeWidth = 2;\r\n            series1.tooltip.label.fill = series1.stroke;\r\n\r\n            var series2 = chart.series.push(new am4charts.LineSeries());\r\n            series2.dataFields.dateX = \"date\";\r\n            series2.dataFields.valueY = \"price2\";\r\n            series2.dataFields.valueYShow = \"changePercent\";\r\n            series2.tooltipText = \"{name}: {valueY.changePercent.formatNumber('[#0c0]+#.00|[#c00]#.##|0')}%\";\r\n            series2.name = \"Stock B\";\r\n            series2.tooltip.getFillFromObject = false;\r\n            series2.tooltip.getStrokeFromObject = true;\r\n            series2.tooltip.background.fill = am4core.color(\"#fff\");\r\n            series2.tooltip.background.strokeWidth = 2;\r\n            series2.tooltip.label.fill = series2.stroke;\r\n\r\n            var series3 = chart.series.push(new am4charts.LineSeries());\r\n            series3.dataFields.dateX = \"date\";\r\n            series3.dataFields.valueY = \"price3\";\r\n            series3.dataFields.valueYShow = \"changePercent\";\r\n            series3.tooltipText = \"{name}: {valueY.changePercent.formatNumber('[#0c0]+#.00|[#c00]#.##|0')}%\";\r\n            series3.name = \"Stock C\";\r\n            series3.tooltip.getFillFromObject = false;\r\n            series3.tooltip.getStrokeFromObject = true;\r\n            series3.tooltip.background.fill = am4core.color(\"#fff\");\r\n            series3.tooltip.background.strokeWidth = 2;\r\n            series3.tooltip.label.fill = series3.stroke;\r\n\r\n            var valueAxis2 = chart.yAxes.push(new am4charts.ValueAxis());\r\n            valueAxis2.tooltip.disabled = true;\r\n            // height of axis\r\n            valueAxis2.height = am4core.percent(35);\r\n            valueAxis2.zIndex = 3\r\n            // this makes gap between panels\r\n            valueAxis2.marginTop = 30;\r\n            valueAxis2.renderer.baseGrid.disabled = true;\r\n            valueAxis2.renderer.inside = true;\r\n            valueAxis2.renderer.labels.template.verticalCenter = \"bottom\";\r\n            valueAxis2.renderer.labels.template.padding(2, 2, 2, 2);\r\n            //valueAxis.renderer.maxLabelPosition = 0.95;\r\n            valueAxis2.renderer.fontSize = \"0.8em\";\r\n\r\n            valueAxis2.renderer.gridContainer.background.fill = am4core.color(\"#000000\");\r\n            valueAxis2.renderer.gridContainer.background.fillOpacity = 0.05;\r\n\r\n            var volumeSeries = chart.series.push(new am4charts.StepLineSeries());\r\n            volumeSeries.fillOpacity = 1;\r\n            volumeSeries.fill = series1.stroke;\r\n            volumeSeries.stroke = series1.stroke;\r\n            volumeSeries.dataFields.dateX = \"date\";\r\n            volumeSeries.dataFields.valueY = \"quantity\";\r\n            volumeSeries.yAxis = valueAxis2;\r\n            volumeSeries.tooltipText = \"Volume: {valueY.value}\";\r\n            volumeSeries.name = \"Series 2\";\r\n            // volume should be summed\r\n            volumeSeries.groupFields.valueY = \"sum\";\r\n            volumeSeries.tooltip.label.fill = volumeSeries.stroke;\r\n            chart.cursor = new am4charts.XYCursor();\r\n\r\n            var scrollbarX = new am4charts.XYChartScrollbar();\r\n            scrollbarX.series.push(series1);\r\n            scrollbarX.marginBottom = 20;\r\n            var sbSeries = scrollbarX.scrollbarChart.series.getIndex(0);\r\n            sbSeries.dataFields.valueYShow = undefined;\r\n            chart.scrollbarX = scrollbarX;\r\n\r\n        }); // end am4core.ready()\r\n    }\r\n\r\n    var _demo3 = function () {\r\n        // Init AmChart -- for more info, please visit the official documentiation: https://www.amcharts.com/docs/v4/\r\n        am4core.ready(function () {\r\n\r\n            // Themes begin\r\n            am4core.useTheme(am4themes_animated);\r\n            // Themes end\r\n\r\n            // Create chart instance\r\n            chart = am4core.create(\"kt_amcharts_3\", am4charts.XYChart);\r\n\r\n            // Add data\r\n            chart.data = [{\r\n                \"year\": \"2011\",\r\n                \"value\": 600000\r\n            }, {\r\n                \"year\": \"2012\",\r\n                \"value\": 900000\r\n            }, {\r\n                \"year\": \"2013\",\r\n                \"value\": 180000\r\n            }, {\r\n                \"year\": \"2014\",\r\n                \"value\": 600000\r\n            }, {\r\n                \"year\": \"2015\",\r\n                \"value\": 350000\r\n            }, {\r\n                \"year\": \"2016\",\r\n                \"value\": 600000\r\n            }, {\r\n                \"year\": \"2017\",\r\n                \"value\": 670000\r\n            }];\r\n\r\n            // Populate data\r\n            for (var i = 0; i < (chart.data.length - 1); i++) {\r\n                chart.data[i].valueNext = chart.data[i + 1].value;\r\n            }\r\n\r\n            // Create axes\r\n            var categoryAxis = chart.xAxes.push(new am4charts.CategoryAxis());\r\n            categoryAxis.dataFields.category = \"year\";\r\n            categoryAxis.renderer.grid.template.location = 0;\r\n            categoryAxis.renderer.minGridDistance = 30;\r\n\r\n            var valueAxis = chart.yAxes.push(new am4charts.ValueAxis());\r\n            valueAxis.min = 0;\r\n\r\n            // Create series\r\n            var series = chart.series.push(new am4charts.ColumnSeries());\r\n            series.dataFields.valueY = \"value\";\r\n            series.dataFields.categoryX = \"year\";\r\n\r\n            // Add series for showing variance arrows\r\n            var series2 = chart.series.push(new am4charts.ColumnSeries());\r\n            series2.dataFields.valueY = \"valueNext\";\r\n            series2.dataFields.openValueY = \"value\";\r\n            series2.dataFields.categoryX = \"year\";\r\n            series2.columns.template.width = 1;\r\n            series2.fill = am4core.color(\"#555\");\r\n            series2.stroke = am4core.color(\"#555\");\r\n\r\n            // Add a triangle for arrow tip\r\n            var arrow = series2.bullets.push(new am4core.Triangle);\r\n            arrow.width = 10;\r\n            arrow.height = 10;\r\n            arrow.horizontalCenter = \"middle\";\r\n            arrow.verticalCenter = \"top\";\r\n            arrow.dy = -1;\r\n\r\n            // Set up a rotation adapter which would rotate the triangle if its a negative change\r\n            arrow.adapter.add(\"rotation\", function (rotation, target) {\r\n                return getVariancePercent(target.dataItem) < 0 ? 180 : rotation;\r\n            });\r\n\r\n            // Set up a rotation adapter which adjusts Y position\r\n            arrow.adapter.add(\"dy\", function (dy, target) {\r\n                return getVariancePercent(target.dataItem) < 0 ? 1 : dy;\r\n            });\r\n\r\n            // Add a label\r\n            var label = series2.bullets.push(new am4core.Label);\r\n            label.padding(10, 10, 10, 10);\r\n            label.text = \"\";\r\n            label.fill = am4core.color(\"#0c0\");\r\n            label.strokeWidth = 0;\r\n            label.horizontalCenter = \"middle\";\r\n            label.verticalCenter = \"bottom\";\r\n            label.fontWeight = \"bolder\";\r\n\r\n            // Adapter for label text which calculates change in percent\r\n            label.adapter.add(\"textOutput\", function (text, target) {\r\n                var percent = getVariancePercent(target.dataItem);\r\n                return percent ? percent + \"%\" : text;\r\n            });\r\n\r\n            // Adapter which shifts the label if it's below the variance column\r\n            label.adapter.add(\"verticalCenter\", function (center, target) {\r\n                return getVariancePercent(target.dataItem) < 0 ? \"top\" : center;\r\n            });\r\n\r\n            // Adapter which changes color of label to red\r\n            label.adapter.add(\"fill\", function (fill, target) {\r\n                return getVariancePercent(target.dataItem) < 0 ? am4core.color(\"#c00\") : fill;\r\n            });\r\n\r\n            function getVariancePercent(dataItem) {\r\n                if (dataItem) {\r\n                    var value = dataItem.valueY;\r\n                    var openValue = dataItem.openValueY;\r\n                    var change = value - openValue;\r\n                    return Math.round(change / openValue * 100);\r\n                }\r\n                return 0;\r\n            }\r\n\r\n        }); // end am4core.ready()\r\n    }\r\n\r\n    return {\r\n        // Public Functions\r\n        init: function () {\r\n            _demo1();\r\n            _demo2();\r\n            _demo3();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function () {\r\n    KTGeneralAmChartsMaps.init();\r\n});\r\n"], "names": [], "sourceRoot": ""}