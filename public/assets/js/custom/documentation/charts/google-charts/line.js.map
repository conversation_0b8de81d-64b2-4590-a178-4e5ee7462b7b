{"version": 3, "file": "js/custom/documentation/charts/google-charts/line.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC", "sources": ["webpack://keenthemes/../src/js/custom/documentation/charts/google-charts/line.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTGoogleChartLineDemo = function () {\r\n    // Private functions\r\n    var exampleLine = function () {\r\n        // GOOGLE CHARTS INIT\r\n        google.load('visualization', '1', {\r\n            packages: ['corechart', 'bar', 'line']\r\n        });\r\n\r\n        google.setOnLoadCallback(function () {\r\n            // LINE CHART\r\n            var data = new google.visualization.DataTable();\r\n            data.addColumn('number', 'Day');\r\n            data.addColumn('number', 'Guardians of the Galaxy');\r\n            data.addColumn('number', 'The Avengers');\r\n            data.addColumn('number', 'Transformers: Age of Extinction');\r\n\r\n            data.addRows([\r\n                [1, 37.8, 80.8, 41.8],\r\n                [2, 30.9, 69.5, 32.4],\r\n                [3, 25.4, 57, 25.7],\r\n                [4, 11.7, 18.8, 10.5],\r\n                [5, 11.9, 17.6, 10.4],\r\n                [6, 8.8, 13.6, 7.7],\r\n                [7, 7.6, 12.3, 9.6],\r\n                [8, 12.3, 29.2, 10.6],\r\n                [9, 16.9, 42.9, 14.8],\r\n                [10, 12.8, 30.9, 11.6],\r\n                [11, 5.3, 7.9, 4.7],\r\n                [12, 6.6, 8.4, 5.2],\r\n                [13, 4.8, 6.3, 3.6],\r\n                [14, 4.2, 6.2, 3.4]\r\n            ]);\r\n\r\n            var options = {\r\n                chart: {\r\n                    title: 'Box Office Earnings in First Two Weeks of Opening',\r\n                    subtitle: 'in millions of dollars (USD)'\r\n                },\r\n                colors: ['#6e4ff5', '#f6aa33', '#fe3995']\r\n            };\r\n\r\n            var chart = new google.charts.Line(document.getElementById('kt_docs_google_chart_line'));\r\n            chart.draw(data, options);\r\n        });\r\n    }\r\n\r\n    return {\r\n        // Public Functions\r\n        init: function () {\r\n            exampleLine();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function () {\r\n    KTGoogleChartLineDemo.init();\r\n});\r\n"], "names": [], "sourceRoot": ""}