{"version": 3, "file": "js/custom/documentation/charts/google-charts/column.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC", "sources": ["webpack://keenthemes/../src/js/custom/documentation/charts/google-charts/column.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTGoogleChartColumnDemo = function () {\r\n    // Private functions\r\n    var exampleColumn = function () {\r\n        // GOOGLE CHARTS INIT\r\n        google.load('visualization', '1', {\r\n            packages: ['corechart', 'bar', 'line']\r\n        });\r\n\r\n        google.setOnLoadCallback(function () {\r\n            // COLUMN CHART\r\n            var data = new google.visualization.DataTable();\r\n            data.addColumn('timeofday', 'Time of Day');\r\n            data.addColumn('number', 'Motivation Level');\r\n            data.addColumn('number', 'Energy Level');\r\n\r\n            data.addRows([\r\n                [{\r\n                    v: [8, 0, 0],\r\n                    f: '8 am'\r\n                }, 1, .25],\r\n                [{\r\n                    v: [9, 0, 0],\r\n                    f: '9 am'\r\n                }, 2, .5],\r\n                [{\r\n                    v: [10, 0, 0],\r\n                    f: '10 am'\r\n                }, 3, 1],\r\n                [{\r\n                    v: [11, 0, 0],\r\n                    f: '11 am'\r\n                }, 4, 2.25],\r\n                [{\r\n                    v: [12, 0, 0],\r\n                    f: '12 pm'\r\n                }, 5, 2.25],\r\n                [{\r\n                    v: [13, 0, 0],\r\n                    f: '1 pm'\r\n                }, 6, 3],\r\n                [{\r\n                    v: [14, 0, 0],\r\n                    f: '2 pm'\r\n                }, 7, 4],\r\n                [{\r\n                    v: [15, 0, 0],\r\n                    f: '3 pm'\r\n                }, 8, 5.25],\r\n                [{\r\n                    v: [16, 0, 0],\r\n                    f: '4 pm'\r\n                }, 9, 7.5],\r\n                [{\r\n                    v: [17, 0, 0],\r\n                    f: '5 pm'\r\n                }, 10, 10],\r\n            ]);\r\n\r\n            var options = {\r\n                title: 'Motivation and Energy Level Throughout the Day',\r\n                focusTarget: 'category',\r\n                hAxis: {\r\n                    title: 'Time of Day',\r\n                    format: 'h:mm a',\r\n                    viewWindow: {\r\n                        min: [7, 30, 0],\r\n                        max: [17, 30, 0]\r\n                    },\r\n                },\r\n                vAxis: {\r\n                    title: 'Rating (scale of 1-10)'\r\n                },\r\n                colors: ['#6e4ff5', '#fe3995']\r\n            };\r\n\r\n            var chart = new google.visualization.ColumnChart(document.getElementById('kt_docs_google_chart_column'));\r\n            chart.draw(data, options);\r\n        });\r\n    }\r\n\r\n    return {\r\n        // Public Functions\r\n        init: function () {\r\n            exampleColumn();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function () {\r\n    KTGoogleChartColumnDemo.init();\r\n});\r\n"], "names": [], "sourceRoot": ""}