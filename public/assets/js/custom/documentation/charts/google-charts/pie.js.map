{"version": 3, "file": "js/custom/documentation/charts/google-charts/pie.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC", "sources": ["webpack://keenthemes/../src/js/custom/documentation/charts/google-charts/pie.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTGoogleChartPieDemo = function () {\r\n    // Private functions\r\n    var examplePie = function () {\r\n        // GOOGLE CHARTS INIT\r\n        google.load('visualization', '1', {\r\n            packages: ['corechart', 'bar', 'line']\r\n        });\r\n\r\n        google.setOnLoadCallback(function () {\r\n            var data = google.visualization.arrayToDataTable([\r\n                ['Task', 'Hours per Day'],\r\n                ['Work', 11],\r\n                ['Eat', 2],\r\n                ['Commute', 2],\r\n                ['Watch TV', 2],\r\n                ['Sleep', 7]\r\n            ]);\r\n\r\n            var options = {\r\n                title: 'My Daily Activities',\r\n                colors: ['#fe3995', '#f6aa33', '#6e4ff5', '#2abe81', '#c7d2e7', '#593ae1']\r\n            };\r\n\r\n            var chart = new google.visualization.PieChart(document.getElementById('kt_docs_google_chart_pie'));\r\n            chart.draw(data, options);\r\n\r\n            // Example of a doughnut chart\r\n            // var options = {\r\n            //     pieHole: 0.4,\r\n            //     colors: ['#fe3995', '#f6aa33', '#6e4ff5', '#2abe81', '#c7d2e7', '#593ae1']\r\n            // };\r\n\r\n            // var chart = new google.visualization.PieChart(document.getElementById('kt_docs_google_chart_pie'));\r\n            // chart.draw(data, options);\r\n        });\r\n    }\r\n\r\n    return {\r\n        // Public Functions\r\n        init: function () {\r\n            examplePie();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function () {\r\n    KTGoogleChartPieDemo.init();\r\n});\r\n"], "names": [], "sourceRoot": ""}