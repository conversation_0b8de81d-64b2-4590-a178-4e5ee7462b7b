{"version": 3, "file": "js/custom/documentation/charts/apexcharts.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,2BAA2B;AAC3B;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC", "sources": ["webpack://keenthemes/../src/js/custom/documentation/charts/apexcharts.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTGeneralApexCharts = function () {\r\n    // Shared variables\r\n\r\n    // Private functions\r\n    var example1 = function () {\r\n        var element = document.getElementById(\"kt_apexcharts_1\");\r\n\r\n        var height = parseInt(KTUtil.css(element, 'height'));\r\n        var labelColor = KTUtil.getCssVariableValue('--bs-gray-500');\r\n        var borderColor = KTUtil.getCssVariableValue('--bs-gray-200');\r\n        var baseColor = KTUtil.getCssVariableValue('--bs-primary');\r\n        var secondaryColor = KTUtil.getCssVariableValue('--bs-gray-300');\r\n        var dangerColor = KTUtil.getCssVariableValue('--bs-danger');\r\n\r\n        if (!element) {\r\n            return;\r\n        }\r\n\r\n        var options = {\r\n            series: [{\r\n                name: 'Net Profit',\r\n                data: [44, 55, 57, 56, 61, 58, 43, 56, 65, 41, 55, 66]\r\n            }, {\r\n                name: 'Cost',\r\n                data: [32, 34, 52, 46, 27, 60, 41, 49, 13, 11, 44, 33]\r\n            }, {\r\n                name: 'Revenue',\r\n                data: [76, 85, 101, 98, 87, 105, 87, 99, 75, 82, 91, 89]\r\n            }],\r\n            chart: {\r\n                fontFamily: 'inherit',\r\n                type: 'bar',\r\n                height: height,\r\n                toolbar: {\r\n                    show: false\r\n                }\r\n            },\r\n            plotOptions: {\r\n                bar: {\r\n                    horizontal: false,\r\n                    columnWidth: ['40%'],\r\n                    endingShape: 'rounded'\r\n                },\r\n            },\r\n            legend: {\r\n                show: false\r\n            },\r\n            dataLabels: {\r\n                enabled: false\r\n            },\r\n            stroke: {\r\n                show: true,\r\n                width: 2,\r\n                colors: ['transparent']\r\n            },\r\n            xaxis: {\r\n                categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],\r\n                axisBorder: {\r\n                    show: false,\r\n                },\r\n                axisTicks: {\r\n                    show: false\r\n                },\r\n                labels: {\r\n                    style: {\r\n                        colors: labelColor,\r\n                        fontSize: '12px'\r\n                    }\r\n                }\r\n            },\r\n            yaxis: {\r\n                labels: {\r\n                    style: {\r\n                        colors: labelColor,\r\n                        fontSize: '12px'\r\n                    }\r\n                }\r\n            },\r\n            fill: {\r\n                opacity: 1\r\n            },\r\n            states: {\r\n                normal: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                hover: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                active: {\r\n                    allowMultipleDataPointsSelection: false,\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                }\r\n            },\r\n            tooltip: {\r\n                style: {\r\n                    fontSize: '12px'\r\n                },\r\n                y: {\r\n                    formatter: function (val) {\r\n                        return \"$\" + val + \" thousands\"\r\n                    }\r\n                }\r\n            },\r\n            colors: [baseColor, dangerColor, secondaryColor],\r\n            grid: {\r\n                borderColor: borderColor,\r\n                strokeDashArray: 4,\r\n                yaxis: {\r\n                    lines: {\r\n                        show: true\r\n                    }\r\n                }\r\n            }\r\n        };\r\n\r\n        var chart = new ApexCharts(element, options);\r\n        chart.render();\r\n    }\r\n\r\n    var example2 = function () {\r\n        var element = document.getElementById(\"kt_apexcharts_2\");\r\n\r\n        var height = parseInt(KTUtil.css(element, 'height'));\r\n        var labelColor = KTUtil.getCssVariableValue('--bs-gray-500');\r\n        var borderColor = KTUtil.getCssVariableValue('--bs-gray-200');\r\n        var baseColor = KTUtil.getCssVariableValue('--bs-warning');\r\n        var secondaryColor = KTUtil.getCssVariableValue('--bs-gray-300');\r\n\r\n        if (!element) {\r\n            return;\r\n        }\r\n\r\n        var options = {\r\n            series: [{\r\n                name: 'Net Profit',\r\n                data: [44, 55, 57, 56, 61, 58]\r\n            }, {\r\n                name: 'Revenue',\r\n                data: [76, 85, 101, 98, 87, 105]\r\n            }],\r\n            chart: {\r\n                fontFamily: 'inherit',\r\n                type: 'bar',\r\n                height: height,\r\n                toolbar: {\r\n                    show: false\r\n                }\r\n            },\r\n            plotOptions: {\r\n                bar: {\r\n                    horizontal: true,\r\n                    columnWidth: ['30%'],\r\n                    endingShape: 'rounded'\r\n                },\r\n            },\r\n            legend: {\r\n                show: false\r\n            },\r\n            dataLabels: {\r\n                enabled: false\r\n            },\r\n            stroke: {\r\n                show: true,\r\n                width: 2,\r\n                colors: ['transparent']\r\n            },\r\n            xaxis: {\r\n                categories: ['Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],\r\n                axisBorder: {\r\n                    show: false,\r\n                },\r\n                axisTicks: {\r\n                    show: false\r\n                },\r\n                labels: {\r\n                    style: {\r\n                        colors: labelColor,\r\n                        fontSize: '12px'\r\n                    }\r\n                }\r\n            },\r\n            yaxis: {\r\n                labels: {\r\n                    style: {\r\n                        colors: labelColor,\r\n                        fontSize: '12px'\r\n                    }\r\n                }\r\n            },\r\n            fill: {\r\n                opacity: 1\r\n            },\r\n            states: {\r\n                normal: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                hover: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                active: {\r\n                    allowMultipleDataPointsSelection: false,\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                }\r\n            },\r\n            tooltip: {\r\n                style: {\r\n                    fontSize: '12px'\r\n                },\r\n                y: {\r\n                    formatter: function (val) {\r\n                        return \"$\" + val + \" thousands\"\r\n                    }\r\n                }\r\n            },\r\n            colors: [baseColor, secondaryColor],\r\n            grid: {\r\n                borderColor: borderColor,\r\n                strokeDashArray: 4,\r\n                yaxis: {\r\n                    lines: {\r\n                        show: true\r\n                    }\r\n                }\r\n            }\r\n        };\r\n\r\n        var chart = new ApexCharts(element, options);\r\n        chart.render();\r\n    }\r\n\r\n    var example3 = function () {\r\n        var element = document.getElementById(\"kt_apexcharts_3\");\r\n\r\n        var height = parseInt(KTUtil.css(element, 'height'));\r\n        var labelColor = KTUtil.getCssVariableValue('--bs-gray-500');\r\n        var borderColor = KTUtil.getCssVariableValue('--bs-gray-200');\r\n        var baseColor = KTUtil.getCssVariableValue('--bs-info');\r\n        var lightColor = KTUtil.getCssVariableValue('--bs-light-info');\r\n\r\n        if (!element) {\r\n            return;\r\n        }\r\n\r\n        var options = {\r\n            series: [{\r\n                name: 'Net Profit',\r\n                data: [30, 40, 40, 90, 90, 70, 70]\r\n            }],\r\n            chart: {\r\n                fontFamily: 'inherit',\r\n                type: 'area',\r\n                height: height,\r\n                toolbar: {\r\n                    show: false\r\n                }\r\n            },\r\n            plotOptions: {\r\n\r\n            },\r\n            legend: {\r\n                show: false\r\n            },\r\n            dataLabels: {\r\n                enabled: false\r\n            },\r\n            fill: {\r\n                type: 'solid',\r\n                opacity: 1\r\n            },\r\n            stroke: {\r\n                curve: 'smooth',\r\n                show: true,\r\n                width: 3,\r\n                colors: [baseColor]\r\n            },\r\n            xaxis: {\r\n                categories: ['Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug'],\r\n                axisBorder: {\r\n                    show: false,\r\n                },\r\n                axisTicks: {\r\n                    show: false\r\n                },\r\n                labels: {\r\n                    style: {\r\n                        colors: labelColor,\r\n                        fontSize: '12px'\r\n                    }\r\n                },\r\n                crosshairs: {\r\n                    position: 'front',\r\n                    stroke: {\r\n                        color: baseColor,\r\n                        width: 1,\r\n                        dashArray: 3\r\n                    }\r\n                },\r\n                tooltip: {\r\n                    enabled: true,\r\n                    formatter: undefined,\r\n                    offsetY: 0,\r\n                    style: {\r\n                        fontSize: '12px'\r\n                    }\r\n                }\r\n            },\r\n            yaxis: {\r\n                labels: {\r\n                    style: {\r\n                        colors: labelColor,\r\n                        fontSize: '12px'\r\n                    }\r\n                }\r\n            },\r\n            states: {\r\n                normal: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                hover: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                active: {\r\n                    allowMultipleDataPointsSelection: false,\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                }\r\n            },\r\n            tooltip: {\r\n                style: {\r\n                    fontSize: '12px'\r\n                },\r\n                y: {\r\n                    formatter: function (val) {\r\n                        return \"$\" + val + \" thousands\"\r\n                    }\r\n                }\r\n            },\r\n            colors: [lightColor],\r\n            grid: {\r\n                borderColor: borderColor,\r\n                strokeDashArray: 4,\r\n                yaxis: {\r\n                    lines: {\r\n                        show: true\r\n                    }\r\n                }\r\n            },\r\n            markers: {\r\n                strokeColor: baseColor,\r\n                strokeWidth: 3\r\n            }\r\n        };\r\n\r\n        var chart = new ApexCharts(element, options);\r\n        chart.render();\r\n    }\r\n\r\n    var example4 = function () {\r\n        var element = document.getElementById(\"kt_apexcharts_4\");\r\n\r\n        var height = parseInt(KTUtil.css(element, 'height'));\r\n        var labelColor = KTUtil.getCssVariableValue('--bs-gray-500');\r\n        var borderColor = KTUtil.getCssVariableValue('--bs-gray-200');\r\n\r\n        var baseColor = KTUtil.getCssVariableValue('--bs-success');\r\n        var baseLightColor = KTUtil.getCssVariableValue('--bs-light-success');\r\n        var secondaryColor = KTUtil.getCssVariableValue('--bs-warning');\r\n        var secondaryLightColor = KTUtil.getCssVariableValue('--bs-light-warning');\r\n\r\n        if (!element) {\r\n            return;\r\n        }\r\n\r\n        var options = {\r\n            series: [{\r\n                name: 'Net Profit',\r\n                data: [60, 50, 80, 40, 100, 60]\r\n            }, {\r\n                name: 'Revenue',\r\n                data: [70, 60, 110, 40, 50, 70]\r\n            }],\r\n            chart: {\r\n                fontFamily: 'inherit',\r\n                type: 'area',\r\n                height: height,\r\n                toolbar: {\r\n                    show: false\r\n                }\r\n            },\r\n            plotOptions: {},\r\n            legend: {\r\n                show: false\r\n            },\r\n            dataLabels: {\r\n                enabled: false\r\n            },\r\n            fill: {\r\n                type: 'solid',\r\n                opacity: 1\r\n            },\r\n            stroke: {\r\n                curve: 'smooth'\r\n            },\r\n            xaxis: {\r\n                categories: ['Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],\r\n                axisBorder: {\r\n                    show: false,\r\n                },\r\n                axisTicks: {\r\n                    show: false\r\n                },\r\n                labels: {\r\n                    style: {\r\n                        colors: labelColor,\r\n                        fontSize: '12px'\r\n                    }\r\n                },\r\n                crosshairs: {\r\n                    position: 'front',\r\n                    stroke: {\r\n                        color: labelColor,\r\n                        width: 1,\r\n                        dashArray: 3\r\n                    }\r\n                },\r\n                tooltip: {\r\n                    enabled: true,\r\n                    formatter: undefined,\r\n                    offsetY: 0,\r\n                    style: {\r\n                        fontSize: '12px'\r\n                    }\r\n                }\r\n            },\r\n            yaxis: {\r\n                labels: {\r\n                    style: {\r\n                        colors: labelColor,\r\n                        fontSize: '12px'\r\n                    }\r\n                }\r\n            },\r\n            states: {\r\n                normal: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                hover: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                active: {\r\n                    allowMultipleDataPointsSelection: false,\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                }\r\n            },\r\n            tooltip: {\r\n                style: {\r\n                    fontSize: '12px'\r\n                },\r\n                y: {\r\n                    formatter: function (val) {\r\n                        return \"$\" + val + \" thousands\"\r\n                    }\r\n                }\r\n            },\r\n            colors: [baseColor, secondaryColor],\r\n            grid: {\r\n                borderColor: borderColor,\r\n                strokeDashArray: 4,\r\n                yaxis: {\r\n                    lines: {\r\n                        show: true\r\n                    }\r\n                }\r\n            },\r\n            markers: {\r\n                colors: [baseLightColor, secondaryLightColor],\r\n                strokeColor: [baseLightColor, secondaryLightColor],\r\n                strokeWidth: 3\r\n            }\r\n        };\r\n\r\n        var chart = new ApexCharts(element, options);\r\n        chart.render();\r\n    }\r\n\r\n    var example5 = function () {\r\n        var element = document.getElementById(\"kt_apexcharts_5\");\r\n\r\n        var height = parseInt(KTUtil.css(element, 'height'));\r\n        var labelColor = KTUtil.getCssVariableValue('--bs-gray-500');\r\n        var borderColor = KTUtil.getCssVariableValue('--bs-gray-200');\r\n\r\n        var baseColor = KTUtil.getCssVariableValue('--bs-primary');\r\n        var baseLightColor = KTUtil.getCssVariableValue('--bs-light-primary');\r\n        var secondaryColor = KTUtil.getCssVariableValue('--bs-info');\r\n\r\n        if (!element) {\r\n            return;\r\n        }\r\n\r\n        var options = {\r\n            series: [{\r\n                name: 'Net Profit',\r\n                type: 'bar',\r\n                stacked: true,\r\n                data: [40, 50, 65, 70, 50, 30]\r\n            }, {\r\n                name: 'Revenue',\r\n                type: 'bar',\r\n                stacked: true,\r\n                data: [20, 20, 25, 30, 30, 20]\r\n            }, {\r\n                name: 'Expenses',\r\n                type: 'area',\r\n                data: [50, 80, 60, 90, 50, 70]\r\n            }],\r\n            chart: {\r\n                fontFamily: 'inherit',\r\n                stacked: true,\r\n                height: height,\r\n                toolbar: {\r\n                    show: false\r\n                }\r\n            },\r\n            plotOptions: {\r\n                bar: {\r\n                    stacked: true,\r\n                    horizontal: false,\r\n                    endingShape: 'rounded',\r\n                    columnWidth: ['12%']\r\n                },\r\n            },\r\n            legend: {\r\n                show: false\r\n            },\r\n            dataLabels: {\r\n                enabled: false\r\n            },\r\n            stroke: {\r\n                curve: 'smooth',\r\n                show: true,\r\n                width: 2,\r\n                colors: ['transparent']\r\n            },\r\n            xaxis: {\r\n                categories: ['Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],\r\n                axisBorder: {\r\n                    show: false,\r\n                },\r\n                axisTicks: {\r\n                    show: false\r\n                },\r\n                labels: {\r\n                    style: {\r\n                        colors: labelColor,\r\n                        fontSize: '12px'\r\n                    }\r\n                }\r\n            },\r\n            yaxis: {\r\n                max: 120,\r\n                labels: {\r\n                    style: {\r\n                        colors: labelColor,\r\n                        fontSize: '12px'\r\n                    }\r\n                }\r\n            },\r\n            fill: {\r\n                opacity: 1\r\n            },\r\n            states: {\r\n                normal: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                hover: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                active: {\r\n                    allowMultipleDataPointsSelection: false,\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                }\r\n            },\r\n            tooltip: {\r\n                style: {\r\n                    fontSize: '12px'\r\n                },\r\n                y: {\r\n                    formatter: function (val) {\r\n                        return \"$\" + val + \" thousands\"\r\n                    }\r\n                }\r\n            },\r\n            colors: [baseColor, secondaryColor, baseLightColor],\r\n            grid: {\r\n                borderColor: borderColor,\r\n                strokeDashArray: 4,\r\n                yaxis: {\r\n                    lines: {\r\n                        show: true\r\n                    }\r\n                },\r\n                padding: {\r\n                    top: 0,\r\n                    right: 0,\r\n                    bottom: 0,\r\n                    left: 0\r\n                }\r\n            }\r\n        };\r\n\r\n        var chart = new ApexCharts(element, options);\r\n        chart.render();\r\n    }\r\n\r\n    var example6 = function () {\r\n        var element = document.getElementById(\"kt_apexcharts_6\");\r\n\r\n        var height = parseInt(KTUtil.css(element, 'height'));\r\n\r\n        var baseColor = KTUtil.getCssVariableValue('--bs-primary');\r\n        var baseLightColor = KTUtil.getCssVariableValue('--bs-success');\r\n        var secondaryColor = KTUtil.getCssVariableValue('--bs-info');\r\n\r\n        if (!element) {\r\n            return;\r\n        }\r\n\r\n        var options = {\r\n            series: [\r\n                {\r\n                    name: 'Bob',\r\n                    data: [\r\n                        {\r\n                            x: 'Design',\r\n                            y: [\r\n                                new Date('2019-03-05').getTime(),\r\n                                new Date('2019-03-08').getTime()\r\n                            ]\r\n                        },\r\n                        {\r\n                            x: 'Code',\r\n                            y: [\r\n                                new Date('2019-03-02').getTime(),\r\n                                new Date('2019-03-05').getTime()\r\n                            ]\r\n                        },\r\n                        {\r\n                            x: 'Code',\r\n                            y: [\r\n                                new Date('2019-03-05').getTime(),\r\n                                new Date('2019-03-07').getTime()\r\n                            ]\r\n                        },\r\n                        {\r\n                            x: 'Test',\r\n                            y: [\r\n                                new Date('2019-03-03').getTime(),\r\n                                new Date('2019-03-09').getTime()\r\n                            ]\r\n                        },\r\n                        {\r\n                            x: 'Test',\r\n                            y: [\r\n                                new Date('2019-03-08').getTime(),\r\n                                new Date('2019-03-11').getTime()\r\n                            ]\r\n                        },\r\n                        {\r\n                            x: 'Validation',\r\n                            y: [\r\n                                new Date('2019-03-11').getTime(),\r\n                                new Date('2019-03-16').getTime()\r\n                            ]\r\n                        },\r\n                        {\r\n                            x: 'Design',\r\n                            y: [\r\n                                new Date('2019-03-01').getTime(),\r\n                                new Date('2019-03-03').getTime()\r\n                            ]\r\n                        }\r\n                    ]\r\n                },\r\n                {\r\n                    name: 'Joe',\r\n                    data: [\r\n                        {\r\n                            x: 'Design',\r\n                            y: [\r\n                                new Date('2019-03-02').getTime(),\r\n                                new Date('2019-03-05').getTime()\r\n                            ]\r\n                        },\r\n                        {\r\n                            x: 'Test',\r\n                            y: [\r\n                                new Date('2019-03-06').getTime(),\r\n                                new Date('2019-03-16').getTime()\r\n                            ]\r\n                        },\r\n                        {\r\n                            x: 'Code',\r\n                            y: [\r\n                                new Date('2019-03-03').getTime(),\r\n                                new Date('2019-03-07').getTime()\r\n                            ]\r\n                        },\r\n                        {\r\n                            x: 'Deployment',\r\n                            y: [\r\n                                new Date('2019-03-20').getTime(),\r\n                                new Date('2019-03-22').getTime()\r\n                            ]\r\n                        },\r\n                        {\r\n                            x: 'Design',\r\n                            y: [\r\n                                new Date('2019-03-10').getTime(),\r\n                                new Date('2019-03-16').getTime()\r\n                            ]\r\n                        }\r\n                    ]\r\n                },\r\n                {\r\n                    name: 'Dan',\r\n                    data: [\r\n                        {\r\n                            x: 'Code',\r\n                            y: [\r\n                                new Date('2019-03-10').getTime(),\r\n                                new Date('2019-03-17').getTime()\r\n                            ]\r\n                        },\r\n                        {\r\n                            x: 'Validation',\r\n                            y: [\r\n                                new Date('2019-03-05').getTime(),\r\n                                new Date('2019-03-09').getTime()\r\n                            ]\r\n                        },\r\n                    ]\r\n                }\r\n            ],\r\n            chart: {\r\n                type: 'rangeBar',\r\n                fontFamily: 'inherit',\r\n                height: height,\r\n                toolbar: {\r\n                    show: false\r\n                }\r\n            },\r\n            colors: [baseColor, secondaryColor, baseLightColor],\r\n            plotOptions: {\r\n                bar: {\r\n                    horizontal: true,\r\n                    barHeight: '80%'\r\n                }\r\n            },\r\n            xaxis: {\r\n                type: 'datetime'\r\n            },\r\n            stroke: {\r\n                width: 1\r\n            },\r\n            fill: {\r\n                type: 'solid',\r\n                opacity: 1\r\n            },\r\n            legend: {\r\n                position: 'top',\r\n                horizontalAlign: 'left'\r\n            }\r\n        };\r\n\r\n        var chart = new ApexCharts(element, options);\r\n        chart.render();\r\n    }\r\n\r\n    return {\r\n        // Public Functions\r\n        init: function () {\r\n            example1();\r\n            example2();\r\n            example3();\r\n            example4();\r\n            example5();\r\n            example6();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function () {\r\n    KTGeneralApexCharts.init();\r\n});\r\n"], "names": [], "sourceRoot": ""}