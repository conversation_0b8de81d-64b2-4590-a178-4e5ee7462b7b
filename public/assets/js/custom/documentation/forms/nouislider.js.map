{"version": 3, "file": "js/custom/documentation/forms/nouislider.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qCAAqC,YAAY;AACjD;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC,E", "sources": ["webpack://keenthemes/../src/js/custom/documentation/forms/nouislider.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTFormsNouisliderDemos = function() {\r\n    // Private functions\r\n    var _exampleBasic = function() {\r\n        var slider = document.querySelector(\"#kt_slider_basic\");\r\n        var valueMin = document.querySelector(\"#kt_slider_basic_min\");\r\n        var valueMax = document.querySelector(\"#kt_slider_basic_max\");\r\n\r\n        noUiSlider.create(slider, {\r\n            start: [20, 80],\r\n            connect: true,\r\n            range: {\r\n                \"min\": 0,\r\n                \"max\": 100\r\n            }\r\n        });\r\n\r\n        slider.noUiSlider.on(\"update\", function (values, handle) {\r\n            if (handle) {\r\n                valueMax.innerHTML = values[handle];\r\n            } else {\r\n                valueMin.innerHTML = values[handle];\r\n            }\r\n        });\r\n    }\r\n\r\n    var _exampleSizes = function() {\r\n        var slider1 = document.querySelector(\"#kt_slider_sizes_sm\");\r\n        var slider2 = document.querySelector(\"#kt_slider_sizes_default\");\r\n        var slider3 = document.querySelector(\"#kt_slider_sizes_lg\");\r\n\r\n        noUiSlider.create(slider1, {\r\n            start: [20, 80],\r\n            connect: true,\r\n            range: {\r\n                \"min\": 0,\r\n                \"max\": 100\r\n            }\r\n        });\r\n\r\n        noUiSlider.create(slider2, {\r\n            start: [20, 80],\r\n            connect: true,\r\n            range: {\r\n                \"min\": 0,\r\n                \"max\": 100\r\n            }\r\n        });\r\n\r\n        noUiSlider.create(slider3, {\r\n            start: [20, 80],\r\n            connect: true,\r\n            range: {\r\n                \"min\": 0,\r\n                \"max\": 100\r\n            }\r\n        });\r\n    }   \r\n\r\n    var _exampleVertical = function() {\r\n        var slider = document.querySelector(\"#kt_slider_vertical\");\r\n\r\n        noUiSlider.create(slider, {\r\n            start: [60, 160],\r\n            connect: true,\r\n            orientation: \"vertical\",\r\n            range: {\r\n                \"min\": 0,\r\n                \"max\": 200\r\n            }\r\n        });\r\n    }\r\n\r\n    var _exampleTooltip = function() {\r\n        var slider = document.querySelector(\"#kt_slider_tooltip\");\r\n\r\n        noUiSlider.create(slider, {\r\n            start: [20, 80, 120],\r\n            tooltips: [false, wNumb({decimals: 1}), true],\r\n            range: {\r\n                \"min\": 0,\r\n                \"max\": 200\r\n            }\r\n        });        \r\n    }\r\n\r\n    var _exampleSoftLimits = function() {\r\n        var slider = document.querySelector(\"#kt_slider_soft_limits\");\r\n\r\n        noUiSlider.create(slider, {\r\n            start: 50,\r\n            range: {\r\n                min: 0,\r\n                max: 100\r\n            },\r\n            pips: {\r\n                mode: \"values\",\r\n                values: [20, 80],\r\n                density: 4\r\n            }\r\n        });\r\n    } \r\n\r\n    return {\r\n        // Public Functions\r\n        init: function(element) {\r\n            _exampleBasic();\r\n            _exampleSizes();\r\n            _exampleVertical();\r\n            _exampleTooltip();\r\n            _exampleSoftLimits();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function() {\r\n    KTFormsNouisliderDemos.init();\r\n});"], "names": [], "sourceRoot": ""}