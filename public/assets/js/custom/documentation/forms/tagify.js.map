{"version": 3, "file": "js/custom/documentation/forms/tagify.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA8C,cAAc,kEAAkE,mCAAmC,IAAI,4BAA4B;AACjM;AACA;AACA,sCAAsC;AACtC,2HAA2H,YAAY;AACvI;AACA,qEAAqE,cAAc;AACnF;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,qEAAqE,mCAAmC;AACxG;AACA,gDAAgD,YAAY;AAC5D,4CAA4C,cAAc;AAC1D;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,kBAAkB,gCAAgC;AAClD,kBAAkB,iEAAiE;AACnF,kBAAkB,8BAA8B;AAChD,kBAAkB,6BAA6B;AAC/C,kBAAkB,4BAA4B;AAC9C,kBAAkB,4BAA4B;AAC9C,kBAAkB,8BAA8B;AAChD,kBAAkB,6BAA6B;AAC/C,kBAAkB,8BAA8B;AAChD,kBAAkB,gCAAgC;AAClD,kBAAkB,8BAA8B;AAChD,kBAAkB,8BAA8B;AAChD,kBAAkB,4BAA4B;AAC9C,kBAAkB,gCAAgC;AAClD,kBAAkB,4BAA4B;AAC9C,kBAAkB,8BAA8B;AAChD,kBAAkB,4BAA4B;AAC9C,kBAAkB,6BAA6B;AAC/C,kBAAkB,iCAAiC;AACnD,kBAAkB,6BAA6B;AAC/C,kBAAkB,kCAAkC;AACpD,kBAAkB,kCAAkC;AACpD,kBAAkB,6BAA6B;AAC/C,kBAAkB,kCAAkC;AACpD,kBAAkB,gCAAgC;AAClD,kBAAkB,kCAAkC;AACpD,kBAAkB,6BAA6B;AAC/C,kBAAkB,kCAAkC;AACpD,kBAAkB,+BAA+B;AACjD,kBAAkB,8BAA8B;AAChD,kBAAkB,qCAAqC;AACvD,kBAAkB,oCAAoC;AACtD,kBAAkB;AAClB;AACA;AACA;AACA;AACA,cAAc;AACd,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,yFAAyF;AACvG,cAAc,gFAAgF;AAC9F,cAAc,qFAAqF;AACnG,cAAc,wFAAwF;AACtG,cAAc,gGAAgG;AAC9G,cAAc,wFAAwF;AACtG,cAAc,sFAAsF;AACpG,cAAc;AACd;AACA;AACA;AACA;AACA,8BAA8B,iCAAiC;AAC/D;AACA;AACA;AACA,iCAAiC,8BAA8B,EAAE,mCAAmC;AACpG,0BAA0B,4BAA4B;AACtD;AACA;AACA;AACA,oHAAoH,QAAQ,QAAQ,eAAe;AACnJ;AACA,yDAAyD,aAAa;AACtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB;AACvB,8EAA8E,mCAAmC;AACjH;AACA;AACA;AACA,sBAAsB;AACtB;AACA,yHAAyH,QAAQ,QAAQ,eAAe;AACxJ;AACA;AACA;AACA;AACA,kCAAkC,aAAa;AAC/C,gCAAgC,cAAc;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,aAAa;AACb;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC", "sources": ["webpack://keenthemes/../src/js/custom/documentation/forms/tagify.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTFormsTagifyDemos = function () {\r\n    // Private functions\r\n    var example1 = function (element) {\r\n        // The DOM elements you wish to replace with Tagify\r\n        var input1 = document.querySelector(\"#kt_tagify_1\");\r\n        var input2 = document.querySelector(\"#kt_tagify_2\");\r\n\r\n        // Initialize Tagify components on the above inputs\r\n        new Tagify(input1, {\r\n            placeholder: \"Type something\"\r\n        });\r\n        new Tagify(input2, {\r\n            placeholder: \"Type something\"\r\n        });\r\n    }\r\n\r\n    var example2 = function (element) {\r\n        // The DOM elements you wish to replace with Tagify\r\n        var input1 = document.querySelector(\"#kt_tagify_3\");\r\n        var input2 = document.querySelector(\"#kt_tagify_4\");\r\n        var input3 = document.querySelector(\"#kt_tagify_5\");\r\n\r\n        // Initialize Tagify components on the above inputs\r\n        new Tagify(input1);\r\n        new Tagify(input2);\r\n        new Tagify(input3);\r\n    }\r\n\r\n    var example3 = function (element) {\r\n        // The DOM elements you wish to replace with Tagify\r\n        var input1 = document.querySelector(\"#kt_tagify_6\");\r\n        var input2 = document.querySelector(\"#kt_tagify_7\");\r\n\r\n        // Initialize Tagify components on the above inputs\r\n        new Tagify(input1, {\r\n            whitelist: [\"A# .NET\", \"A# (Axiom)\", \"A-0 System\", \"A+\", \"A++\", \"ABAP\", \"ABC\", \"ABC ALGOL\", \"ABSET\", \"ABSYS\", \"ACC\", \"Accent\", \"Ace DASL\", \"ACL2\", \"Avicsoft\", \"ACT-III\", \"Action!\", \"ActionScript\", \"Ada\", \"Adenine\", \"Agda\", \"Agilent VEE\", \"Agora\", \"AIMMS\", \"Alef\", \"ALF\", \"ALGOL 58\", \"ALGOL 60\", \"ALGOL 68\", \"ALGOL W\", \"Alice\", \"Alma-0\", \"AmbientTalk\", \"Amiga E\", \"AMOS\", \"AMPL\", \"Apex (Salesforce.com)\", \"APL\", \"AppleScript\", \"Arc\", \"ARexx\", \"Argus\", \"AspectJ\", \"Assembly language\", \"ATS\", \"Ateji PX\", \"AutoHotkey\", \"Autocoder\", \"AutoIt\", \"AutoLISP / Visual LISP\", \"Averest\", \"AWK\", \"Axum\", \"Active Server Pages\", \"ASP.NET\", \"B\", \"Babbage\", \"Bash\", \"BASIC\", \"bc\", \"BCPL\", \"BeanShell\", \"Batch (Windows/Dos)\", \"Bertrand\", \"BETA\", \"Bigwig\", \"Bistro\", \"BitC\", \"BLISS\", \"Blockly\", \"BlooP\", \"Blue\", \"Boo\", \"Boomerang\", \"Bourne shell (including bash and ksh)\", \"BREW\", \"BPEL\", \"B\", \"C--\", \"C++ – ISO/IEC 14882\", \"C# – ISO/IEC 23270\", \"C/AL\", \"Caché ObjectScript\", \"C Shell\", \"Caml\", \"Cayenne\", \"CDuce\", \"Cecil\", \"Cesil\", \"Céu\", \"Ceylon\", \"CFEngine\", \"CFML\", \"Cg\", \"Ch\", \"Chapel\", \"Charity\", \"Charm\", \"Chef\", \"CHILL\", \"CHIP-8\", \"chomski\", \"ChucK\", \"CICS\", \"Cilk\", \"Citrine (programming language)\", \"CL (IBM)\", \"Claire\", \"Clarion\", \"Clean\", \"Clipper\", \"CLIPS\", \"CLIST\", \"Clojure\", \"CLU\", \"CMS-2\", \"COBOL – ISO/IEC 1989\", \"CobolScript – COBOL Scripting language\", \"Cobra\", \"CODE\", \"CoffeeScript\", \"ColdFusion\", \"COMAL\", \"Combined Programming Language (CPL)\", \"COMIT\", \"Common Intermediate Language (CIL)\", \"Common Lisp (also known as CL)\", \"COMPASS\", \"Component Pascal\", \"Constraint Handling Rules (CHR)\", \"COMTRAN\", \"Converge\", \"Cool\", \"Coq\", \"Coral 66\", \"Corn\", \"CorVision\", \"COWSEL\", \"CPL\", \"CPL\", \"Cryptol\", \"csh\", \"Csound\", \"CSP\", \"CUDA\", \"Curl\", \"Curry\", \"Cybil\", \"Cyclone\", \"Cython\", \"Java\", \"Javascript\", \"M2001\", \"M4\", \"M#\", \"Machine code\", \"MAD (Michigan Algorithm Decoder)\", \"MAD/I\", \"Magik\", \"Magma\", \"make\", \"Maple\", \"MAPPER now part of BIS\", \"MARK-IV now VISION:BUILDER\", \"Mary\", \"MASM Microsoft Assembly x86\", \"MATH-MATIC\", \"Mathematica\", \"MATLAB\", \"Maxima (see also Macsyma)\", \"Max (Max Msp – Graphical Programming Environment)\", \"Maya (MEL)\", \"MDL\", \"Mercury\", \"Mesa\", \"Metafont\", \"Microcode\", \"MicroScript\", \"MIIS\", \"Milk (programming language)\", \"MIMIC\", \"Mirah\", \"Miranda\", \"MIVA Script\", \"ML\", \"Model 204\", \"Modelica\", \"Modula\", \"Modula-2\", \"Modula-3\", \"Mohol\", \"MOO\", \"Mortran\", \"Mouse\", \"MPD\", \"Mathcad\", \"MSIL – deprecated name for CIL\", \"MSL\", \"MUMPS\", \"Mystic Programming L\"],\r\n            maxTags: 10,\r\n            dropdown: {\r\n                maxItems: 20,           // <- mixumum allowed rendered suggestions\r\n                classname: \"tagify__inline__suggestions\", // <- custom classname for this dropdown, so it could be targeted\r\n                enabled: 0,             // <- show suggestions on focus\r\n                closeOnSelect: false    // <- do not hide the suggestions dropdown once an item has been selected\r\n            }\r\n        });\r\n\r\n        new Tagify(input2, {\r\n            whitelist: [\"A# .NET\", \"A# (Axiom)\", \"A-0 System\", \"A+\", \"A++\", \"ABAP\", \"ABC\", \"ABC ALGOL\", \"ABSET\", \"ABSYS\", \"ACC\", \"Accent\", \"Ace DASL\", \"ACL2\", \"Avicsoft\", \"ACT-III\", \"Action!\", \"ActionScript\", \"Ada\", \"Adenine\", \"Agda\", \"Agilent VEE\", \"Agora\", \"AIMMS\", \"Alef\", \"ALF\", \"ALGOL 58\", \"ALGOL 60\", \"ALGOL 68\", \"ALGOL W\", \"Alice\", \"Alma-0\", \"AmbientTalk\", \"Amiga E\", \"AMOS\", \"AMPL\", \"Apex (Salesforce.com)\", \"APL\", \"AppleScript\", \"Arc\", \"ARexx\", \"Argus\", \"AspectJ\", \"Assembly language\", \"ATS\", \"Ateji PX\", \"AutoHotkey\", \"Autocoder\", \"AutoIt\", \"AutoLISP / Visual LISP\", \"Averest\", \"AWK\", \"Axum\", \"Active Server Pages\", \"ASP.NET\", \"B\", \"Babbage\", \"Bash\", \"BASIC\", \"bc\", \"BCPL\", \"BeanShell\", \"Batch (Windows/Dos)\", \"Bertrand\", \"BETA\", \"Bigwig\", \"Bistro\", \"BitC\", \"BLISS\", \"Blockly\", \"BlooP\", \"Blue\", \"Boo\", \"Boomerang\", \"Bourne shell (including bash and ksh)\", \"BREW\", \"BPEL\", \"B\", \"C--\", \"C++ – ISO/IEC 14882\", \"C# – ISO/IEC 23270\", \"C/AL\", \"Caché ObjectScript\", \"C Shell\", \"Caml\", \"Cayenne\", \"CDuce\", \"Cecil\", \"Cesil\", \"Céu\", \"Ceylon\", \"CFEngine\", \"CFML\", \"Cg\", \"Ch\", \"Chapel\", \"Charity\", \"Charm\", \"Chef\", \"CHILL\", \"CHIP-8\", \"chomski\", \"ChucK\", \"CICS\", \"Cilk\", \"Citrine (programming language)\", \"CL (IBM)\", \"Claire\", \"Clarion\", \"Clean\", \"Clipper\", \"CLIPS\", \"CLIST\", \"Clojure\", \"CLU\", \"CMS-2\", \"COBOL – ISO/IEC 1989\", \"CobolScript – COBOL Scripting language\", \"Cobra\", \"CODE\", \"CoffeeScript\", \"ColdFusion\", \"COMAL\", \"Combined Programming Language (CPL)\", \"COMIT\", \"Common Intermediate Language (CIL)\", \"Common Lisp (also known as CL)\", \"COMPASS\", \"Component Pascal\", \"Constraint Handling Rules (CHR)\", \"COMTRAN\", \"Converge\", \"Cool\", \"Coq\", \"Coral 66\", \"Corn\", \"CorVision\", \"COWSEL\", \"CPL\", \"CPL\", \"Cryptol\", \"csh\", \"Csound\", \"CSP\", \"CUDA\", \"Curl\", \"Curry\", \"Cybil\", \"Cyclone\", \"Cython\", \"Java\", \"Javascript\", \"M2001\", \"M4\", \"M#\", \"Machine code\", \"MAD (Michigan Algorithm Decoder)\", \"MAD/I\", \"Magik\", \"Magma\", \"make\", \"Maple\", \"MAPPER now part of BIS\", \"MARK-IV now VISION:BUILDER\", \"Mary\", \"MASM Microsoft Assembly x86\", \"MATH-MATIC\", \"Mathematica\", \"MATLAB\", \"Maxima (see also Macsyma)\", \"Max (Max Msp – Graphical Programming Environment)\", \"Maya (MEL)\", \"MDL\", \"Mercury\", \"Mesa\", \"Metafont\", \"Microcode\", \"MicroScript\", \"MIIS\", \"Milk (programming language)\", \"MIMIC\", \"Mirah\", \"Miranda\", \"MIVA Script\", \"ML\", \"Model 204\", \"Modelica\", \"Modula\", \"Modula-2\", \"Modula-3\", \"Mohol\", \"MOO\", \"Mortran\", \"Mouse\", \"MPD\", \"Mathcad\", \"MSIL – deprecated name for CIL\", \"MSL\", \"MUMPS\", \"Mystic Programming L\"],\r\n            maxTags: 10,\r\n            dropdown: {\r\n                maxItems: 20,           // <- mixumum allowed rendered suggestions\r\n                classname: \"\", // <- custom classname for this dropdown, so it could be targeted\r\n                enabled: 0,             // <- show suggestions on focus\r\n                closeOnSelect: false    // <- do not hide the suggestions dropdown once an item has been selected\r\n            }\r\n        });\r\n    }\r\n\r\n    var example4 = function (element) {\r\n        // The DOM elements you wish to replace with Tagify\r\n        var input1 = document.querySelector(\"#kt_tagify_8\");\r\n\r\n        // Initialize Tagify components on the above inputs\r\n        new Tagify(input1);\r\n    }\r\n\r\n    const exampleCountry = () => {\r\n        var tagify = new Tagify(document.querySelector('#kt_tagify_country'), {\r\n            delimiters: null,\r\n            templates: {\r\n                tag: function (tagData) {\r\n                    const countryPath = hostUrl + 'media/flags/' + tagData.value.toLowerCase().replace(/\\s+/g, '-') + '.svg';\r\n                    try {\r\n                        // _ESCAPE_START_\r\n                        return `<tag title='${tagData.value}' contenteditable='false' spellcheck=\"false\" class='tagify__tag ${tagData.class ? tagData.class : \"\"}' ${this.getAttributes(tagData)}>\r\n                                <x title='remove tag' class='tagify__tag__removeBtn'></x>\r\n                                <div class=\"d-flex align-items-center\">\r\n                                    ${tagData.code ?\r\n                                `<img onerror=\"this.style.visibility = 'hidden'\" class=\"w-25px rounded-circle me-2\" src='${countryPath}' />` : ''\r\n                            }\r\n                                    <span class='tagify__tag-text'>${tagData.value}</span>\r\n                                </div>\r\n                            </tag>`\r\n                        // _ESCAPE_END_\r\n                    }\r\n                    catch (err) { }\r\n                },\r\n\r\n                dropdownItem: function (tagData) {\r\n                    const countryPath = hostUrl + 'media/flags/' + tagData.value.toLowerCase().replace(/\\s+/g, '-') + '.svg';\r\n                    try {\r\n                        // _ESCAPE_START_\r\n                        return `<div class='tagify__dropdown__item ${tagData.class ? tagData.class : \"\"}'>\r\n                                    <img onerror=\"this.style.visibility = 'hidden'\" class=\"w-25px rounded-circle me-2\"\r\n                                         src='${countryPath}' />\r\n                                    <span>${tagData.value}</span>\r\n                                </div>`\r\n                        // _ESCAPE_END_\r\n                    }\r\n                    catch (err) { }\r\n                }\r\n            },\r\n            enforceWhitelist: true,\r\n            whitelist: [\r\n                { value: 'Argentina', code: 'AR' },\r\n                { value: 'Australia', code: 'AU', searchBy: 'beach, sub-tropical' },\r\n                { value: 'Austria', code: 'AT' },\r\n                { value: 'Brazil', code: 'BR' },\r\n                { value: 'China', code: 'CN' },\r\n                { value: 'Egypt', code: 'EG' },\r\n                { value: 'Finland', code: 'FI' },\r\n                { value: 'France', code: 'FR' },\r\n                { value: 'Germany', code: 'DE' },\r\n                { value: 'Hong Kong', code: 'HK' },\r\n                { value: 'Hungary', code: 'HU' },\r\n                { value: 'Iceland', code: 'IS' },\r\n                { value: 'India', code: 'IN' },\r\n                { value: 'Indonesia', code: 'ID' },\r\n                { value: 'Italy', code: 'IT' },\r\n                { value: 'Jamaica', code: 'JM' },\r\n                { value: 'Japan', code: 'JP' },\r\n                { value: 'Jersey', code: 'JE' },\r\n                { value: 'Luxembourg', code: 'LU' },\r\n                { value: 'Mexico', code: 'MX' },\r\n                { value: 'Netherlands', code: 'NL' },\r\n                { value: 'New Zealand', code: 'NZ' },\r\n                { value: 'Norway', code: 'NO' },\r\n                { value: 'Philippines', code: 'PH' },\r\n                { value: 'Singapore', code: 'SG' },\r\n                { value: 'South Korea', code: 'KR' },\r\n                { value: 'Sweden', code: 'SE' },\r\n                { value: 'Switzerland', code: 'CH' },\r\n                { value: 'Thailand', code: 'TH' },\r\n                { value: 'Ukraine', code: 'UA' },\r\n                { value: 'United Kingdom', code: 'GB' },\r\n                { value: 'United States', code: 'US' },\r\n                { value: 'Vietnam', code: 'VN' }\r\n            ],\r\n            dropdown: {\r\n                enabled: 1, // suggest tags after a single character input\r\n                classname: 'extra-properties' // custom class for the suggestions dropdown\r\n            } // map tags' values to this property name, so this property will be the actual value and not the printed value on the screen\r\n        })\r\n\r\n        // add the first 2 tags and makes them readonly\r\n        var tagsToAdd = tagify.settings.whitelist.slice(0, 2);\r\n        tagify.addTags(tagsToAdd);\r\n    }\r\n\r\n    const exampleUsers = () => {\r\n        var inputElm = document.querySelector('#kt_tagify_users');\r\n\r\n        const usersList = [\r\n            { value: 1, name: 'Emma Smith', avatar: 'avatars/150-1.jpg', email: '<EMAIL>' },\r\n            { value: 2, name: 'Max Smith', avatar: 'avatars/150-26.jpg', email: '<EMAIL>' },\r\n            { value: 3, name: 'Sean Bean', avatar: 'avatars/150-4.jpg', email: '<EMAIL>' },\r\n            { value: 4, name: 'Brian Cox', avatar: 'avatars/150-15.jpg', email: '<EMAIL>' },\r\n            { value: 5, name: 'Francis Mitcham', avatar: 'avatars/150-8.jpg', email: '<EMAIL>' },\r\n            { value: 6, name: 'Dan Wilson', avatar: 'avatars/150-6.jpg', email: '<EMAIL>' },\r\n            { value: 7, name: 'Ana Crown', avatar: 'avatars/150-7.jpg', email: '<EMAIL>' },\r\n            { value: 8, name: 'John Miller', avatar: 'avatars/150-17.jpg', email: '<EMAIL>' }\r\n        ];\r\n\r\n        function tagTemplate(tagData) {\r\n            return `\r\n                <tag title=\"${(tagData.title || tagData.email)}\"\r\n                        contenteditable='false'\r\n                        spellcheck='false'\r\n                        tabIndex=\"-1\"\r\n                        class=\"${this.settings.classNames.tag} ${tagData.class ? tagData.class : \"\"}\"\r\n                        ${this.getAttributes(tagData)}>\r\n                    <x title='' class='tagify__tag__removeBtn' role='button' aria-label='remove tag'></x>\r\n                    <div class=\"d-flex align-items-center\">\r\n                        <div class='tagify__tag__avatar-wrap ps-0'>\r\n                            <img onerror=\"this.style.visibility='hidden'\" class=\"rounded-circle w-25px me-2\" src=\"${hostUrl}media/${tagData.avatar}\">\r\n                        </div>\r\n                        <span class='tagify__tag-text'>${tagData.name}</span>\r\n                    </div>\r\n                </tag>\r\n            `\r\n        }\r\n\r\n        function suggestionItemTemplate(tagData) {\r\n            return `\r\n                <div ${this.getAttributes(tagData)}\r\n                    class='tagify__dropdown__item d-flex align-items-center ${tagData.class ? tagData.class : \"\"}'\r\n                    tabindex=\"0\"\r\n                    role=\"option\">\r\n\r\n                    ${tagData.avatar ? `\r\n                            <div class='tagify__dropdown__item__avatar-wrap me-2'>\r\n                                <img onerror=\"this.style.visibility='hidden'\"  class=\"rounded-circle w-50px me-2\" src=\"${hostUrl}media/${tagData.avatar}\">\r\n                            </div>` : ''\r\n                        }\r\n\r\n                    <div class=\"d-flex flex-column\">\r\n                        <strong>${tagData.name}</strong>\r\n                        <span>${tagData.email}</span>\r\n                    </div>\r\n                </div>\r\n            `\r\n        }\r\n\r\n        // initialize Tagify on the above input node reference\r\n        var tagify = new Tagify(inputElm, {\r\n            tagTextProp: 'name', // very important since a custom template is used with this property as text. allows typing a \"value\" or a \"name\" to match input with whitelist\r\n            enforceWhitelist: true,\r\n            skipInvalid: true, // do not remporarily add invalid tags\r\n            dropdown: {\r\n                closeOnSelect: false,\r\n                enabled: 0,\r\n                classname: 'users-list',\r\n                searchKeys: ['name', 'email']  // very important to set by which keys to search for suggesttions when typing\r\n            },\r\n            templates: {\r\n                tag: tagTemplate,\r\n                dropdownItem: suggestionItemTemplate\r\n            },\r\n            whitelist: usersList\r\n        })\r\n\r\n        tagify.on('dropdown:show dropdown:updated', onDropdownShow)\r\n        tagify.on('dropdown:select', onSelectSuggestion)\r\n\r\n        var addAllSuggestionsElm;\r\n\r\n        function onDropdownShow(e) {\r\n            var dropdownContentElm = e.detail.tagify.DOM.dropdown.content;\r\n\r\n            if (tagify.suggestedListItems.length > 1) {\r\n                addAllSuggestionsElm = getAddAllSuggestionsElm();\r\n\r\n                // insert \"addAllSuggestionsElm\" as the first element in the suggestions list\r\n                dropdownContentElm.insertBefore(addAllSuggestionsElm, dropdownContentElm.firstChild)\r\n            }\r\n        }\r\n\r\n        function onSelectSuggestion(e) {\r\n            if (e.detail.elm == addAllSuggestionsElm)\r\n                tagify.dropdown.selectAll.call(tagify);\r\n        }\r\n\r\n        // create a \"add all\" custom suggestion element every time the dropdown changes\r\n        function getAddAllSuggestionsElm() {\r\n            // suggestions items should be based on \"dropdownItem\" template\r\n            return tagify.parseTemplate('dropdownItem', [{\r\n                class: \"addAll\",\r\n                name: \"Add all\",\r\n                email: tagify.settings.whitelist.reduce(function (remainingSuggestions, item) {\r\n                    return tagify.isTagDuplicate(item.value) ? remainingSuggestions : remainingSuggestions + 1\r\n                }, 0) + \" Members\"\r\n            }]\r\n            )\r\n        }\r\n    }\r\n\r\n    return {\r\n        // Public Functions\r\n        init: function () {\r\n            example1();\r\n            example2();\r\n            example3();\r\n            example4();\r\n            exampleCountry();\r\n            exampleUsers();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function () {\r\n    KTFormsTagifyDemos.init();\r\n});\r\n"], "names": [], "sourceRoot": ""}