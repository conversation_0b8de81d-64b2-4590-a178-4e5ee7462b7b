{"version": 3, "file": "js/custom/documentation/forms/formrepeater/nested.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC", "sources": ["webpack://keenthemes/../src/js/custom/documentation/forms/formrepeater/nested.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTFormRepeaterNested = function() {\r\n    // Private functions\r\n    var example1 = function() {\r\n        $('#kt_docs_repeater_nested').repeater({\r\n            // (Required if there is a nested repeater)\r\n            // Specify the configuration of the nested repeaters.\r\n            // Nested configuration follows the same format as the base configuration,\r\n            // supporting options \"defaultValues\", \"show\", \"hide\", etc.\r\n            // Nested repeaters additionally require a \"selector\" field.\r\n            repeaters: [{\r\n                // (Required)\r\n                // Specify the jQuery selector for this nested repeater\r\n                selector: '.inner-repeater',\r\n                show: function () {\r\n                    $(this).slideDown();\r\n                },\r\n    \r\n                hide: function (deleteElement) {\r\n                    $(this).slideUp(deleteElement);\r\n                }\r\n            }],\r\n\r\n            show: function () {\r\n                $(this).slideDown();\r\n            },\r\n\r\n            hide: function (deleteElement) {\r\n                $(this).slideUp(deleteElement);\r\n            }\r\n        });\r\n    }\r\n\r\n    return {\r\n        // Public Functions\r\n        init: function() {\r\n            example1();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function() {\r\n    KTFormRepeaterNested.init();\r\n});\r\n"], "names": [], "sourceRoot": ""}