{"version": 3, "file": "js/custom/documentation/forms/formrepeater/advanced.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC", "sources": ["webpack://keenthemes/../src/js/custom/documentation/forms/formrepeater/advanced.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTFormRepeaterAdvanced = function () {\r\n    // Private functions\r\n    var example1 = function () {\r\n        $('#kt_docs_repeater_advanced').repeater({\r\n            initEmpty: false,\r\n\r\n            defaultValues: {\r\n                'text-input': 'foo'\r\n            },\r\n\r\n            show: function () {\r\n                $(this).slideDown();\r\n\r\n                // Re-init select2\r\n                $(this).find('[data-kt-repeater=\"select2\"]').select2();\r\n\r\n                // Re-init flatpickr\r\n                $(this).find('[data-kt-repeater=\"datepicker\"]').flatpickr();\r\n\r\n                // Re-init tagify\r\n                new Tagify(this.querySelector('[data-kt-repeater=\"tagify\"]'));\r\n            },\r\n\r\n            hide: function (deleteElement) {\r\n                $(this).slideUp(deleteElement);\r\n            },\r\n\r\n            ready: function(){\r\n                // Init select\r\n                $('[data-kt-repeater=\"select2\"]').select2();\r\n\r\n                // Init flatpickr\r\n                $('[data-kt-repeater=\"datepicker\"]').flatpickr();\r\n\r\n                // Init Tagify\r\n                new Tagify(document.querySelector('[data-kt-repeater=\"tagify\"]'));\r\n            }\r\n        });        \r\n    }\r\n\r\n    return {\r\n        // Public Functions\r\n        init: function () {\r\n            example1();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function () {\r\n    KTFormRepeaterAdvanced.init();\r\n});\r\n"], "names": [], "sourceRoot": ""}