{"version": 3, "file": "js/custom/documentation/forms/bootstrap-maxlength.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC", "sources": ["webpack://keenthemes/../src/js/custom/documentation/forms/bootstrap-maxlength.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTFormsMaxlengthDemos = function () {\r\n    // Private functions\r\n    var exampleBasic = function () {\r\n        // minimum setup\r\n        $('#kt_docs_maxlength_basic').maxlength({\r\n            warningClass: \"badge badge-primary\",\r\n            limitReachedClass: \"badge badge-success\"\r\n        });\r\n    }\r\n\r\n    var exampleThreshold = function () {\r\n        // Threshold setup\r\n        $('#kt_docs_maxlength_threshold').maxlength({\r\n            threshold: 20,\r\n            warningClass: \"badge badge-primary\",\r\n            limitReachedClass: \"badge badge-success\"\r\n        });\r\n    }\r\n\r\n    var exampleAlwaysShow = function () {\r\n        // Always show setup\r\n        $('#kt_docs_maxlength_always_show').maxlength({\r\n            alwaysShow: true,\r\n            threshold: 20,\r\n            warningClass: \"badge badge-danger\",\r\n            limitReachedClass: \"badge badge-info\"\r\n        });\r\n    }\r\n\r\n    var exampleCustomText = function () {\r\n        // Always show setup\r\n        $('#kt_docs_maxlength_custom_text').maxlength({\r\n            threshold: 20,\r\n            warningClass: \"badge badge-danger\",\r\n            limitReachedClass: \"badge badge-success\",\r\n            separator: ' of ',\r\n            preText: 'You have ',\r\n            postText: ' chars remaining.',\r\n            validate: true\r\n        });\r\n    }\r\n\r\n    var exampleTextarea = function () {\r\n        // Textarea setup\r\n        $('#kt_docs_maxlength_textarea').maxlength({\r\n            warningClass: \"badge badge-primary\",\r\n            limitReachedClass: \"badge badge-success\"\r\n        });\r\n    }\r\n\r\n    var examplePosition = function () {\r\n        // Position setup\r\n        $('#kt_docs_maxlength_position_top_left').maxlength({\r\n            placement: 'top-left',\r\n            warningClass: \"badge badge-danger\",\r\n            limitReachedClass: \"badge badge-primary\"\r\n        });\r\n\r\n        $('#kt_docs_maxlength_position_top_right').maxlength({\r\n            placement: 'top-right',\r\n            warningClass: \"badge badge-success\",\r\n            limitReachedClass: \"badge badge-danger\"\r\n        });\r\n\r\n        $('#kt_docs_maxlength_position_bottom_left').maxlength({\r\n            placement: 'bottom-left',\r\n            warningClass: \"badge badge-info\",\r\n            limitReachedClass: \"badge badge-warning\"\r\n        });\r\n\r\n        $('#kt_docs_maxlength_position_bottom_right').maxlength({\r\n            placement: 'bottom-right',\r\n            warningClass: \"badge badge-primary\",\r\n            limitReachedClass: \"badge badge-success\"\r\n        });\r\n    }\r\n\r\n    return {\r\n        // Public Functions\r\n        init: function () {\r\n            exampleBasic();\r\n            exampleThreshold();\r\n            exampleAlwaysShow();\r\n            exampleCustomText();\r\n            exampleTextarea();\r\n            examplePosition();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function () {\r\n    KTFormsMaxlengthDemos.init();\r\n});\r\n"], "names": [], "sourceRoot": ""}