{"version": 3, "file": "js/custom/documentation/forms/select2.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC", "sources": ["webpack://keenthemes/../src/js/custom/documentation/forms/select2.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTFormsSelect2Demo = function () {\r\n    // Private functions\r\n    var exampleCountry = function () {\r\n        // Format options\r\n        const format = (item) => {\r\n            if (!item.id) {\r\n                return item.text;\r\n            }\r\n\r\n            var url = hostUrl + 'media/' + item.element.getAttribute('data-kt-select2-country');\r\n            var img = $(\"<img>\", {\r\n                class: \"rounded-circle me-2\",\r\n                width: 26,\r\n                src: url\r\n            });\r\n            var span = $(\"<span>\", {\r\n                text: \" \" + item.text\r\n            });\r\n            span.prepend(img);\r\n            return span;\r\n        }\r\n\r\n        // Init Select2 --- more info: https://select2.org/\r\n        $('#kt_docs_select2_country').select2({\r\n            templateResult: function (item) {\r\n                return format(item);\r\n            }\r\n        });\r\n    }\r\n\r\n    const exampleUsers = function () {\r\n        // Format options\r\n        const format = (item) => {\r\n            if (!item.id) {\r\n                return item.text;\r\n            }\r\n\r\n            var url = hostUrl + 'media/' + item.element.getAttribute('data-kt-select2-user');\r\n            var img = $(\"<img>\", {\r\n                class: \"rounded-circle me-2\",\r\n                width: 26,\r\n                src: url\r\n            });\r\n            var span = $(\"<span>\", {\r\n                text: \" \" + item.text\r\n            });\r\n            span.prepend(img);\r\n            return span;\r\n        }\r\n\r\n        // Init Select2 --- more info: https://select2.org/\r\n        $('#kt_docs_select2_users').select2({\r\n            templateResult: function (item) {\r\n                return format(item);\r\n            }\r\n        });\r\n    }\r\n\r\n    return {\r\n        // Public Functions\r\n        init: function () {\r\n            exampleCountry();\r\n            exampleUsers();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function () {\r\n    KTFormsSelect2Demo.init();\r\n});\r\n"], "names": [], "sourceRoot": ""}