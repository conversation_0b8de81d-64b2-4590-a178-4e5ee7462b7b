{"version": 3, "file": "js/custom/documentation/forms/inputmask.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,qBAAqB,KAAK,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI;AACjF;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,yDAAyD,EAAE;AAC3D;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC", "sources": ["webpack://keenthemes/../src/js/custom/documentation/forms/inputmask.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTFormsInputmaskDemos = function() {\r\n    // Private functions\r\n    var _examples = function() {\r\n        // Date\r\n        Inputmask({\r\n            \"mask\" : \"99/99/9999\"\r\n        }).mask(\"#kt_inputmask_1\");\r\n\r\n        // Phone \r\n        Inputmask({\r\n            \"mask\" : \"(*************\"\r\n        }).mask(\"#kt_inputmask_2\");\r\n\r\n        // Placeholder \r\n        Inputmask({\r\n            \"mask\" : \"(*************\",\r\n            \"placeholder\": \"(*************\",\r\n        }).mask(\"#kt_inputmask_3\");\r\n\r\n        // Repeating \r\n        Inputmask({\r\n            \"mask\": \"9\",\r\n            \"repeat\": 10,\r\n            \"greedy\": false\r\n        }).mask(\"#kt_inputmask_4\");\r\n\r\n        // Right aligned \r\n        Inputmask(\"decimal\", {\r\n            \"rightAlignNumerics\": false\r\n        }).mask(\"#kt_inputmask_5\");\r\n\r\n        // Currency\r\n        Inputmask(\"€ 999.999.999,99\", {\r\n            \"numericInput\": true\r\n        }).mask(\"#kt_inputmask_6\");\r\n\r\n        // Ip address\r\n        Inputmask({\r\n            \"mask\": \"999.999.999.999\"\r\n        }).mask(\"#kt_inputmask_7\");\r\n\r\n        // Email address\r\n        Inputmask({\r\n            mask: \"*{1,20}[.*{1,20}][.*{1,20}][.*{1,20}]@*{1,20}[.*{2,6}][.*{1,2}]\",\r\n            greedy: false,\r\n            onBeforePaste: function (pastedValue, opts) {\r\n                pastedValue = pastedValue.toLowerCase();\r\n                return pastedValue.replace(\"mailto:\", \"\");\r\n            },\r\n            definitions: {\r\n                \"*\": {\r\n                    validator: '[0-9A-Za-z!#$%&\"*+/=?^_`{|}~\\-]',\r\n                    cardinality: 1,\r\n                    casing: \"lower\"\r\n                }\r\n            }\r\n        }).mask(\"#kt_inputmask_8\");\r\n    }\r\n\r\n    return {\r\n        // Public Functions\r\n        init: function(element) {\r\n            _examples();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function() {\r\n    KTFormsInputmaskDemos.init();\r\n});\r\n"], "names": [], "sourceRoot": ""}