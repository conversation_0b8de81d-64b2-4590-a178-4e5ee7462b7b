{"version": 3, "file": "js/custom/documentation/forms/multiselectsplitter.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC", "sources": ["webpack://keenthemes/../src/js/custom/documentation/forms/multiselectsplitter.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTFormsMultiselectsplitterDemos = function() {\r\n    // Private functions\r\n    var example1 = function() {\r\n        $(\"#kt_multiselectsplitter_example_1\").multiselectsplitter();\r\n    }\r\n\r\n    var example2 = function() {\r\n        $('#kt_multiselectsplitter_example_2').multiselectsplitter({\r\n    \t\tselectSize: 7,\r\n            clearOnFirstChange: true,\r\n    \t\tgroupCounter: true\r\n        });\r\n    }\r\n\r\n    var example3 = function() {\r\n        $('#kt_multiselectsplitter_example_3').multiselectsplitter({\r\n    \t\tgroupCounter: true,\r\n            maximumSelected: 2\r\n        });\r\n    }\r\n\r\n    var example4 = function() {\r\n        $('#kt_multiselectsplitter_example_4').multiselectsplitter({\r\n    \t\tgroupCounter: true,\r\n            maximumSelected: 3,\r\n            onlySameGroup: true\r\n        });\r\n    }\r\n\r\n    var example5 = function() {\r\n        $('#kt_multiselectsplitter_example_5').multiselectsplitter({\r\n    \t\tsize: 6,\r\n    \t\tgroupCounter: true,\r\n            maximumSelected: 2,\r\n            maximumAlert: function(maximumSelected) {\r\n                alert(\"You choose \" + ( maximumSelected + 1 ) + \" options. Are you crazy ?\");\r\n            },\r\n            createFirstSelect: function (label, $originalSelect) { \r\n                return \"<option class=\\\"text-success\\\">prefix - \" + label + \"</option>\";\r\n            },\r\n            createSecondSelect: function (label, $firstSelect) { \r\n                return \"<option class=\\\"text-danger\\\"> ??? </option>\";\r\n            }\r\n        });\r\n    }\r\n\r\n    return {\r\n        // Public Functions\r\n        init: function() {\r\n            example1();\r\n            example2();\r\n            example3();\r\n            example4();\r\n            example5();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function() {\r\n    KTFormsMultiselectsplitterDemos.init();\r\n});\r\n"], "names": [], "sourceRoot": ""}