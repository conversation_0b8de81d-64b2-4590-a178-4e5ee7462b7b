{"version": 3, "file": "js/custom/documentation/forms/recaptcha.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC", "sources": ["webpack://keenthemes/../src/js/custom/documentation/forms/recaptcha.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTFormsGoogleRecaptchaDemos = function () {\r\n    // Private functions\r\n    var example = function (element) {\r\n        document.querySelector(\"#kt_form_submit_button\").addEventListener(\"click\", function (e) {\r\n            e.preventDefault();\r\n\r\n            grecaptcha.ready(function () {\r\n                if (grecaptcha.getResponse() === \"\") {\r\n                    alert(\"Please validate the Google reCaptcha.\");\r\n                } else {\r\n                    alert(\"Successful validation! Now you can submit this form to your server side processing.\");\r\n                }\r\n            });\r\n        });\r\n    }\r\n\r\n    return {\r\n        // Public Functions\r\n        init: function (element) {\r\n            example();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function () {\r\n    KTFormsGoogleRecaptchaDemos.init();\r\n});\r\n"], "names": [], "sourceRoot": ""}