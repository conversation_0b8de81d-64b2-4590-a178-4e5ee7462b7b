{"version": 3, "file": "js/custom/documentation/forms/dropzonejs.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4CAA4C;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,+FAA+F;AAC/F;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4CAA4C;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC", "sources": ["webpack://keenthemes/../src/js/custom/documentation/forms/dropzonejs.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTFormsDropzoneJSDemos = function () {\r\n    // Private functions\r\n    var exampleBasic = function () {\r\n        // For more info about Dropzone plugin visit:  https://www.dropzonejs.com/#usage\r\n        var myDropzone = new Dropzone(\"#kt_dropzonejs_example_1\", {\r\n            url: \"https://keenthemes.com/scripts/void.php\", // Set the url for your upload script location\r\n            paramName: \"file\", // The name that will be used to transfer the file\r\n            maxFiles: 10,\r\n            maxFilesize: 10, // MB\r\n            addRemoveLinks: true,\r\n            accept: function (file, done) {\r\n                if (file.name == \"wow.jpg\") {\r\n                    done(\"Nah<PERSON>, you don't.\");\r\n                } else {\r\n                    done();\r\n                }\r\n            }\r\n        });\r\n    }\r\n\r\n    var exampleQueue = function () {\r\n        // set the dropzone container id\r\n        const id = \"#kt_dropzonejs_example_2\";\r\n        const dropzone = document.querySelector(id);\r\n\r\n        // set the preview element template\r\n        var previewNode = dropzone.querySelector(\".dropzone-item\");\r\n        previewNode.id = \"\";\r\n        var previewTemplate = previewNode.parentNode.innerHTML;\r\n        previewNode.parentNode.removeChild(previewNode);\r\n\r\n        var myDropzone = new Dropzone(id, { // Make the whole body a dropzone\r\n            url: \"https://preview.keenthemes.com/api/dropzone/void.php\", // Set the url for your upload script location\r\n            parallelUploads: 20,\r\n            previewTemplate: previewTemplate,\r\n            maxFilesize: 1, // Max filesize in MB\r\n            autoQueue: false, // Make sure the files aren't queued until manually added\r\n            previewsContainer: id + \" .dropzone-items\", // Define the container to display the previews\r\n            clickable: id + \" .dropzone-select\" // Define the element that should be used as click trigger to select files.\r\n        });\r\n\r\n        myDropzone.on(\"addedfile\", function (file) {\r\n            // Hookup the start button\r\n            file.previewElement.querySelector(id + \" .dropzone-start\").onclick = function () { myDropzone.enqueueFile(file); };\r\n            const dropzoneItems = dropzone.querySelectorAll('.dropzone-item');\r\n            dropzoneItems.forEach(dropzoneItem => {\r\n                dropzoneItem.style.display = '';\r\n            });\r\n            dropzone.querySelector('.dropzone-upload').style.display = \"inline-block\";\r\n            dropzone.querySelector('.dropzone-remove-all').style.display = \"inline-block\";\r\n        });\r\n\r\n        // Update the total progress bar\r\n        myDropzone.on(\"totaluploadprogress\", function (progress) {\r\n            const progressBars = dropzone.querySelectorAll('.progress-bar');\r\n            progressBars.forEach(progressBar => {\r\n                progressBar.style.width = progress + \"%\";\r\n            });\r\n        });\r\n\r\n        myDropzone.on(\"sending\", function (file) {\r\n            // Show the total progress bar when upload starts\r\n            const progressBars = dropzone.querySelectorAll('.progress-bar');\r\n            progressBars.forEach(progressBar => {\r\n                progressBar.style.opacity = \"1\";\r\n            });\r\n            // And disable the start button\r\n            file.previewElement.querySelector(id + \" .dropzone-start\").setAttribute(\"disabled\", \"disabled\");\r\n        });\r\n\r\n        // Hide the total progress bar when nothing's uploading anymore\r\n        myDropzone.on(\"complete\", function (progress) {\r\n            const progressBars = dropzone.querySelectorAll('.dz-complete');\r\n\r\n            setTimeout(function () {\r\n                progressBars.forEach(progressBar => {\r\n                    progressBar.querySelector('.progress-bar').style.opacity = \"0\";\r\n                    progressBar.querySelector('.progress').style.opacity = \"0\";\r\n                    progressBar.querySelector('.dropzone-start').style.opacity = \"0\";\r\n                });\r\n            }, 300);\r\n        });\r\n\r\n        // Setup the buttons for all transfers\r\n        dropzone.querySelector(\".dropzone-upload\").addEventListener('click', function () {\r\n            myDropzone.enqueueFiles(myDropzone.getFilesWithStatus(Dropzone.ADDED));\r\n        });\r\n\r\n        // Setup the button for remove all files\r\n        dropzone.querySelector(\".dropzone-remove-all\").addEventListener('click', function () {\r\n            dropzone.querySelector('.dropzone-upload').style.display = \"none\";\r\n            dropzone.querySelector('.dropzone-remove-all').style.display = \"none\";\r\n            myDropzone.removeAllFiles(true);\r\n        });\r\n\r\n        // On all files completed upload\r\n        myDropzone.on(\"queuecomplete\", function (progress) {\r\n            const uploadIcons = dropzone.querySelectorAll('.dropzone-upload');\r\n            uploadIcons.forEach(uploadIcon => {\r\n                uploadIcon.style.display = \"none\";\r\n            });\r\n        });\r\n\r\n        // On all files removed\r\n        myDropzone.on(\"removedfile\", function (file) {\r\n            if (myDropzone.files.length < 1) {\r\n                dropzone.querySelector('.dropzone-upload').style.display = \"none\";\r\n                dropzone.querySelector('.dropzone-remove-all').style.display = \"none\";\r\n            }\r\n        });\r\n    }\r\n\r\n    var exampleQueueAutoUpload = function () {\r\n        // set the dropzone container id\r\n        const id = \"#kt_dropzonejs_example_3\";\r\n        const dropzone = document.querySelector(id);\r\n\r\n        // set the preview element template\r\n        var previewNode = dropzone.querySelector(\".dropzone-item\");\r\n        previewNode.id = \"\";\r\n        var previewTemplate = previewNode.parentNode.innerHTML;\r\n        previewNode.parentNode.removeChild(previewNode);\r\n\r\n        var myDropzone = new Dropzone(id, { // Make the whole body a dropzone\r\n            url: \"https://preview.keenthemes.com/api/dropzone/void.php\", // Set the url for your upload script location\r\n            parallelUploads: 20,\r\n            maxFilesize: 1, // Max filesize in MB\r\n            previewTemplate: previewTemplate,\r\n            previewsContainer: id + \" .dropzone-items\", // Define the container to display the previews\r\n            clickable: id + \" .dropzone-select\" // Define the element that should be used as click trigger to select files.\r\n        });\r\n\r\n\r\n        myDropzone.on(\"addedfile\", function (file) {\r\n            // Hookup the start button\r\n            const dropzoneItems = dropzone.querySelectorAll('.dropzone-item');\r\n            dropzoneItems.forEach(dropzoneItem => {\r\n                dropzoneItem.style.display = '';\r\n            });\r\n        });\r\n\r\n        // Update the total progress bar\r\n        myDropzone.on(\"totaluploadprogress\", function (progress) {\r\n            const progressBars = dropzone.querySelectorAll('.progress-bar');\r\n            progressBars.forEach(progressBar => {\r\n                progressBar.style.width = progress + \"%\";\r\n            });\r\n        });\r\n\r\n        myDropzone.on(\"sending\", function (file) {\r\n            // Show the total progress bar when upload starts\r\n            const progressBars = dropzone.querySelectorAll('.progress-bar');\r\n            progressBars.forEach(progressBar => {\r\n                progressBar.style.opacity = \"1\";\r\n            });\r\n        });\r\n\r\n        // Hide the total progress bar when nothing\"s uploading anymore\r\n        myDropzone.on(\"complete\", function (progress) {\r\n            const progressBars = dropzone.querySelectorAll('.dz-complete');\r\n\r\n            setTimeout(function () {\r\n                progressBars.forEach(progressBar => {\r\n                    progressBar.querySelector('.progress-bar').style.opacity = \"0\";\r\n                    progressBar.querySelector('.progress').style.opacity = \"0\";\r\n                });\r\n            }, 300);\r\n        });\r\n    }\r\n\r\n    return {\r\n        // Public Functions\r\n        init: function (element) {\r\n            exampleBasic();\r\n            exampleQueue();\r\n            exampleQueueAutoUpload();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function () {\r\n    KTFormsDropzoneJSDemos.init();\r\n});\r\n"], "names": [], "sourceRoot": ""}