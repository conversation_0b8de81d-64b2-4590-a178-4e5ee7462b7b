{"version": 3, "file": "js/custom/documentation/forms/password-meter.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC", "sources": ["webpack://keenthemes/../src/js/custom/documentation/forms/password-meter.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTGeneralPasswordMeterDemos = function() {\r\n    // Private functions\r\n    var _showScore = function() {\r\n        // Select show score button\r\n        const showScoreButton = document.getElementById('kt_password_meter_example_show_score');  \r\n\r\n        // Get password meter instance\r\n        const passwordMeterElement = document.querySelector(\"#kt_password_meter_example\");\r\n        const passwordMeter = KTPasswordMeter.getInstance(passwordMeterElement);\r\n\r\n        // Handle show score button click\r\n        showScoreButton.addEventListener('click', e => {\r\n            // Get password score\r\n            const score = passwordMeter.getScore();\r\n\r\n            // Show popup confirmation \r\n            Swal.fire({\r\n                text: \"Current Password Score: \" + score,\r\n                icon: \"success\",\r\n                buttonsStyling: false,\r\n                confirmButtonText: \"Ok, got it!\",\r\n                customClass: {\r\n                    confirmButton: \"btn btn-primary\"\r\n                }\r\n            });\r\n        });\r\n    }\r\n\r\n    return {\r\n        // Public Functions\r\n        init: function() {\r\n            _showScore();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function() {\r\n    KTGeneralPasswordMeterDemos.init();\r\n});\r\n"], "names": [], "sourceRoot": ""}