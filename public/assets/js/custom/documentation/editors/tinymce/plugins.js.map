{"version": 3, "file": "js/custom/documentation/editors/tinymce/plugins.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC", "sources": ["webpack://keenthemes/../src/js/custom/documentation/editors/tinymce/plugins.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTFormsTinyMCEPlugins = function() {\r\n    // Private functions\r\n    var examplePlugins = function() {\r\n        tinymce.init({\r\n            selector: '#kt_docs_tinymce_plugins',\r\n            toolbar: 'advlist | autolink | link image | lists charmap | print preview',\r\n            plugins : 'advlist autolink link image lists charmap print preview'\r\n        });\r\n    }\r\n\r\n    return {\r\n        // Public Functions\r\n        init: function() {\r\n            examplePlugins();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function() {\r\n    KTFormsTinyMCEPlugins.init();\r\n});\r\n"], "names": [], "sourceRoot": ""}