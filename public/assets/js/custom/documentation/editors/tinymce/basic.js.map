{"version": 3, "file": "js/custom/documentation/editors/tinymce/basic.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA,uBAAuB;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC", "sources": ["webpack://keenthemes/../src/js/custom/documentation/editors/tinymce/basic.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTFormsTinyMCEBasic = function() {\r\n    // Private functions\r\n    var exampleBasic = function() {\r\n        var options = {selector: '#kt_docs_tinymce_basic'};\r\n        \r\n        if (KTApp.isDarkMode()) {\r\n            options['skin'] = 'oxide-dark';\r\n            options['content_css'] = 'dark';\r\n        }\r\n        \r\n        tinymce.init(options);\r\n    }\r\n\r\n    return {\r\n        // Public Functions\r\n        init: function() {\r\n            exampleBasic();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function() {\r\n    KTFormsTinyMCEBasic.init();\r\n});\r\n"], "names": [], "sourceRoot": ""}