{"version": 3, "file": "js/custom/documentation/editors/quill/basic.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC", "sources": ["webpack://keenthemes/../src/js/custom/documentation/editors/quill/basic.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTFormsQuillBasic = function() {\r\n    // Private functions\r\n    var exampleBasic = function() {\r\n        var quill = new Quill('#kt_docs_quill_basic', {\r\n            modules: {\r\n                toolbar: [\r\n                    [{\r\n                        header: [1, 2, false]\r\n                    }],\r\n                    ['bold', 'italic', 'underline'],\r\n                    ['image', 'code-block']\r\n                ]\r\n            },\r\n            placeholder: 'Type your text here...',\r\n            theme: 'snow' // or 'bubble'\r\n        });\r\n    }\r\n\r\n    return {\r\n        // Public Functions\r\n        init: function() {\r\n            exampleBasic();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function() {\r\n    KTFormsQuillBasic.init();\r\n});\r\n"], "names": [], "sourceRoot": ""}