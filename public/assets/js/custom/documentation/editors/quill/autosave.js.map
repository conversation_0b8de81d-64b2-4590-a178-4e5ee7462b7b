{"version": 3, "file": "js/custom/documentation/editors/quill/autosave.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC", "sources": ["webpack://keenthemes/../src/js/custom/documentation/editors/quill/autosave.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTFormsQuillAutosave = function () {\r\n    // Private functions\r\n    var exampleAutosave = function () {\r\n        var Delta = Quill.import('delta');\r\n        var quill = new Quill('#kt_docs_quill_autosave', {\r\n            modules: {\r\n                toolbar: true\r\n            },\r\n            placeholder: 'Type your text here...',\r\n            theme: 'snow'\r\n        });\r\n\r\n        // Store accumulated changes\r\n        var change = new Delta();\r\n        quill.on('text-change', function (delta) {\r\n            change = change.compose(delta);\r\n        });\r\n\r\n        // Save periodically\r\n        setInterval(function () {\r\n            if (change.length() > 0) {\r\n                console.log('Saving changes', change);\r\n                /*\r\n                Send partial changes\r\n                $.post('/your-endpoint', {\r\n                partial: JSON.stringify(change)\r\n                });\r\n\r\n                Send entire document\r\n                $.post('/your-endpoint', {\r\n                doc: JSON.stringify(quill.getContents())\r\n                });\r\n                */\r\n                change = new Delta();\r\n            }\r\n        }, 5 * 1000);\r\n\r\n        // Check for unsaved data\r\n        window.onbeforeunload = function () {\r\n            if (change.length() > 0) {\r\n                return 'There are unsaved changes. Are you sure you want to leave?';\r\n            }\r\n        }\r\n    }\r\n\r\n    return {\r\n        // Public Functions\r\n        init: function () {\r\n            exampleAutosave();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function () {\r\n    KTFormsQuillAutosave.init();\r\n});\r\n"], "names": [], "sourceRoot": ""}