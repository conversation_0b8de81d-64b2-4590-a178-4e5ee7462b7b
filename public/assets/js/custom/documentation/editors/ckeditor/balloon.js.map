{"version": 3, "file": "js/custom/documentation/editors/ckeditor/balloon.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC", "sources": ["webpack://keenthemes/../src/js/custom/documentation/editors/ckeditor/balloon.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTFormsCKEditorBalloon = function () {\r\n    // Private functions\r\n    var exampleBalloon = function () {\r\n        BalloonEditor\r\n            .create(document.querySelector('#kt_docs_ckeditor_balloon'))\r\n            .then(editor => {\r\n                console.log(editor);\r\n            })\r\n            .catch(error => {\r\n                console.error(error);\r\n            });\r\n    }\r\n\r\n    return {\r\n        // Public Functions\r\n        init: function () {\r\n            exampleBalloon();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function () {\r\n    KTFormsCKEditorBalloon.init();\r\n});\r\n"], "names": [], "sourceRoot": ""}