{"version": 3, "file": "js/custom/documentation/editors/ckeditor/document.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC", "sources": ["webpack://keenthemes/../src/js/custom/documentation/editors/ckeditor/document.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTFormsCKEditorDocument = function () {\r\n    // Private functions\r\n    var exampleDocument = function () {\r\n        DecoupledEditor\r\n            .create(document.querySelector('#kt_docs_ckeditor_document'))\r\n            .then(editor => {\r\n                const toolbarContainer = document.querySelector('#kt_docs_ckeditor_document_toolbar');\r\n\r\n                toolbarContainer.appendChild(editor.ui.view.toolbar.element);\r\n            })\r\n            .catch(error => {\r\n                console.error(error);\r\n            });\r\n    }\r\n\r\n    return {\r\n        // Public Functions\r\n        init: function () {\r\n            exampleDocument();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function () {\r\n    KTFormsCKEditorDocument.init();\r\n});\r\n"], "names": [], "sourceRoot": ""}