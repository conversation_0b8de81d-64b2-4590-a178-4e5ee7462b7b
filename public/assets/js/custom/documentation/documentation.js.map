{"version": 3, "file": "js/custom/documentation/documentation.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B,qBAAqB;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC,E", "sources": ["webpack://keenthemes/../src/js/custom/documentation/documentation.js"], "sourcesContent": ["\"use strict\";\r\n\r\nvar KTLayoutDocumentation = function() {\r\n    var _init = function(element) {\r\n        var elements = element;\r\n\r\n        if ( typeof elements === 'undefined' ) {\r\n            elements = document.querySelectorAll('.highlight');\r\n        }\r\n\r\n        if ( elements && elements.length > 0 ) {\r\n            for ( var i = 0; i < elements.length; ++i ) {\r\n                var highlight = elements[i];\r\n                var copy = highlight.querySelector('.highlight-copy');\r\n\r\n                if ( copy ) {\r\n                    var clipboard = new ClipboardJS(copy, {\r\n                        target: function(trigger) {\r\n                            var highlight = trigger.closest('.highlight');\r\n                            var el = highlight.querySelector('.tab-pane.active');\r\n\r\n                            if ( el == null ) {\r\n                                el = highlight.querySelector('.highlight-code');\r\n                            }\r\n\r\n                            return el;\r\n                        }\r\n                    });\r\n\r\n                    clipboard.on('success', function(e) {\r\n                        var caption = e.trigger.innerHTML;\r\n\r\n                        e.trigger.innerHTML = 'copied';\r\n                        e.clearSelection();\r\n\r\n                        setTimeout(function() {\r\n                            e.trigger.innerHTML = caption;\r\n                        }, 2000);\r\n                    });\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    return {\r\n        init: function(element) {\r\n            _init(element);\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function() {\r\n    KTLayoutDocumentation.init();\r\n});"], "names": [], "sourceRoot": ""}