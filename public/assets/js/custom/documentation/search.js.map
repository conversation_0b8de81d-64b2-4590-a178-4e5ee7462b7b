{"version": 3, "file": "js/custom/documentation/search.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC,E", "sources": ["webpack://keenthemes/../src/js/custom/documentation/search.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTDocsSearch = function() {\r\n    // Private variables\r\n    var element;\r\n    var formElement;\r\n    var mainElement;\r\n    var resultsElement;\r\n    var wrapperElement;\r\n    var emptyElement;    \r\n    var searchObject;\r\n\r\n    // Private functions\r\n    var processs = function(search) {\r\n        var results = 0;\r\n\r\n        // Elements\r\n        var searchable = [].slice.call(resultsElement.querySelectorAll('[data-kt-searchable=\"true\"]'));\r\n\r\n        // Match elements\r\n        searchable.map(function (element) {  \r\n            var query = searchObject.getQuery();\r\n\r\n            if (element.innerText.toLowerCase().indexOf(query.toLowerCase()) !== -1) {\r\n                element.classList.remove('d-none');\r\n                results++;\r\n            } else {\r\n                element.classList.add('d-none');\r\n            }\r\n        });\r\n        \r\n        // Hide recently viewed\r\n        mainElement.classList.add('d-none');\r\n\r\n        if (results === 0) {\r\n            // Hide results\r\n            resultsElement.classList.add('d-none');\r\n            // Show empty message \r\n            emptyElement.classList.remove('d-none');\r\n        } else {\r\n            // Show results\r\n            resultsElement.classList.remove('d-none');\r\n            // Hide empty message \r\n            emptyElement.classList.add('d-none');\r\n        }                  \r\n\r\n        // Complete search\r\n        search.complete();\r\n    }\r\n\r\n    var clear = function(search) {\r\n        // Show recently viewed\r\n        mainElement.classList.remove('d-none');\r\n        // Hide results\r\n        resultsElement.classList.add('d-none');\r\n        // Hide empty message \r\n        emptyElement.classList.add('d-none');\r\n    }    \r\n\r\n    // Public methods\r\n\treturn {\r\n\t\tinit: function() {\r\n            // Elements\r\n            element = document.querySelector('#kt_docs_search');\r\n\r\n            if (!element) {\r\n                return;\r\n            }\r\n\r\n            wrapperElement = element.querySelector('[data-kt-search-element=\"wrapper\"]');\r\n            formElement = element.querySelector('[data-kt-search-element=\"form\"]');\r\n            mainElement = element.querySelector('[data-kt-search-element=\"main\"]');\r\n            resultsElement = element.querySelector('[data-kt-search-element=\"results\"]');\r\n            emptyElement = element.querySelector('[data-kt-search-element=\"empty\"]');\r\n            \r\n            // Initialize search handler\r\n            searchObject = new KTSearch(element);\r\n\r\n            // Search handler\r\n            searchObject.on('kt.search.process', processs);\r\n\r\n            // Clear handler\r\n            searchObject.on('kt.search.clear', clear);     \r\n\t\t}\r\n\t};\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function() {\r\n    KTDocsSearch.init();\r\n});"], "names": [], "sourceRoot": ""}