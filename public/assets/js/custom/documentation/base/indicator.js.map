{"version": 3, "file": "js/custom/documentation/base/indicator.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC,E", "sources": ["webpack://keenthemes/../src/js/custom/documentation/base/indicator.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTBaseIndicatorDemos = function() {\r\n    // Private functions\r\n    var _example1 = function(element) {\r\n        // Element to indecate\r\n        var button = document.querySelector(\"#kt_button_1\");\r\n\r\n        // Handle button click event\r\n        button.addEventListener(\"click\", function() {\r\n            // Activate indicator \r\n            button.setAttribute(\"data-kt-indicator\", \"on\");\r\n\r\n            // Disable indicator after 3 seconds\r\n            setTimeout(function() {\r\n                button.removeAttribute(\"data-kt-indicator\");\r\n            }, 3000);\r\n        });\r\n    }\r\n\r\n    var _example2 = function(element) {\r\n        // Element to indecate\r\n        var button = document.querySelector(\"#kt_button_2\");\r\n\r\n        // Handle button click event\r\n        button.addEventListener(\"click\", function() {\r\n            // Activate indicator \r\n            button.setAttribute(\"data-kt-indicator\", \"on\");\r\n\r\n            // Disable indicator after 3 seconds\r\n            setTimeout(function() {\r\n                button.removeAttribute(\"data-kt-indicator\");\r\n            }, 3000);\r\n        });\r\n    }\r\n\r\n    var _example3 = function(element) {\r\n        // Element to indecate\r\n        var button = document.querySelector(\"#kt_button_3\");\r\n\r\n        // Handle button click event\r\n        button.addEventListener(\"click\", function() {\r\n            // Activate indicator \r\n            button.setAttribute(\"data-kt-indicator\", \"on\");\r\n\r\n            // Disable indicator after 3 seconds\r\n            setTimeout(function() {\r\n                button.removeAttribute(\"data-kt-indicator\");\r\n            }, 3000);\r\n        });\r\n    }\r\n    \r\n\r\n    return {\r\n        // Public Functions\r\n        init: function(element) {\r\n            _example1();\r\n            _example2();\r\n            _example3();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function() {\r\n    KTBaseIndicatorDemos.init();\r\n});"], "names": [], "sourceRoot": ""}