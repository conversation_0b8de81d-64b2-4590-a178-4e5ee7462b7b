{"version": 3, "file": "js/custom/documentation/base/toasts.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,sFAAsF;AACtF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC,E", "sources": ["webpack://keenthemes/../src/js/custom/documentation/base/toasts.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nconst KTBaseToastDemos = function () {\r\n    // Private functions\r\n    const exampleToggle = () => {\r\n        // Select elements\r\n        const button = document.getElementById('kt_docs_toast_toggle_button');\r\n        const toastElement = document.getElementById('kt_docs_toast_toggle');\r\n\r\n        // Get toast instance --- more info: https://getbootstrap.com/docs/5.1/components/toasts/#getinstance\r\n        const toast = bootstrap.Toast.getOrCreateInstance(toastElement);\r\n\r\n        // Handle button click\r\n        button.addEventListener('click', e => {\r\n            e.preventDefault();\r\n\r\n            // Toggle toast to show --- more info: https://getbootstrap.com/docs/5.1/components/toasts/#show\r\n            toast.show();\r\n        });\r\n    }\r\n\r\n    const exampleStack = () => {\r\n        // Select elements\r\n        const button = document.getElementById('kt_docs_toast_stack_button');\r\n        const container = document.getElementById('kt_docs_toast_stack_container');\r\n        const targetElement = document.querySelector('[data-kt-docs-toast=\"stack\"]'); // Use CSS class or HTML attr to avoid duplicating ids\r\n\r\n        // Remove base element markup\r\n        targetElement.parentNode.removeChild(targetElement);\r\n\r\n        // Handle button click\r\n        button.addEventListener('click', e => {\r\n            e.preventDefault();\r\n\r\n            // Create new toast element\r\n            const newToast = targetElement.cloneNode(true);\r\n            container.append(newToast);\r\n\r\n            // Create new toast instance --- more info: https://getbootstrap.com/docs/5.1/components/toasts/#getorcreateinstance\r\n            const toast = bootstrap.Toast.getOrCreateInstance(newToast);\r\n\r\n            // Toggle toast to show --- more info: https://getbootstrap.com/docs/5.1/components/toasts/#show\r\n            toast.show();\r\n        });\r\n    }\r\n\r\n    return {\r\n        // Public Functions\r\n        init: function () {\r\n            exampleToggle();\r\n            exampleStack();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function () {\r\n    KTBaseToastDemos.init();\r\n});"], "names": [], "sourceRoot": ""}