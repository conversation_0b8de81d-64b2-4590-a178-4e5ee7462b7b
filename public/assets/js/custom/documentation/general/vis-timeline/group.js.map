{"version": 3, "file": "js/custom/documentation/general/vis-timeline/group.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,WAAW;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC", "sources": ["webpack://keenthemes/../src/js/custom/documentation/general/vis-timeline/group.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTVisTimelineGroup = function () {\r\n    // Private functions\r\n    var exampleGroup = function () {\r\n        var now = Date.now();\r\n\r\n        var options = {\r\n            stack: true,\r\n            maxHeight: 640,\r\n            horizontalScroll: false,\r\n            verticalScroll: true,\r\n            zoomKey: \"ctrlKey\",\r\n            start: Date.now() - 1000 * 60 * 60 * 24 * 3, // minus 3 days\r\n            end: Date.now() + 1000 * 60 * 60 * 24 * 21, // plus 1 months aprox.\r\n            orientation: {\r\n                axis: \"both\",\r\n                item: \"top\",\r\n            },\r\n        };\r\n        var groups = new vis.DataSet();\r\n        var items = new vis.DataSet();\r\n\r\n        var count = 300;\r\n\r\n        for (var i = 0; i < count; i++) {\r\n            var start = now + 1000 * 60 * 60 * 24 * (i + Math.floor(Math.random() * 7));\r\n            var end = start + 1000 * 60 * 60 * 24 * (1 + Math.floor(Math.random() * 5));\r\n\r\n            groups.add({\r\n                id: i,\r\n                content: \"Task \" + i,\r\n                order: i,\r\n            });\r\n\r\n            items.add({\r\n                id: i,\r\n                group: i,\r\n                start: start,\r\n                end: end,\r\n                type: \"range\",\r\n                content: \"Item \" + i,\r\n            });\r\n        }\r\n\r\n        // create a Timeline\r\n        var container = document.getElementById(\"kt_docs_vistimeline_group\");\r\n        var timeline = new vis.Timeline(container, items, groups, options);\r\n        //timeline = new vis.Timeline(container, null, options);\r\n        timeline.setGroups(groups);\r\n        timeline.setItems(items);\r\n\r\n        function debounce(func, wait = 100) {\r\n            let timeout;\r\n            return function (...args) {\r\n                clearTimeout(timeout);\r\n                timeout = setTimeout(() => {\r\n                    func.apply(this, args);\r\n                }, wait);\r\n            };\r\n        }\r\n\r\n        let groupFocus = (e) => {\r\n            let vGroups = timeline.getVisibleGroups();\r\n            let vItems = vGroups.reduce((res, groupId) => {\r\n                let group = timeline.itemSet.groups[groupId];\r\n                if (group.items) {\r\n                    res = res.concat(Object.keys(group.items));\r\n                }\r\n                return res;\r\n            }, []);\r\n            timeline.focus(vItems);\r\n        };\r\n        timeline.on(\"scroll\", debounce(groupFocus, 200));\r\n        // Enabling the next line leads to a continuous since calling focus might scroll vertically even if it shouldn't\r\n        // this.timeline.on(\"scrollSide\", debounce(groupFocus, 200))        \r\n\r\n        // Handle button click\r\n        const button = document.getElementById('kt_docs_vistimeline_group_button');\r\n        button.addEventListener('click', e => {\r\n            e.preventDefault();\r\n\r\n            var a = timeline.getVisibleGroups();\r\n            document.getElementById(\"visibleGroupsContainer\").innerHTML = \"\";\r\n            document.getElementById(\"visibleGroupsContainer\").innerHTML += a;\r\n        });\r\n    }\r\n\r\n    return {\r\n        // Public Functions\r\n        init: function () {\r\n            exampleGroup();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function () {\r\n    KTVisTimelineGroup.init();\r\n});\r\n"], "names": [], "sourceRoot": ""}