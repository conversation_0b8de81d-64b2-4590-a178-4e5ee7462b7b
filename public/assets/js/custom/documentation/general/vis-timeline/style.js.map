{"version": 3, "file": "js/custom/documentation/general/vis-timeline/style.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,aAAa;AACb,cAAc,2DAA2D;AACzE;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC", "sources": ["webpack://keenthemes/../src/js/custom/documentation/general/vis-timeline/style.js"], "sourcesContent": ["\"use strict\";\r\n\r\n\r\n// Class definition\r\nvar KTVisTimelineStyle = function () {\r\n    // Private functions\r\n    var exampleStyle = function () {\r\n        var container = document.getElementById(\"kt_docs_vistimeline_style\");\r\n\r\n        // Generate HTML content\r\n        const getContent = (title, img) => {\r\n            const item = document.createElement('div');\r\n            const name = document.createElement('div');\r\n            const nameClasses = ['fw-bolder', 'mb-2'];\r\n            name.classList.add(...nameClasses);\r\n            name.innerHTML = title;\r\n\r\n            const image = document.createElement('img');\r\n            image.setAttribute('src', img);\r\n\r\n            const symbol = document.createElement('div');\r\n            const symbolClasses = ['symbol', 'symbol-circle', 'symbol-30'];\r\n            symbol.classList.add(...symbolClasses);\r\n            symbol.appendChild(image);\r\n\r\n            item.appendChild(name);\r\n            item.appendChild(symbol);\r\n\r\n            return item;\r\n        }\r\n\r\n        // note that months are zero-based in the JavaScript Date object\r\n        var items = new vis.DataSet([\r\n            {\r\n                start: new Date(2010, 7, 23),\r\n                content: getContent('Conversation', hostUrl + '/media/avatars/150-1.jpg')\r\n            },\r\n            {\r\n                start: new Date(2010, 7, 23, 23, 0, 0),\r\n                content: getContent('Mail from boss', hostUrl + '/media/avatars/150-2.jpg')\r\n            },\r\n            { start: new Date(2010, 7, 24, 16, 0, 0), content: \"Report\" },\r\n            {\r\n                start: new Date(2010, 7, 26),\r\n                end: new Date(2010, 8, 2),\r\n                content: \"Traject A\",\r\n            },\r\n            {\r\n                start: new Date(2010, 7, 28),\r\n                content: getContent('Memo', hostUrl + '/media/avatars/150-3.jpg')\r\n            },\r\n            {\r\n                start: new Date(2010, 7, 29),\r\n                content: getContent('Phone call', hostUrl + '/media/avatars/150-4.jpg')\r\n            },\r\n            {\r\n                start: new Date(2010, 7, 31),\r\n                end: new Date(2010, 8, 3),\r\n                content: \"Traject B\",\r\n            },\r\n            {\r\n                start: new Date(2010, 8, 4, 12, 0, 0),\r\n                content: getContent('Report', hostUrl + '/media/avatars/150-5.jpg')\r\n            },\r\n        ]);\r\n\r\n        var options = {\r\n            editable: true,\r\n            margin: {\r\n                item: 20,\r\n                axis: 40,\r\n            },\r\n        };\r\n\r\n        var timeline = new vis.Timeline(container, items, options);\r\n    }\r\n\r\n    return {\r\n        // Public Functions\r\n        init: function () {\r\n            exampleStyle();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function () {\r\n    KTVisTimelineStyle.init();\r\n});\r\n"], "names": [], "sourceRoot": ""}