{"version": 3, "file": "js/custom/documentation/general/jstree/contextual.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA,6BAA6B;AAC7B;AACA,iCAAiC;AACjC;AACA,yBAAyB;AACzB;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA,iCAAiC,oDAAoD;AACrF,iCAAiC,qDAAqD;AACtF,iCAAiC,qDAAqD;AACtF,iCAAiC,oDAAoD;AACrF,iCAAiC;AACjC;AACA,yBAAyB;AACzB,qBAAqB;AACrB;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,aAAa;AACb,wBAAwB,iBAAiB;AACzC;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC", "sources": ["webpack://keenthemes/../src/js/custom/documentation/general/jstree/contextual.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTJSTreeContextual = function() {\r\n    // Private functions\r\n    var exampleContextual = function() {\r\n        $(\"#kt_docs_jstree_contextual\").jstree({\r\n            \"core\" : {\r\n                \"themes\" : {\r\n                    \"responsive\": false\r\n                },\r\n                // so that create works\r\n                \"check_callback\" : true,\r\n                'data': [{\r\n                        \"text\": \"Parent Node\",\r\n                        \"children\": [{\r\n                            \"text\": \"Initially selected\",\r\n                            \"state\": {\r\n                                \"selected\": true\r\n                            }\r\n                        }, {\r\n                            \"text\": \"Custom Icon\",\r\n                            \"icon\": \"flaticon2-hourglass-1 text-danger\"\r\n                        }, {\r\n                            \"text\": \"Initially open\",\r\n                            \"icon\" : \"fa fa-folder text-success\",\r\n                            \"state\": {\r\n                                \"opened\": true\r\n                            },\r\n                            \"children\": [\r\n                                {\"text\": \"Another node\", \"icon\" : \"fa fa-file text-waring\"}\r\n                            ]\r\n                        }, {\r\n                            \"text\": \"Another Custom Icon\",\r\n                            \"icon\": \"flaticon2-drop text-waring\"\r\n                        }, {\r\n                            \"text\": \"Disabled Node\",\r\n                            \"icon\": \"fa fa-check text-success\",\r\n                            \"state\": {\r\n                                \"disabled\": true\r\n                            }\r\n                        }, {\r\n                            \"text\": \"Sub Nodes\",\r\n                            \"icon\": \"fa fa-folder text-danger\",\r\n                            \"children\": [\r\n                                {\"text\": \"Item 1\", \"icon\" : \"fa fa-file text-waring\"},\r\n                                {\"text\": \"Item 2\", \"icon\" : \"fa fa-file text-success\"},\r\n                                {\"text\": \"Item 3\", \"icon\" : \"fa fa-file text-default\"},\r\n                                {\"text\": \"Item 4\", \"icon\" : \"fa fa-file text-danger\"},\r\n                                {\"text\": \"Item 5\", \"icon\" : \"fa fa-file text-info\"}\r\n                            ]\r\n                        }]\r\n                    },\r\n                    \"Another Node\"\r\n                ]\r\n            },\r\n            \"types\" : {\r\n                \"default\" : {\r\n                    \"icon\" : \"fa fa-folder text-primary\"\r\n                },\r\n                \"file\" : {\r\n                    \"icon\" : \"fa fa-file  text-primary\"\r\n                }\r\n            },\r\n            \"state\" : { \"key\" : \"demo2\" },\r\n            \"plugins\" : [ \"contextmenu\", \"state\", \"types\" ]\r\n        });\r\n    }\r\n\r\n    return {\r\n        // Public Functions\r\n        init: function() {\r\n            exampleContextual();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function() {\r\n    KTJSTreeContextual.init();\r\n});\r\n"], "names": [], "sourceRoot": ""}