{"version": 3, "file": "js/custom/documentation/general/jstree/checkable.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA,6BAA6B;AAC7B;AACA,yBAAyB;AACzB;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB,qBAAqB;AACrB;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC", "sources": ["webpack://keenthemes/../src/js/custom/documentation/general/jstree/checkable.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTJSTreeCheckable = function() {\r\n    // Private functions\r\n    var exampleCheckable = function() {\r\n        $('#kt_docs_jstree_checkable').jstree({\r\n            'plugins': [\"wholerow\", \"checkbox\", \"types\"],\r\n            'core': {\r\n                \"themes\" : {\r\n                    \"responsive\": false\r\n                },\r\n                'data': [{\r\n                        \"text\": \"Same but with checkboxes\",\r\n                        \"children\": [{\r\n                            \"text\": \"initially selected\",\r\n                            \"state\": {\r\n                                \"selected\": true\r\n                            }\r\n                        }, {\r\n                            \"text\": \"custom icon\",\r\n                            \"icon\": \"fa fa-warning text-danger\"\r\n                        }, {\r\n                            \"text\": \"initially open\",\r\n                            \"icon\" : \"fa fa-folder text-default\",\r\n                            \"state\": {\r\n                                \"opened\": true\r\n                            },\r\n                            \"children\": [\"Another node\"]\r\n                        }, {\r\n                            \"text\": \"custom icon\",\r\n                            \"icon\": \"fa fa-warning text-waring\"\r\n                        }, {\r\n                            \"text\": \"disabled node\",\r\n                            \"icon\": \"fa fa-check text-success\",\r\n                            \"state\": {\r\n                                \"disabled\": true\r\n                            }\r\n                        }]\r\n                    },\r\n                    \"And wholerow selection\"\r\n                ]\r\n            },\r\n            \"types\" : {\r\n                \"default\" : {\r\n                    \"icon\" : \"fa fa-folder text-warning\"\r\n                },\r\n                \"file\" : {\r\n                    \"icon\" : \"fa fa-file  text-warning\"\r\n                }\r\n            },\r\n        });\r\n    }\r\n\r\n    return {\r\n        // Public Functions\r\n        init: function() {\r\n            exampleCheckable();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function() {\r\n    KTJSTreeCheckable.init();\r\n});\r\n"], "names": [], "sourceRoot": ""}