{"version": 3, "file": "js/custom/documentation/general/jstree/basic.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,aAAa;AACb;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC", "sources": ["webpack://keenthemes/../src/js/custom/documentation/general/jstree/basic.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTJSTreeBasic = function() {\r\n    // Private functions\r\n    var exampleBasic = function() {\r\n        $('#kt_docs_jstree_basic').jstree({\r\n            \"core\" : {\r\n                \"themes\" : {\r\n                    \"responsive\": false\r\n                }\r\n            },\r\n            \"types\" : {\r\n                \"default\" : {\r\n                    \"icon\" : \"fa fa-folder\"\r\n                },\r\n                \"file\" : {\r\n                    \"icon\" : \"fa fa-file\"\r\n                }\r\n            },\r\n            \"plugins\": [\"types\"]\r\n        });\r\n    }\r\n\r\n    return {\r\n        // Public Functions\r\n        init: function() {\r\n            exampleBasic();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function() {\r\n    KTJSTreeBasic.init();\r\n});\r\n"], "names": [], "sourceRoot": ""}