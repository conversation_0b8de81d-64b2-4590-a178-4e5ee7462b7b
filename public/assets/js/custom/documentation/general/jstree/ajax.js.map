{"version": 3, "file": "js/custom/documentation/general/jstree/ajax.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,0FAA0F;AAC1F,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC", "sources": ["webpack://keenthemes/../src/js/custom/documentation/general/jstree/ajax.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTJSTreeAjax = function() {\r\n    // Private functions\r\n    var exampleAjax = function() {\r\n        $(\"#kt_docs_jstree_ajax\").jstree({\r\n            \"core\": {\r\n                \"themes\": {\r\n                    \"responsive\": false\r\n                },\r\n                // so that create works\r\n                \"check_callback\": true,\r\n                'data': {\r\n                    'url': function(node) {\r\n                        return 'https://preview.keenthemes.com/api/jstree/ajax_data.php'; // Demo API endpoint -- Replace this URL with your set endpoint\r\n                    },\r\n                    'data': function(node) {\r\n                        return {\r\n                            'parent': node.id\r\n                        };\r\n                    }\r\n                }\r\n            },\r\n            \"types\": {\r\n                \"default\": {\r\n                    \"icon\": \"fa fa-folder text-primary\"\r\n                },\r\n                \"file\": {\r\n                    \"icon\": \"fa fa-file  text-primary\"\r\n                }\r\n            },\r\n            \"state\": {\r\n                \"key\": \"demo3\"\r\n            },\r\n            \"plugins\": [\"dnd\", \"state\", \"types\"]\r\n        });\r\n    }\r\n\r\n    return {\r\n        // Public Functions\r\n        init: function() {\r\n            exampleAjax();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function() {\r\n    KTJSTreeAjax.init();\r\n});\r\n"], "names": [], "sourceRoot": ""}