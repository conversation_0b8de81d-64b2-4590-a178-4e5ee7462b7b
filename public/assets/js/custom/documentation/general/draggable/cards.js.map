{"version": 3, "file": "js/custom/documentation/general/draggable/cards.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC", "sources": ["webpack://keenthemes/../src/js/custom/documentation/general/draggable/cards.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTDraggableCards = function() {\r\n    // Private functions\r\n    var exampleCards = function() {\r\n        var containers = document.querySelectorAll('.draggable-zone');\r\n\r\n        if (containers.length === 0) {\r\n            return false;\r\n        }\r\n\r\n        var swappable = new Sortable.default(containers, {\r\n            draggable: '.draggable',\r\n            handle: '.draggable .draggable-handle',\r\n            mirror: {\r\n                //appendTo: selector,\r\n                appendTo: 'body',\r\n                constrainDimensions: true\r\n            }\r\n        });\r\n    }\r\n\r\n    return {\r\n        // Public Functions\r\n        init: function() {\r\n            exampleCards();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function() {\r\n    KTDraggableCards.init();\r\n});\r\n"], "names": [], "sourceRoot": ""}