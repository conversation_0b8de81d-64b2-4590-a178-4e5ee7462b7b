{"version": 3, "file": "js/custom/documentation/general/jkanban/restricted.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC", "sources": ["webpack://keenthemes/../src/js/custom/documentation/general/jkanban/restricted.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTJKanbanDemoRestricted = function() {\r\n    // Private functions\r\n    var exampleRestricted = function() {\r\n        var kanban = new jKanban({\r\n            element: '#kt_docs_jkanban_restricted',\r\n            gutter: '0',\r\n            widthBoard: '250px',\r\n            click: function(el) {\r\n                alert(el.innerHTML);\r\n            },\r\n            boards: [{\r\n                    'id': '_todo',\r\n                    'title': 'To Do',\r\n                    'class': 'light-primary',\r\n                    'dragTo': ['_working'],\r\n                    'item': [{\r\n                            'title': 'My Task Test',\r\n                            'class': 'primary'\r\n                        },\r\n                        {\r\n                            'title': 'Buy Milk',\r\n                            'class': 'primary'\r\n                        }\r\n                    ]\r\n                },\r\n                {\r\n                    'id': '_working',\r\n                    'title': 'Working',\r\n                    'class': 'light-warning',\r\n                    'item': [{\r\n                            'title': 'Do Something!',\r\n                            'class': 'warning'\r\n                        },\r\n                        {\r\n                            'title': 'Run?',\r\n                            'class': 'warning'\r\n                        }\r\n                    ]\r\n                },\r\n                {\r\n                    'id': '_done',\r\n                    'title': 'Done',\r\n                    'class': 'light-success',\r\n                    'dragTo': ['_working'],\r\n                    'item': [{\r\n                            'title': 'All right',\r\n                            'class': 'success'\r\n                        },\r\n                        {\r\n                            'title': 'Ok!',\r\n                            'class': 'success'\r\n                        }\r\n                    ]\r\n                },\r\n                {\r\n                    'id': '_notes',\r\n                    'title': 'Notes',\r\n                    'class': 'light-danger',\r\n                    'item': [{\r\n                            'title': 'Warning Task',\r\n                            'class': 'danger'\r\n                        },\r\n                        {\r\n                            'title': 'Do not enter',\r\n                            'class': 'danger'\r\n                        }\r\n                    ]\r\n                }\r\n            ]\r\n        });\r\n    }\r\n\r\n    return {\r\n        // Public Functions\r\n        init: function() {\r\n            exampleRestricted();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function() {\r\n    KTJKanbanDemoRestricted.init();\r\n});\r\n"], "names": [], "sourceRoot": ""}