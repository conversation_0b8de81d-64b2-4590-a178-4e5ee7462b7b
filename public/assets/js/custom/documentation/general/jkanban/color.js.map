{"version": 3, "file": "js/custom/documentation/general/jkanban/color.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC", "sources": ["webpack://keenthemes/../src/js/custom/documentation/general/jkanban/color.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTJKanbanDemoColor = function() {\r\n    // Private functions\r\n    var exampleColor = function() {\r\n        var kanban = new jKanban({\r\n            element: '#kt_docs_jkanban_color',\r\n            gutter: '0',\r\n            widthBoard: '250px',\r\n            boards: [{\r\n                    'id': '_inprocess',\r\n                    'title': 'In Process',\r\n                    'class': 'primary',\r\n                    'item': [{\r\n                            'title': '<span class=\"fw-bold\">You can drag me too</span>',\r\n                            'class': 'light-primary',\r\n                        },\r\n                        {\r\n                            'title': '<span class=\"fw-bold\">Buy Milk</span>',\r\n                            'class': 'light-primary',\r\n                        }\r\n                    ]\r\n                }, {\r\n                    'id': '_working',\r\n                    'title': 'Working',\r\n                    'class': 'success',\r\n                    'item': [{\r\n                            'title': '<span class=\"fw-bold\">Do Something!</span>',\r\n                            'class': 'light-success',\r\n                        },\r\n                        {\r\n                            'title': '<span class=\"fw-bold\">Run?</span>',\r\n                            'class': 'light-success',\r\n                        }\r\n                    ]\r\n                }, {\r\n                    'id': '_done',\r\n                    'title': 'Done',\r\n                    'class': 'danger',\r\n                    'item': [{\r\n                            'title': '<span class=\"fw-bold\">All right</span>',\r\n                            'class': 'light-danger',\r\n                        },\r\n                        {\r\n                            'title': '<span class=\"fw-bold\">Ok!</span>',\r\n                            'class': 'light-danger',\r\n                        }\r\n                    ]\r\n                }\r\n            ]\r\n        });\r\n    }\r\n\r\n    return {\r\n        // Public Functions\r\n        init: function() {\r\n            exampleColor();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function() {\r\n    KTJKanbanDemoColor.init();\r\n});\r\n"], "names": [], "sourceRoot": ""}