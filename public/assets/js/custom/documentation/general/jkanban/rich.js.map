{"version": 3, "file": "js/custom/documentation/general/jkanban/rich.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2DAA2D,QAAQ;AACnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2DAA2D,QAAQ;AACnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA,2DAA2D,QAAQ;AACnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4DAA4D,QAAQ;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2DAA2D,QAAQ;AACnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA,2DAA2D,QAAQ;AACnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sDAAsD,QAAQ;AAC9D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8DAA8D,QAAQ;AACtE;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA,8DAA8D,QAAQ;AACtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC", "sources": ["webpack://keenthemes/../src/js/custom/documentation/general/jkanban/rich.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTJKanbanDemoRich = function() {\r\n    // Private functions\r\n    var exampleRich = function() {\r\n        var kanban = new jKanban({\r\n            element: '#kt_docs_jkanban_rich',\r\n            gutter: '0',\r\n            click: function(el) {\r\n                alert(el.innerHTML);\r\n            },\r\n            boards: [{\r\n                    'id': '_backlog',\r\n                    'title': 'Backlog',\r\n                    'class': 'light-dark',\r\n                    'item': [{\r\n                            'title': `\r\n                                <div class=\"d-flex align-items-center\">\r\n                        \t        <div class=\"symbol symbol-success me-3\">\r\n                        \t            <img alt=\"Pic\" src=\"${hostUrl}media/avatars/150-1.jpg\" />\r\n                        \t        </div>\r\n                        \t        <div class=\"d-flex flex-column align-items-start\">\r\n                        \t            <span class=\"text-dark-50 fw-bold mb-1\">SEO Optimization</span>\r\n                        \t            <span class=\"badge badge-light-success\">In progress</span>\r\n                        \t        </div>\r\n                        \t    </div>\r\n                            `,\r\n                        },\r\n                        {\r\n                            'title': `\r\n                                <div class=\"d-flex align-items-center\">\r\n                        \t        <div class=\"symbol symbol-success me-3\">\r\n                        \t            <span class=\"symbol-label fs-4\">A.D</span>\r\n                        \t        </div>\r\n                        \t        <div class=\"d-flex flex-column align-items-start\">\r\n                        \t            <span class=\"text-dark-50 fw-bold mb-1\">Finance</span>\r\n                        \t            <span class=\"badge badge-light-danger\">Pending</span>\r\n                        \t        </div>\r\n                        \t    </div>\r\n                            `,\r\n                        }\r\n                    ]\r\n                },\r\n                {\r\n                    'id': '_todo',\r\n                    'title': 'To Do',\r\n                    'class': 'light-danger',\r\n                    'item': [{\r\n                            'title': `\r\n                                <div class=\"d-flex align-items-center\">\r\n                        \t        <div class=\"symbol symbol-success me-3\">\r\n                        \t            <img alt=\"Pic\" src=\"${hostUrl}media/avatars/150-2.jpg\" />\r\n                        \t        </div>\r\n                        \t        <div class=\"d-flex flex-column align-items-start\">\r\n                        \t            <span class=\"text-dark-50 fw-bold mb-1\">Server Setup</span>\r\n                        \t            <span class=\"badge badge-light-info\">Completed</span>\r\n                        \t        </div>\r\n                        \t    </div>\r\n                            `,\r\n                        },\r\n                        {\r\n                            'title': `\r\n                                <div class=\"d-flex align-items-center\">\r\n                        \t        <div class=\"symbol symbol-success me-3\">\r\n                        \t            <img alt=\"Pic\" src=\"${hostUrl}media/avatars/150-3.jpg\" />\r\n                        \t        </div>\r\n                        \t        <div class=\"d-flex flex-column align-items-start\">\r\n                        \t            <span class=\"text-dark-50 fw-bold mb-1\">Report Generation</span>\r\n                        \t            <span class=\"badge badge-light-warning\">Due</span>\r\n                        \t        </div>\r\n                        \t    </div>\r\n                            `,\r\n                        }\r\n                    ]\r\n                },\r\n                {\r\n                    'id': '_working',\r\n                    'title': 'Working',\r\n                    'class': 'light-primary',\r\n                    'item': [{\r\n                            'title': `\r\n                                <div class=\"d-flex align-items-center\">\r\n                        \t        <div class=\"symbol symbol-success me-3\">\r\n                            \t         <img alt=\"Pic\" src=\"${hostUrl}media/avatars/150-1.jpg\" />\r\n                        \t        </div>\r\n                        \t        <div class=\"d-flex flex-column align-items-start\">\r\n                        \t            <span class=\"text-dark-50 fw-bold mb-1\">Marketing</span>\r\n                        \t            <span class=\"badge badge-light-danger\">Planning</span>\r\n                        \t        </div>\r\n                        \t    </div>\r\n                            `,\r\n                        },\r\n                        {\r\n                            'title': `\r\n                                <div class=\"d-flex align-items-center\">\r\n                        \t        <div class=\"symbol symbol-light-info me-3\">\r\n                        \t            <span class=\"symbol-label fs-4\">A.P</span>\r\n                        \t        </div>\r\n                        \t        <div class=\"d-flex flex-column align-items-start\">\r\n                        \t            <span class=\"text-dark-50 fw-bold mb-1\">Finance</span>\r\n                        \t            <span class=\"badge badge-light-primary\">Done</span>\r\n                        \t        </div>\r\n                        \t    </div>\r\n                            `,\r\n                        }\r\n                    ]\r\n                },\r\n                {\r\n                    'id': '_done',\r\n                    'title': 'Done',\r\n                    'class': 'light-success',\r\n                    'item': [{\r\n                            'title': `\r\n                                <div class=\"d-flex align-items-center\">\r\n                        \t        <div class=\"symbol symbol-success me-3\">\r\n                        \t            <img alt=\"Pic\" src=\"${hostUrl}media/avatars/150-4.jpg\" />\r\n                        \t        </div>\r\n                        \t        <div class=\"d-flex flex-column align-items-start\">\r\n                        \t            <span class=\"text-dark-50 fw-bold mb-1\">SEO Optimization</span>\r\n                        \t            <span class=\"badge badge-light-success\">In progress</span>\r\n                        \t        </div>\r\n                        \t    </div>\r\n                            `,\r\n                        },\r\n                        {\r\n                            'title': `\r\n                                <div class=\"d-flex align-items-center\">\r\n                        \t        <div class=\"symbol symbol-success me-3\">\r\n                        \t            <img alt=\"Pic\" src=\"${hostUrl}media/avatars/150-5.jpg\" />\r\n                        \t        </div>\r\n                        \t        <div class=\"d-flex flex-column align-items-start\">\r\n                        \t            <span class=\"text-dark-50 fw-bold mb-1\">Product Team</span>\r\n                        \t            <span class=\"badge badge-light-danger\">In progress</span>\r\n                        \t        </div>\r\n                        \t    </div>\r\n                            `,\r\n                        }\r\n                    ]\r\n                },\r\n                {\r\n                    'id': '_deploy',\r\n                    'title': 'Deploy',\r\n                    'class': 'light-primary',\r\n                    'item': [{\r\n                            'title': `\r\n                                <div class=\"d-flex align-items-center\">\r\n                        \t        <div class=\"symbol symbol-light-warning me-3\">\r\n                        \t            <span class=\"symbol-label fs-4\">D.L</span>\r\n                        \t        </div>\r\n                        \t        <div class=\"d-flex flex-column align-items-start\">\r\n                        \t            <span class=\"text-dark-50 fw-bold mb-1\">SEO Optimization</span>\r\n                        \t            <span class=\"badge badge-light-success\">In progress</span>\r\n                        \t        </div>\r\n                        \t    </div>\r\n                            `,\r\n                        },\r\n                        {\r\n                            'title': `\r\n                                <div class=\"d-flex align-items-center\">\r\n                        \t        <div class=\"symbol symbol-light-danger me-3\">\r\n                        \t            <span class=\"symbol-label fs-4\">E.K</span>\r\n                        \t        </div>\r\n                        \t        <div class=\"d-flex flex-column align-items-start\">\r\n                        \t            <span class=\"text-dark-50 fw-bold mb-1\">Requirement Study</span>\r\n                        \t            <span class=\"badge badge-light-warning\">Scheduled</span>\r\n                        \t        </div>\r\n                        \t    </div>\r\n                            `,\r\n                        }\r\n                    ]\r\n                }\r\n            ]\r\n        });\r\n\r\n        var toDoButton = document.getElementById('addToDo');\r\n        toDoButton.addEventListener('click', function() {\r\n            kanban.addElement(\r\n                '_todo', {\r\n                    'title': `\r\n                        <div class=\"d-flex align-items-center\">\r\n                            <div class=\"symbol symbol-light-primary me-3\">\r\n                                <img alt=\"Pic\" src=\"${hostUrl}media/avatars/150-6.jpg\" />\r\n                            </div>\r\n                            <div class=\"d-flex flex-column align-items-start\">\r\n                                <span class=\"text-dark-50 fw-bold mb-1\">Requirement Study</span>\r\n                                <span class=\"badge badge-light-success\">Scheduled</span>\r\n                            </div>\r\n                        </div>\r\n                    `\r\n                }\r\n            );\r\n        });\r\n\r\n        var addBoardDefault = document.getElementById('addDefault');\r\n        addBoardDefault.addEventListener('click', function() {\r\n            kanban.addBoards(\r\n                [{\r\n                    'id': '_default',\r\n                    'title': 'New Board',\r\n                    'class': 'light-primary',\r\n                    'item': [{\r\n                            'title': `\r\n                                <div class=\"d-flex align-items-center\">\r\n                                    <div class=\"symbol symbol-success me-3\">\r\n                                        <img alt=\"Pic\" src=\"${hostUrl}media/avatars/150-7.jpg\" />\r\n                                    </div>\r\n                                    <div class=\"d-flex flex-column align-items-start\">\r\n                                        <span class=\"text-dark-50 fw-bold mb-1\">Payment Modules</span>\r\n                                        <span class=\"badge badge-light-primary\">In development</span>\r\n                                    </div>\r\n                                </div>\r\n                        `},{\r\n                            'title': `\r\n                                <div class=\"d-flex align-items-center\">\r\n                                    <div class=\"symbol symbol-success me-3\">\r\n                                        <img alt=\"Pic\" src=\"${hostUrl}media/avatars/150-8.jpg\" />\r\n                                    </div>\r\n                                    <div class=\"d-flex flex-column align-items-start\">\r\n                                    <span class=\"text-dark-50 fw-bold mb-1\">New Project</span>\r\n                                    <span class=\"badge badge-light-danger\">Pending</span>\r\n                                </div>\r\n                            </div>\r\n                        `}\r\n                    ]\r\n                }]\r\n            )\r\n        });\r\n\r\n        var removeBoard = document.getElementById('removeBoard');\r\n        removeBoard.addEventListener('click', function() {\r\n            kanban.removeBoard('_done');\r\n        });\r\n    }\r\n\r\n    return {\r\n        // Public Functions\r\n        init: function() {\r\n            exampleRich();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function() {\r\n    KTJKanbanDemoRich.init();\r\n});\r\n"], "names": [], "sourceRoot": ""}