{"version": 3, "file": "js/custom/documentation/general/jkanban/basic.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC", "sources": ["webpack://keenthemes/../src/js/custom/documentation/general/jkanban/basic.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTJKanbanDemoBasic = function() {\r\n    // Private functions\r\n    var exampleBasic = function() {\r\n        var kanban = new jKanban({\r\n            element: '#kt_docs_jkanban_basic',\r\n            gutter: '0',\r\n            widthBoard: '250px',\r\n            boards: [{\r\n                    'id': '_inprocess',\r\n                    'title': 'In Process',\r\n                    'item': [{\r\n                            'title': '<span class=\"fw-bold\">You can drag me too</span>'\r\n                        },\r\n                        {\r\n                            'title': '<span class=\"fw-bold\">Buy Milk</span>'\r\n                        }\r\n                    ]\r\n                }, {\r\n                    'id': '_working',\r\n                    'title': 'Working',\r\n                    'item': [{\r\n                            'title': '<span class=\"fw-bold\">Do Something!</span>'\r\n                        },\r\n                        {\r\n                            'title': '<span class=\"fw-bold\">Run?</span>'\r\n                        }\r\n                    ]\r\n                }, {\r\n                    'id': '_done',\r\n                    'title': 'Done',\r\n                    'item': [{\r\n                            'title': '<span class=\"fw-bold\">All right</span>'\r\n                        },\r\n                        {\r\n                            'title': '<span class=\"fw-bold\">Ok!</span>'\r\n                        }\r\n                    ]\r\n                }\r\n            ]\r\n        });\r\n    }\r\n\r\n    return {\r\n        // Public Functions\r\n        init: function() {\r\n            exampleBasic();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function() {\r\n    KTJKanbanDemoBasic.init();\r\n});\r\n"], "names": [], "sourceRoot": ""}