{"version": 3, "file": "js/custom/documentation/general/fullcalendar/locales.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC", "sources": ["webpack://keenthemes/../src/js/custom/documentation/general/fullcalendar/locales.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTGeneralFullCalendarLocalesDemos = function () {\r\n    // Private functions\r\n\r\n    var examplelocales = function () {\r\n        // Define variables        \r\n        var initialLocaleCode = 'en';\r\n        var localeSelectorEl = document.getElementById('kt_docs_fullcalendar_locale_selector');\r\n        var calendarEl = document.getElementById('kt_docs_fullcalendar_locales');\r\n\r\n        // initialize the calendar -- for more info please visit the official site: https://fullcalendar.io/demos\r\n        var calendar = new FullCalendar.Calendar(calendarEl, {\r\n            headerToolbar: {\r\n                left: 'prev,next today',\r\n                center: 'title',\r\n                right: 'dayGridMonth,timeGridWeek,timeGridDay,listMonth'\r\n            },\r\n            initialDate: '2020-09-12',\r\n            locale: initialLocaleCode,\r\n            buttonIcons: false, // show the prev/next text\r\n            weekNumbers: true,\r\n            navLinks: true, // can click day/week names to navigate views\r\n            editable: true,\r\n            dayMaxEvents: true, // allow \"more\" link when too many events\r\n            events: [\r\n                {\r\n                    title: 'All Day Event',\r\n                    start: '2020-09-01'\r\n                },\r\n                {\r\n                    title: 'Long Event',\r\n                    start: '2020-09-07',\r\n                    end: '2020-09-10'\r\n                },\r\n                {\r\n                    groupId: 999,\r\n                    title: 'Repeating Event',\r\n                    start: '2020-09-09T16:00:00'\r\n                },\r\n                {\r\n                    groupId: 999,\r\n                    title: 'Repeating Event',\r\n                    start: '2020-09-16T16:00:00'\r\n                },\r\n                {\r\n                    title: 'Conference',\r\n                    start: '2020-09-11',\r\n                    end: '2020-09-13'\r\n                },\r\n                {\r\n                    title: 'Meeting',\r\n                    start: '2020-09-12T10:30:00',\r\n                    end: '2020-09-12T12:30:00'\r\n                },\r\n                {\r\n                    title: 'Lunch',\r\n                    start: '2020-09-12T12:00:00'\r\n                },\r\n                {\r\n                    title: 'Meeting',\r\n                    start: '2020-09-12T14:30:00'\r\n                },\r\n                {\r\n                    title: 'Happy Hour',\r\n                    start: '2020-09-12T17:30:00'\r\n                },\r\n                {\r\n                    title: 'Dinner',\r\n                    start: '2020-09-12T20:00:00'\r\n                },\r\n                {\r\n                    title: 'Birthday Party',\r\n                    start: '2020-09-13T07:00:00'\r\n                },\r\n                {\r\n                    title: 'Click for Google',\r\n                    url: 'http://google.com/',\r\n                    start: '2020-09-28'\r\n                }\r\n            ]\r\n        });\r\n\r\n        calendar.render();\r\n\r\n        // build the locale selector's options\r\n        calendar.getAvailableLocaleCodes().forEach(function (localeCode) {\r\n            var optionEl = document.createElement('option');\r\n            optionEl.value = localeCode;\r\n            optionEl.selected = localeCode == initialLocaleCode;\r\n            optionEl.innerText = localeCode;\r\n            localeSelectorEl.appendChild(optionEl);\r\n        });\r\n\r\n        // when the selected option changes, dynamically change the calendar option -- more info on Select2 on Change event: https://select2.org/programmatic-control/events\r\n        $(localeSelectorEl).on('change', function () {\r\n            if (this.value) {\r\n                calendar.setOption('locale', this.value);\r\n            }\r\n        });\r\n\r\n        calendar.render();\r\n    }\r\n\r\n    return {\r\n        // Public Functions\r\n        init: function () {\r\n            examplelocales();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function () {\r\n    KTGeneralFullCalendarLocalesDemos.init();\r\n});\r\n"], "names": [], "sourceRoot": ""}