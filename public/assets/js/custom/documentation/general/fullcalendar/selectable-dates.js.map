{"version": 3, "file": "js/custom/documentation/general/fullcalendar/selectable-dates.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B;AAC7B;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA,iBAAiB;AACjB,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA,iBAAiB;AACjB,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC", "sources": ["webpack://keenthemes/../src/js/custom/documentation/general/fullcalendar/selectable-dates.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTGeneralFullCalendarSelectDemos = function () {\r\n    // Private functions\r\n\r\n    var exampleSelect = function () {\r\n        // Define variables\r\n        var calendarEl = document.getElementById('kt_docs_fullcalendar_selectable');\r\n\r\n        var calendar = new FullCalendar.Calendar(calendarEl, {\r\n            headerToolbar: {\r\n                left: 'prev,next today',\r\n                center: 'title',\r\n                right: 'dayGridMonth,timeGridWeek,timeGridDay'\r\n            },\r\n            initialDate: '2020-09-12',\r\n            navLinks: true, // can click day/week names to navigate views\r\n            selectable: true,\r\n            selectMirror: true,\r\n\r\n            // Create new event\r\n            select: function (arg) {\r\n                Swal.fire({\r\n                    html: '<div class=\"mb-7\">Create new event?</div><div class=\"fw-bolder mb-5\">Event Name:</div><input type=\"text\" class=\"form-control\" name=\"event_name\" />',\r\n                    icon: \"info\",\r\n                    showCancelButton: true,\r\n                    buttonsStyling: false,\r\n                    confirmButtonText: \"Yes, create it!\",\r\n                    cancelButtonText: \"No, return\",\r\n                    customClass: {\r\n                        confirmButton: \"btn btn-primary\",\r\n                        cancelButton: \"btn btn-active-light\"\r\n                    }\r\n                }).then(function (result) {\r\n                    if (result.value) {\r\n                        var title = document.querySelector('input[name=\"event_name\"]').value;\r\n                        if (title) {\r\n                            calendar.addEvent({\r\n                                title: title,\r\n                                start: arg.start,\r\n                                end: arg.end,\r\n                                allDay: arg.allDay\r\n                            })\r\n                        }\r\n                        calendar.unselect()\r\n                    } else if (result.dismiss === 'cancel') {\r\n                        Swal.fire({\r\n                            text: \"Event creation was declined!.\",\r\n                            icon: \"error\",\r\n                            buttonsStyling: false,\r\n                            confirmButtonText: \"Ok, got it!\",\r\n                            customClass: {\r\n                                confirmButton: \"btn btn-primary\",\r\n                            }\r\n                        });\r\n                    }\r\n                });\r\n            },\r\n\r\n            // Delete event\r\n            eventClick: function (arg) {\r\n                Swal.fire({\r\n                    text: 'Are you sure you want to delete this event?',\r\n                    icon: \"warning\",\r\n                    showCancelButton: true,\r\n                    buttonsStyling: false,\r\n                    confirmButtonText: \"Yes, delete it!\",\r\n                    cancelButtonText: \"No, return\",\r\n                    customClass: {\r\n                        confirmButton: \"btn btn-primary\",\r\n                        cancelButton: \"btn btn-active-light\"\r\n                    }\r\n                }).then(function (result) {\r\n                    if (result.value) {\r\n                        arg.event.remove()\r\n                    } else if (result.dismiss === 'cancel') {\r\n                        Swal.fire({\r\n                            text: \"Event was not deleted!.\",\r\n                            icon: \"error\",\r\n                            buttonsStyling: false,\r\n                            confirmButtonText: \"Ok, got it!\",\r\n                            customClass: {\r\n                                confirmButton: \"btn btn-primary\",\r\n                            }\r\n                        });\r\n                    }\r\n                });\r\n            },\r\n            editable: true,\r\n            dayMaxEvents: true, // allow \"more\" link when too many events\r\n            events: [\r\n                {\r\n                    title: 'All Day Event',\r\n                    start: '2020-09-01'\r\n                },\r\n                {\r\n                    title: 'Long Event',\r\n                    start: '2020-09-07',\r\n                    end: '2020-09-10'\r\n                },\r\n                {\r\n                    groupId: 999,\r\n                    title: 'Repeating Event',\r\n                    start: '2020-09-09T16:00:00'\r\n                },\r\n                {\r\n                    groupId: 999,\r\n                    title: 'Repeating Event',\r\n                    start: '2020-09-16T16:00:00'\r\n                },\r\n                {\r\n                    title: 'Conference',\r\n                    start: '2020-09-11',\r\n                    end: '2020-09-13'\r\n                },\r\n                {\r\n                    title: 'Meeting',\r\n                    start: '2020-09-12T10:30:00',\r\n                    end: '2020-09-12T12:30:00'\r\n                },\r\n                {\r\n                    title: 'Lunch',\r\n                    start: '2020-09-12T12:00:00'\r\n                },\r\n                {\r\n                    title: 'Meeting',\r\n                    start: '2020-09-12T14:30:00'\r\n                },\r\n                {\r\n                    title: 'Happy Hour',\r\n                    start: '2020-09-12T17:30:00'\r\n                },\r\n                {\r\n                    title: 'Dinner',\r\n                    start: '2020-09-12T20:00:00'\r\n                },\r\n                {\r\n                    title: 'Birthday Party',\r\n                    start: '2020-09-13T07:00:00'\r\n                },\r\n                {\r\n                    title: 'Click for Google',\r\n                    url: 'http://google.com/',\r\n                    start: '2020-09-28'\r\n                }\r\n            ]\r\n        });\r\n\r\n        calendar.render();\r\n    }\r\n\r\n    return {\r\n        // Public Functions\r\n        init: function () {\r\n            exampleSelect();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function () {\r\n    KTGeneralFullCalendarSelectDemos.init();\r\n});\r\n"], "names": [], "sourceRoot": ""}