{"version": 3, "file": "js/custom/documentation/general/fullcalendar/basic.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC,qBAAqB;AACrD,gCAAgC,oBAAoB;AACpD,+BAA+B;AAC/B,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC", "sources": ["webpack://keenthemes/../src/js/custom/documentation/general/fullcalendar/basic.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTGeneralFullCalendarBasicDemos = function () {\r\n    // Private functions\r\n\r\n    var exampleBasic = function () {\r\n        var todayDate = moment().startOf('day');\r\n        var YM = todayDate.format('YYYY-MM');\r\n        var YESTERDAY = todayDate.clone().subtract(1, 'day').format('YYYY-MM-DD');\r\n        var TODAY = todayDate.format('YYYY-MM-DD');\r\n        var TOMORROW = todayDate.clone().add(1, 'day').format('YYYY-MM-DD');\r\n\r\n        var calendarEl = document.getElementById('kt_docs_fullcalendar_basic');\r\n        var calendar = new FullCalendar.Calendar(calendarEl, {\r\n            headerToolbar: {\r\n                left: 'prev,next today',\r\n                center: 'title',\r\n                right: 'dayGridMonth,timeGridWeek,timeGridDay,listMonth'\r\n            },\r\n\r\n            height: 800,\r\n            contentHeight: 780,\r\n            aspectRatio: 3,  // see: https://fullcalendar.io/docs/aspectRatio\r\n\r\n            nowIndicator: true,\r\n            now: TODAY + 'T09:25:00', // just for demo\r\n\r\n            views: {\r\n                dayGridMonth: { buttonText: 'month' },\r\n                timeGridWeek: { buttonText: 'week' },\r\n                timeGridDay: { buttonText: 'day' }\r\n            },\r\n\r\n            initialView: 'dayGridMonth',\r\n            initialDate: TODAY,\r\n\r\n            editable: true,\r\n            dayMaxEvents: true, // allow \"more\" link when too many events\r\n            navLinks: true,\r\n            events: [\r\n                {\r\n                    title: 'All Day Event',\r\n                    start: YM + '-01',\r\n                    description: 'Toto lorem ipsum dolor sit incid idunt ut',\r\n                    className: \"fc-event-danger fc-event-solid-warning\"\r\n                },\r\n                {\r\n                    title: 'Reporting',\r\n                    start: YM + '-14T13:30:00',\r\n                    description: 'Lorem ipsum dolor incid idunt ut labore',\r\n                    end: YM + '-14',\r\n                    className: \"fc-event-success\"\r\n                },\r\n                {\r\n                    title: 'Company Trip',\r\n                    start: YM + '-02',\r\n                    description: 'Lorem ipsum dolor sit tempor incid',\r\n                    end: YM + '-03',\r\n                    className: \"fc-event-primary\"\r\n                },\r\n                {\r\n                    title: 'ICT Expo 2017 - Product Release',\r\n                    start: YM + '-03',\r\n                    description: 'Lorem ipsum dolor sit tempor inci',\r\n                    end: YM + '-05',\r\n                    className: \"fc-event-light fc-event-solid-primary\"\r\n                },\r\n                {\r\n                    title: 'Dinner',\r\n                    start: YM + '-12',\r\n                    description: 'Lorem ipsum dolor sit amet, conse ctetur',\r\n                    end: YM + '-10'\r\n                },\r\n                {\r\n                    id: 999,\r\n                    title: 'Repeating Event',\r\n                    start: YM + '-09T16:00:00',\r\n                    description: 'Lorem ipsum dolor sit ncididunt ut labore',\r\n                    className: \"fc-event-danger\"\r\n                },\r\n                {\r\n                    id: 1000,\r\n                    title: 'Repeating Event',\r\n                    description: 'Lorem ipsum dolor sit amet, labore',\r\n                    start: YM + '-16T16:00:00'\r\n                },\r\n                {\r\n                    title: 'Conference',\r\n                    start: YESTERDAY,\r\n                    end: TOMORROW,\r\n                    description: 'Lorem ipsum dolor eius mod tempor labore',\r\n                    className: \"fc-event-primary\"\r\n                },\r\n                {\r\n                    title: 'Meeting',\r\n                    start: TODAY + 'T10:30:00',\r\n                    end: TODAY + 'T12:30:00',\r\n                    description: 'Lorem ipsum dolor eiu idunt ut labore'\r\n                },\r\n                {\r\n                    title: 'Lunch',\r\n                    start: TODAY + 'T12:00:00',\r\n                    className: \"fc-event-info\",\r\n                    description: 'Lorem ipsum dolor sit amet, ut labore'\r\n                },\r\n                {\r\n                    title: 'Meeting',\r\n                    start: TODAY + 'T14:30:00',\r\n                    className: \"fc-event-warning\",\r\n                    description: 'Lorem ipsum conse ctetur adipi scing'\r\n                },\r\n                {\r\n                    title: 'Happy Hour',\r\n                    start: TODAY + 'T17:30:00',\r\n                    className: \"fc-event-info\",\r\n                    description: 'Lorem ipsum dolor sit amet, conse ctetur'\r\n                },\r\n                {\r\n                    title: 'Dinner',\r\n                    start: TOMORROW + 'T05:00:00',\r\n                    className: \"fc-event-solid-danger fc-event-light\",\r\n                    description: 'Lorem ipsum dolor sit ctetur adipi scing'\r\n                },\r\n                {\r\n                    title: 'Birthday Party',\r\n                    start: TOMORROW + 'T07:00:00',\r\n                    className: \"fc-event-primary\",\r\n                    description: 'Lorem ipsum dolor sit amet, scing'\r\n                },\r\n                {\r\n                    title: 'Click for Google',\r\n                    url: 'http://google.com/',\r\n                    start: YM + '-28',\r\n                    className: \"fc-event-solid-info fc-event-light\",\r\n                    description: 'Lorem ipsum dolor sit amet, labore'\r\n                }\r\n            ],\r\n\r\n            eventContent: function (info) {\r\n                var element = $(info.el);\r\n\r\n                if (info.event.extendedProps && info.event.extendedProps.description) {\r\n                    if (element.hasClass('fc-day-grid-event')) {\r\n                        element.data('content', info.event.extendedProps.description);\r\n                        element.data('placement', 'top');\r\n                        KTApp.initPopover(element);\r\n                    } else if (element.hasClass('fc-time-grid-event')) {\r\n                        element.find('.fc-title').append('<div class=\"fc-description\">' + info.event.extendedProps.description + '</div>');\r\n                    } else if (element.find('.fc-list-item-title').lenght !== 0) {\r\n                        element.find('.fc-list-item-title').append('<div class=\"fc-description\">' + info.event.extendedProps.description + '</div>');\r\n                    }\r\n                }\r\n            }\r\n        });\r\n\r\n        calendar.render();\r\n    }\r\n\r\n    return {\r\n        // Public Functions\r\n        init: function () {\r\n            exampleBasic();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function () {\r\n    KTGeneralFullCalendarBasicDemos.init();\r\n});\r\n"], "names": [], "sourceRoot": ""}