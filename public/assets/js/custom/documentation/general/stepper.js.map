{"version": 3, "file": "js/custom/documentation/general/stepper.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB,GAAG;AACH;AACA;AACA;AACA,yBAAyB;AACzB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB,GAAG;AACH;AACA;AACA;AACA,yBAAyB;AACzB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gDAAgD;AAChD,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC", "sources": ["webpack://keenthemes/../src/js/custom/documentation/general/stepper.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTGeneralStepperDemos = function() {\r\n    // Private functions\r\n    var _exampleBasic = function() {\r\n        // Stepper lement\r\n        var element = document.querySelector(\"#kt_stepper_example_basic\");\r\n\r\n        // Initialize Stepper\r\n\t\tvar stepper = new KTStepper(element);\r\n\r\n        // Handle next step\r\n\t\tstepper.on(\"kt.stepper.next\", function (stepper) {\r\n\t\t\tstepper.goNext(); // go next step\r\n\t\t});\r\n\r\n\t\t// Handle previous step\r\n\t\tstepper.on(\"kt.stepper.previous\", function (stepper) {\r\n\t\t\tstepper.goPrevious(); // go previous step\r\n\t\t});\r\n    }\r\n\r\n    var _exampleVertical = function() {\r\n        // Stepper lement\r\n        var element = document.querySelector(\"#kt_stepper_example_vertical\");\r\n\r\n        // Initialize Stepper\r\n\t\tvar stepper = new KTStepper(element);\r\n\r\n        // Handle next step\r\n\t\tstepper.on(\"kt.stepper.next\", function (stepper) {\r\n\t\t\tstepper.goNext(); // go next step\r\n\t\t});\r\n\r\n\t\t// Handle previous step\r\n\t\tstepper.on(\"kt.stepper.previous\", function (stepper) {\r\n\t\t\tstepper.goPrevious(); // go previous step\r\n\t\t});\r\n    }\r\n\r\n    var _exampleClickable = function() {\r\n        // Stepper lement\r\n        var element = document.querySelector(\"#kt_stepper_example_clickable\");\r\n\r\n        // Initialize Stepper\r\n\t\tvar stepper = new KTStepper(element);\r\n\r\n        // Handle navigation click\r\n\t\tstepper.on(\"kt.stepper.click\", function (stepper) {\r\n\t\t\tstepper.goTo(stepper.getClickedStepIndex()); // go to clicked step\r\n\t\t});\r\n    }\r\n\r\n    return {\r\n        // Public Functions\r\n        init: function() {\r\n            _exampleBasic();\r\n            _exampleVertical();\r\n            _exampleClickable();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function() {\r\n    KTGeneralStepperDemos.init();\r\n});\r\n"], "names": [], "sourceRoot": ""}