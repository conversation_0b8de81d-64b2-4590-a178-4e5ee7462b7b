{"version": 3, "file": "js/custom/documentation/general/datatables/server-side.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA,kBAAkB,kBAAkB;AACpC,kBAAkB,cAAc;AAChC,kBAAkB,eAAe;AACjC,kBAAkB,iBAAiB;AACnC,kBAAkB,0BAA0B;AAC5C,kBAAkB,kBAAkB;AACpC,kBAAkB,YAAY;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yFAAyF,KAAK;AAC9F;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,4CAA4C,QAAQ,uBAAuB,mBAAmB,iCAAiC,mBAAmB;AAClJ;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B;AAC7B;AACA;AACA,6BAA6B;AAC7B,yBAAyB;AACzB,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA,iBAAiB;AACjB,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA,qBAAqB;AACrB,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC,E", "sources": ["webpack://keenthemes/../src/js/custom/documentation/general/datatables/server-side.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTDatatablesServerSide = function () {\r\n    // Shared variables\r\n    var table;\r\n    var dt;\r\n    var filterPayment;\r\n\r\n    // Private functions\r\n    var initDatatable = function () {\r\n        dt = $(\"#kt_datatable_example_1\").DataTable({\r\n            searchDelay: 500,\r\n            processing: true,\r\n            serverSide: true,\r\n            order: [[5, 'desc']],\r\n            stateSave: true,\r\n            select: {\r\n                style: 'os',\r\n                selector: 'td:first-child',\r\n                className: 'row-selected'\r\n            },\r\n            ajax: {\r\n                url: \"https://preview.keenthemes.com/api/datatables.php\",\r\n            },\r\n            columns: [\r\n                { data: 'RecordID' },\r\n                { data: 'Name' },\r\n                { data: 'Email' },\r\n                { data: 'Company' },\r\n                { data: 'CreditCardNumber' },\r\n                { data: 'Datetime' },\r\n                { data: null },\r\n            ],\r\n            columnDefs: [\r\n                {\r\n                    targets: 0,\r\n                    orderable: false,\r\n                    render: function (data) {\r\n                        return `\r\n                            <div class=\"form-check form-check-sm form-check-custom form-check-solid\">\r\n                                <input class=\"form-check-input\" type=\"checkbox\" value=\"${data}\" />\r\n                            </div>`;\r\n                    }\r\n                },\r\n                {\r\n                    targets: 4,\r\n                    render: function (data, type, row) {\r\n                        return `<img src=\"${hostUrl}media/svg/card-logos/${row.CreditCardType}.svg\" class=\"w-35px me-3\" alt=\"${row.CreditCardType}\">` + data;\r\n                    }\r\n                },\r\n                {\r\n                    targets: -1,\r\n                    data: null,\r\n                    orderable: false,\r\n                    className: 'text-end',\r\n                    render: function (data, type, row) {\r\n                        return `\r\n                            <a href=\"#\" class=\"btn btn-light btn-active-light-primary btn-sm\" data-kt-menu-trigger=\"click\" data-kt-menu-placement=\"bottom-end\" data-kt-menu-flip=\"top-end\">\r\n                                Actions\r\n                                <span class=\"svg-icon svg-icon-5 m-0\">\r\n                                    <svg xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"24px\" height=\"24px\" viewBox=\"0 0 24 24\" version=\"1.1\">\r\n                                        <g stroke=\"none\" stroke-width=\"1\" fill=\"none\" fill-rule=\"evenodd\">\r\n                                            <polygon points=\"0 0 24 0 24 24 0 24\"></polygon>\r\n                                            <path d=\"M6.70710678,15.7071068 C6.31658249,16.0976311 5.68341751,16.0976311 5.29289322,15.7071068 C4.90236893,15.3165825 4.90236893,14.6834175 5.29289322,14.2928932 L11.2928932,8.29289322 C11.6714722,7.91431428 12.2810586,7.90106866 12.6757246,8.26284586 L18.6757246,13.7628459 C19.0828436,14.1360383 19.1103465,14.7686056 18.7371541,15.1757246 C18.3639617,15.5828436 17.7313944,15.6103465 17.3242754,15.2371541 L12.0300757,10.3841378 L6.70710678,15.7071068 Z\" fill=\"#000000\" fill-rule=\"nonzero\" transform=\"translate(12.000003, 11.999999) rotate(-180.000000) translate(-12.000003, -11.999999)\"></path>\r\n                                        </g>\r\n                                    </svg>\r\n                                </span>\r\n                            </a>\r\n                            <!--begin::Menu-->\r\n                            <div class=\"menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-bold fs-7 w-125px py-4\" data-kt-menu=\"true\">\r\n                                <!--begin::Menu item-->\r\n                                <div class=\"menu-item px-3\">\r\n                                    <a href=\"#\" class=\"menu-link px-3\" data-kt-docs-table-filter=\"edit_row\">\r\n                                        Edit\r\n                                    </a>\r\n                                </div>\r\n                                <!--end::Menu item-->\r\n                                \r\n                                <!--begin::Menu item-->\r\n                                <div class=\"menu-item px-3\">\r\n                                    <a href=\"#\" class=\"menu-link px-3\" data-kt-docs-table-filter=\"delete_row\">\r\n                                        Delete\r\n                                    </a>\r\n                                </div>\r\n                                <!--end::Menu item-->\r\n                            </div>\r\n                            <!--end::Menu-->\r\n                        `;\r\n                    },\r\n                },\r\n            ],\r\n            // Add data-filter attribute\r\n            createdRow: function (row, data, dataIndex) {\r\n                $(row).find('td:eq(4)').attr('data-filter', data.CreditCardType);\r\n            }\r\n        });\r\n\r\n        table = dt.$;\r\n\r\n        // Re-init functions on every table re-draw -- more info: https://datatables.net/reference/event/draw\r\n        dt.on('draw', function () {\r\n            initToggleToolbar();\r\n            toggleToolbars();\r\n            handleDeleteRows();\r\n            KTMenu.createInstances();\r\n        });\r\n    }\r\n\r\n    // Search Datatable --- official docs reference: https://datatables.net/reference/api/search()\r\n    var handleSearchDatatable = function () {\r\n        const filterSearch = document.querySelector('[data-kt-docs-table-filter=\"search\"]');\r\n        filterSearch.addEventListener('keyup', function (e) {\r\n            dt.search(e.target.value).draw();\r\n        });\r\n    }\r\n\r\n    // Filter Datatable\r\n    var handleFilterDatatable = () => {\r\n        // Select filter options\r\n        filterPayment = document.querySelectorAll('[data-kt-docs-table-filter=\"payment_type\"] [name=\"payment_type\"]');\r\n        const filterButton = document.querySelector('[data-kt-docs-table-filter=\"filter\"]');\r\n\r\n        // Filter datatable on submit\r\n        filterButton.addEventListener('click', function () {\r\n            // Get filter values\r\n            let paymentValue = '';\r\n\r\n            // Get payment value\r\n            filterPayment.forEach(r => {\r\n                if (r.checked) {\r\n                    paymentValue = r.value;\r\n                }\r\n\r\n                // Reset payment value if \"All\" is selected\r\n                if (paymentValue === 'all') {\r\n                    paymentValue = '';\r\n                }\r\n            });\r\n\r\n            // Filter datatable --- official docs reference: https://datatables.net/reference/api/search()\r\n            dt.search(paymentValue).draw();\r\n        });\r\n    }\r\n\r\n    // Delete customer\r\n    var handleDeleteRows = () => {\r\n        // Select all delete buttons\r\n        const deleteButtons = document.querySelectorAll('[data-kt-docs-table-filter=\"delete_row\"]');\r\n\r\n        deleteButtons.forEach(d => {\r\n            // Delete button on click\r\n            d.addEventListener('click', function (e) {\r\n                e.preventDefault();\r\n\r\n                // Select parent row\r\n                const parent = e.target.closest('tr');\r\n\r\n                // Get customer name\r\n                const customerName = parent.querySelectorAll('td')[1].innerText;\r\n\r\n                // SweetAlert2 pop up --- official docs reference: https://sweetalert2.github.io/\r\n                Swal.fire({\r\n                    text: \"Are you sure you want to delete \" + customerName + \"?\",\r\n                    icon: \"warning\",\r\n                    showCancelButton: true,\r\n                    buttonsStyling: false,\r\n                    confirmButtonText: \"Yes, delete!\",\r\n                    cancelButtonText: \"No, cancel\",\r\n                    customClass: {\r\n                        confirmButton: \"btn fw-bold btn-danger\",\r\n                        cancelButton: \"btn fw-bold btn-active-light-primary\"\r\n                    }\r\n                }).then(function (result) {\r\n                    if (result.value) {\r\n                        // Simulate delete request -- for demo purpose only\r\n                        Swal.fire({\r\n                            text: \"Deleting \" + customerName,\r\n                            icon: \"info\",\r\n                            buttonsStyling: false,\r\n                            showConfirmButton: false,\r\n                            timer: 2000\r\n                        }).then(function () {\r\n                            Swal.fire({\r\n                                text: \"You have deleted \" + customerName + \"!.\",\r\n                                icon: \"success\",\r\n                                buttonsStyling: false,\r\n                                confirmButtonText: \"Ok, got it!\",\r\n                                customClass: {\r\n                                    confirmButton: \"btn fw-bold btn-primary\",\r\n                                }\r\n                            }).then(function () {\r\n                                // delete row data from server and re-draw datatable\r\n                                dt.draw();\r\n                            });\r\n                        });\r\n                    } else if (result.dismiss === 'cancel') {\r\n                        Swal.fire({\r\n                            text: customerName + \" was not deleted.\",\r\n                            icon: \"error\",\r\n                            buttonsStyling: false,\r\n                            confirmButtonText: \"Ok, got it!\",\r\n                            customClass: {\r\n                                confirmButton: \"btn fw-bold btn-primary\",\r\n                            }\r\n                        });\r\n                    }\r\n                });\r\n            })\r\n        });\r\n    }\r\n\r\n    // Reset Filter\r\n    var handleResetForm = () => {\r\n        // Select reset button\r\n        const resetButton = document.querySelector('[data-kt-docs-table-filter=\"reset\"]');\r\n\r\n        // Reset datatable\r\n        resetButton.addEventListener('click', function () {\r\n            // Reset payment type\r\n            filterPayment[0].checked = true;\r\n\r\n            // Reset datatable --- official docs reference: https://datatables.net/reference/api/search()\r\n            dt.search('').draw();\r\n        });\r\n    }\r\n\r\n    // Init toggle toolbar\r\n    var initToggleToolbar = function () {\r\n        // Toggle selected action toolbar\r\n        // Select all checkboxes\r\n        const container = document.querySelector('#kt_datatable_example_1');\r\n        const checkboxes = container.querySelectorAll('[type=\"checkbox\"]');\r\n\r\n        // Select elements\r\n        const deleteSelected = document.querySelector('[data-kt-docs-table-select=\"delete_selected\"]');\r\n\r\n        // Toggle delete selected toolbar\r\n        checkboxes.forEach(c => {\r\n            // Checkbox on click event\r\n            c.addEventListener('click', function () {\r\n                setTimeout(function () {\r\n                    toggleToolbars();\r\n                }, 50);\r\n            });\r\n        });\r\n\r\n        // Deleted selected rows\r\n        deleteSelected.addEventListener('click', function () {\r\n            // SweetAlert2 pop up --- official docs reference: https://sweetalert2.github.io/\r\n            Swal.fire({\r\n                text: \"Are you sure you want to delete selected customers?\",\r\n                icon: \"warning\",\r\n                showCancelButton: true,\r\n                buttonsStyling: false,\r\n                showLoaderOnConfirm: true,\r\n                confirmButtonText: \"Yes, delete!\",\r\n                cancelButtonText: \"No, cancel\",\r\n                customClass: {\r\n                    confirmButton: \"btn fw-bold btn-danger\",\r\n                    cancelButton: \"btn fw-bold btn-active-light-primary\"\r\n                },\r\n            }).then(function (result) {\r\n                if (result.value) {\r\n                    // Simulate delete request -- for demo purpose only\r\n                    Swal.fire({\r\n                        text: \"Deleting selected customers\",\r\n                        icon: \"info\",\r\n                        buttonsStyling: false,\r\n                        showConfirmButton: false,\r\n                        timer: 2000\r\n                    }).then(function () {\r\n                        Swal.fire({\r\n                            text: \"You have deleted all selected customers!.\",\r\n                            icon: \"success\",\r\n                            buttonsStyling: false,\r\n                            confirmButtonText: \"Ok, got it!\",\r\n                            customClass: {\r\n                                confirmButton: \"btn fw-bold btn-primary\",\r\n                            }\r\n                        }).then(function () {\r\n                            // delete row data from server and re-draw datatable\r\n                            dt.draw();\r\n                        });\r\n\r\n                        // Remove header checked box\r\n                        const headerCheckbox = container.querySelectorAll('[type=\"checkbox\"]')[0];\r\n                        headerCheckbox.checked = false;\r\n                    });\r\n                } else if (result.dismiss === 'cancel') {\r\n                    Swal.fire({\r\n                        text: \"Selected customers was not deleted.\",\r\n                        icon: \"error\",\r\n                        buttonsStyling: false,\r\n                        confirmButtonText: \"Ok, got it!\",\r\n                        customClass: {\r\n                            confirmButton: \"btn fw-bold btn-primary\",\r\n                        }\r\n                    });\r\n                }\r\n            });\r\n        });\r\n    }\r\n\r\n    // Toggle toolbars\r\n    var toggleToolbars = function () {\r\n        // Define variables\r\n        const container = document.querySelector('#kt_datatable_example_1');\r\n        const toolbarBase = document.querySelector('[data-kt-docs-table-toolbar=\"base\"]');\r\n        const toolbarSelected = document.querySelector('[data-kt-docs-table-toolbar=\"selected\"]');\r\n        const selectedCount = document.querySelector('[data-kt-docs-table-select=\"selected_count\"]');\r\n\r\n        // Select refreshed checkbox DOM elements \r\n        const allCheckboxes = container.querySelectorAll('tbody [type=\"checkbox\"]');\r\n\r\n        // Detect checkboxes state & count\r\n        let checkedState = false;\r\n        let count = 0;\r\n\r\n        // Count checked boxes\r\n        allCheckboxes.forEach(c => {\r\n            if (c.checked) {\r\n                checkedState = true;\r\n                count++;\r\n            }\r\n        });\r\n\r\n        // Toggle toolbars\r\n        if (checkedState) {\r\n            selectedCount.innerHTML = count;\r\n            toolbarBase.classList.add('d-none');\r\n            toolbarSelected.classList.remove('d-none');\r\n        } else {\r\n            toolbarBase.classList.remove('d-none');\r\n            toolbarSelected.classList.add('d-none');\r\n        }\r\n    }\r\n\r\n    // Public methods\r\n    return {\r\n        init: function () {\r\n            initDatatable();\r\n            handleSearchDatatable();\r\n            initToggleToolbar();\r\n            handleFilterDatatable();\r\n            handleDeleteRows();\r\n            handleResetForm();\r\n        }\r\n    }\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function () {\r\n    KTDatatablesServerSide.init();\r\n});"], "names": [], "sourceRoot": ""}