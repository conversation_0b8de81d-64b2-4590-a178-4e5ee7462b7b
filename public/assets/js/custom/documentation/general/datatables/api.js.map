{"version": 3, "file": "js/custom/documentation/general/datatables/api.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC", "sources": ["webpack://keenthemes/../src/js/custom/documentation/general/datatables/api.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTDatatablesApi = function () {\r\n    // Private functions\r\n\r\n    var _initExample1 = function() {\r\n        var t = $(\"#kt_datatable_example_1\").DataTable();\r\n        var counter = 1;\r\n    \r\n        $(\"#kt_datatable_example_1_addrow\").on( \"click\", function () {\r\n            t.row.add( [\r\n                counter +\".1\",\r\n                counter +\".2\",\r\n                counter +\".3\",\r\n                counter +\".4\",\r\n                counter +\".5\",\r\n            ] ).draw( false );\r\n    \r\n            counter++;\r\n        } );\r\n    \r\n        // Automatically add a first row of data\r\n        $(\"#kt_datatable_example_1_addrow\").click();\r\n    }\r\n\r\n    var _initExample2 = function() {\r\n        var table = $(\"#kt_datatable_example_2\").DataTable({\r\n            columnDefs: [{\r\n                orderable: false,\r\n                targets: [1,2,3]\r\n            }]\r\n        });\r\n     \r\n        $(\"#kt_datatable_example_2_submit\").click( function() {\r\n            var data = table.$(\"input, select\").serialize();\r\n            alert(\r\n                \"The following data would have been submitted to the server: \\n\\n\"+\r\n                data.substr( 0, 120 )+\"...\"\r\n            );\r\n            return false;\r\n        });\r\n    }\r\n\r\n    // Public methods\r\n    return {\r\n        init: function () {\r\n            _initExample1();\r\n            _initExample2();\r\n        }\r\n    }\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function() {\r\n    KTDatatablesApi.init();\r\n});\r\n"], "names": [], "sourceRoot": ""}