{"version": 3, "file": "js/custom/documentation/general/search/responsive.js", "mappings": ";;;;;;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA,IAAI,KAA6B;AACjC;AACA;;;;;;UCnGA;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;UEtBA;UACA;UACA;UACA", "sources": ["webpack://keenthemes/../src/js/custom/documentation/general/search/responsive.js", "webpack://keenthemes/webpack/bootstrap", "webpack://keenthemes/webpack/before-startup", "webpack://keenthemes/webpack/startup", "webpack://keenthemes/webpack/after-startup"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTGeneralSearchResponsiveDemos = function() {\r\n    // Private variables\r\n    var element;\r\n    var recentlyViewedElement;\r\n    var resultsElement;\r\n    var wrapperElement;\r\n    var emptyElement;\r\n    var preferencesElement;\r\n    var preferencesShowElement;\r\n    var preferencesDismissElement;\r\n    var searchObject;\r\n\r\n    // Private functions\r\n    var processs = function(search) {\r\n        var timeout = setTimeout(function() {\r\n            var number = KTUtil.getRandomInt(1, 3);\r\n\r\n            // Hide recently viewed\r\n            recentlyViewedElement.classList.add('d-none');\r\n\r\n            if (number === 3) {\r\n                // Hide results\r\n                resultsElement.classList.add('d-none');\r\n                // Show empty message \r\n                emptyElement.classList.remove('d-none');\r\n            } else {\r\n                // Show results\r\n                resultsElement.classList.remove('d-none');\r\n                // Hide empty message \r\n                emptyElement.classList.add('d-none');\r\n            }                  \r\n\r\n            // Complete search\r\n            search.complete();\r\n        }, 1500);\r\n    }\r\n\r\n    var clear = function(search) {\r\n        // Show recently viewed\r\n        recentlyViewedElement.classList.remove('d-none');\r\n        // Hide results\r\n        resultsElement.classList.add('d-none');\r\n        // Hide empty message \r\n        emptyElement.classList.add('d-none');\r\n    }    \r\n\r\n    // Public methods\r\n\treturn {\r\n\t\tinit: function() {\r\n            // Elements\r\n            element = document.querySelector('#kt_docs_search_handler_responsive');\r\n\r\n            if (!element) {\r\n                return;\r\n            }\r\n\r\n            wrapperElement = element.querySelector('[data-kt-search-element=\"wrapper\"]');\r\n            recentlyViewedElement = element.querySelector('[data-kt-search-element=\"recently-viewed\"]');\r\n            resultsElement = element.querySelector('[data-kt-search-element=\"results\"]');\r\n            emptyElement = element.querySelector('[data-kt-search-element=\"empty\"]');\r\n            preferencesElement = element.querySelector('[data-kt-search-element=\"preferences\"]');\r\n            preferencesShowElement = element.querySelector('[data-kt-search-element=\"preferences-show\"]');\r\n            preferencesDismissElement = element.querySelector('[data-kt-search-element=\"preferences-dismiss\"]');\r\n            \r\n            // Initialize search handler\r\n            searchObject = new KTSearch(element);\r\n\r\n            // Search handler\r\n            searchObject.on('kt.search.process', processs);\r\n\r\n            // Clear handler\r\n            searchObject.on('kt.search.clear', clear);\r\n\r\n            // Preference show handler\r\n            preferencesShowElement.addEventListener('click', function() {\r\n                wrapperElement.classList.add('d-none');\r\n                preferencesElement.classList.remove('d-none');\r\n            });\r\n\r\n            // Preference dismiss handler\r\n            preferencesDismissElement.addEventListener('click', function() {\r\n                wrapperElement.classList.remove('d-none');\r\n                preferencesElement.classList.add('d-none');\r\n            });\r\n\t\t}\r\n\t};\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function() {\r\n    KTGeneralSearchResponsiveDemos.init();\r\n});\r\n\r\n// Webpack support\r\nif (typeof module !== 'undefined' && typeof module.exports !== 'undefined') {\r\n    module.exports = KTGeneralSearchResponsiveDemos;\r\n}", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "", "// startup\n// Load entry module and return exports\n// This entry module is referenced by other modules so it can't be inlined\nvar __webpack_exports__ = __webpack_require__(\"../src/js/custom/documentation/general/search/responsive.js\");\n", ""], "names": [], "sourceRoot": ""}