{"version": 3, "file": "js/custom/documentation/general/blockui.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC", "sources": ["webpack://keenthemes/../src/js/custom/documentation/general/blockui.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTGeneralBlockUI = function() {\r\n    // Private functions\r\n    var example1 = function() {\r\n        var button = document.querySelector(\"#kt_block_ui_1_button\");\r\n        var target = document.querySelector(\"#kt_block_ui_1_target\");\r\n\r\n        var blockUI = new KTBlockUI(target);\r\n\r\n        button.addEventListener(\"click\", function() {\r\n            if (blockUI.isBlocked()) {\r\n                blockUI.release();\r\n                button.innerText = \"Block\";\r\n            } else {\r\n                blockUI.block();\r\n                button.innerText = \"Release\";\r\n            }\r\n        });\r\n    }\r\n\r\n    var example2 = function() {\r\n        var button = document.querySelector(\"#kt_block_ui_2_button\");\r\n        var target = document.querySelector(\"#kt_block_ui_2_target\");\r\n\r\n        var blockUI = new KTBlockUI(target, {\r\n            message: '<div class=\"blockui-message\"><span class=\"spinner-border text-primary\"></span> Loading...</div>',\r\n        });\r\n\r\n        button.addEventListener(\"click\", function() {\r\n            if (blockUI.isBlocked()) {\r\n                blockUI.release();\r\n                button.innerText = \"Block\";\r\n            } else {\r\n                blockUI.block();\r\n                button.innerText = \"Release\";\r\n            }\r\n        });\r\n    }\r\n\r\n    var example3 = function() {\r\n        var button = document.querySelector(\"#kt_block_ui_3_button\");\r\n        var target = document.querySelector(\"#kt_block_ui_3_target\");\r\n\r\n        var blockUI = new KTBlockUI(target, {\r\n            overlayClass: 'bg-danger bg-opacity-25',\r\n        });\r\n\r\n        button.addEventListener(\"click\", function() {\r\n            if (blockUI.isBlocked()) {\r\n                blockUI.release();\r\n                button.innerText = \"Block\";\r\n            } else {\r\n                blockUI.block();\r\n                button.innerText = \"Release\";\r\n            }\r\n        });\r\n    }\r\n\r\n    var example4 = function() {\r\n        var button = document.querySelector(\"#kt_block_ui_4_button\");\r\n        var target = document.querySelector(\"#kt_block_ui_4_target\");\r\n\r\n        var blockUI = new KTBlockUI(target);\r\n\r\n        button.addEventListener(\"click\", function(e) {\r\n            e.preventDefault();\r\n\r\n            blockUI.block();\r\n\r\n            setTimeout(function() {\r\n                blockUI.release();\r\n            }, 3000);\r\n        });\r\n    }\r\n\r\n    var example5 = function() {\r\n        var button = document.querySelector(\"#kt_block_ui_5_button\");\r\n\r\n        var blockUI = new KTBlockUI(document.body);\r\n\r\n        button.addEventListener(\"click\", function(e) {\r\n            e.preventDefault();\r\n\r\n            blockUI.block();\r\n\r\n            setTimeout(function() {\r\n                //blockUI.release();\r\n            }, 3000);\r\n        });\r\n    }\r\n\r\n    return {\r\n        // Public Functions\r\n        init: function() {\r\n            example1();\r\n            example2();\r\n            example3();\r\n            example4();\r\n            example5();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function() {\r\n    KTGeneralBlockUI.init();\r\n});\r\n"], "names": [], "sourceRoot": ""}