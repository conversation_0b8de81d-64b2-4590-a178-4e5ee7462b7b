{"version": 3, "file": "js/custom/documentation/general/typed.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC", "sources": ["webpack://keenthemes/../src/js/custom/documentation/general/typed.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTGeneralTypedJsDemos = function() {\r\n    // Private functions\r\n    var exampleBasic = function() {\r\n        var typed = new Typed(\"#kt_typedjs_example_1\", {\r\n            strings: [\"First sentence.\", \"Second sentence.\", \"Third sentense\", \"And some longer sentence\"],\r\n            typeSpeed: 30\r\n        });\r\n    }\r\n\r\n    return {\r\n        // Public Functions\r\n        init: function() {\r\n            exampleBasic();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function() {\r\n    KTGeneralTypedJsDemos.init();\r\n});\r\n"], "names": [], "sourceRoot": ""}