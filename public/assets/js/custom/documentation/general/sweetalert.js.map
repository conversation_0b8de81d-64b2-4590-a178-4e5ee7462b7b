{"version": 3, "file": "js/custom/documentation/general/sweetalert.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC", "sources": ["webpack://keenthemes/../src/js/custom/documentation/general/sweetalert.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTGeneralSweetAlertDemos = function() {\r\n    // Private functions\r\n    const exampleBasic = function() {\r\n        const button = document.getElementById('kt_docs_sweetalert_basic');\r\n\r\n        button.addEventListener('click', e =>{\r\n            e.preventDefault();\r\n\r\n            Swal.fire({\r\n                text: \"Here's a basic example of SweetAlert!\",\r\n                icon: \"success\",\r\n                buttonsStyling: false,\r\n                confirmButtonText: \"Ok, got it!\",\r\n                customClass: {\r\n                    confirmButton: \"btn btn-primary\"\r\n                }\r\n            });\r\n        });\r\n    }\r\n\r\n    const exampleHtml = function() {\r\n        const button = document.getElementById('kt_docs_sweetalert_html');\r\n\r\n        button.addEventListener('click', e =>{\r\n            e.preventDefault();\r\n\r\n            Swal.fire({\r\n                html: 'A SweetAlert content with <strong>bold text</strong>, <a href=\"#\">links</a> or any of our available <span class=\"badge badge-primary\">components</span>',\r\n                icon: \"info\",\r\n                buttonsStyling: false,\r\n                showCancelButton: true,\r\n                confirmButtonText: \"Ok, got it!\",\r\n                cancelButtonText: 'Nope, cancel it',\r\n                customClass: {\r\n                    confirmButton: \"btn btn-primary\",\r\n                    cancelButton: 'btn btn-danger'\r\n                }\r\n            });\r\n        });\r\n    }\r\n\r\n    const exampleStates = () => {\r\n        // Select buttons\r\n        const infoButton = document.getElementById('kt_docs_sweetalert_state_info');\r\n        const warningButton = document.getElementById('kt_docs_sweetalert_state_warning');\r\n        const errorButton = document.getElementById('kt_docs_sweetalert_state_error');\r\n        const successButton = document.getElementById('kt_docs_sweetalert_state_success');\r\n        const questionButton = document.getElementById('kt_docs_sweetalert_state_question');\r\n\r\n        // Click action handlers\r\n        infoButton.addEventListener('click', e => {\r\n            e.preventDefault();\r\n\r\n            Swal.fire({\r\n                text: \"Here's an example of an info SweetAlert!\",\r\n                icon: \"info\",\r\n                buttonsStyling: false,\r\n                confirmButtonText: \"Ok, got it!\",\r\n                customClass: {\r\n                    confirmButton: \"btn btn-info\"\r\n                }\r\n            });\r\n        });\r\n\r\n        warningButton.addEventListener('click', e => {\r\n            e.preventDefault();\r\n\r\n            Swal.fire({\r\n                text: \"Here's an example of a warning SweetAlert!\",\r\n                icon: \"warning\",\r\n                buttonsStyling: false,\r\n                confirmButtonText: \"Ok, got it!\",\r\n                customClass: {\r\n                    confirmButton: \"btn btn-warning\"\r\n                }\r\n            });\r\n        });\r\n\r\n        errorButton.addEventListener('click', e => {\r\n            e.preventDefault();\r\n\r\n            Swal.fire({\r\n                text: \"Here's an example of an error SweetAlert!\",\r\n                icon: \"error\",\r\n                buttonsStyling: false,\r\n                confirmButtonText: \"Ok, got it!\",\r\n                customClass: {\r\n                    confirmButton: \"btn btn-danger\"\r\n                }\r\n            });\r\n        });\r\n\r\n        successButton.addEventListener('click', e => {\r\n            e.preventDefault();\r\n\r\n            Swal.fire({\r\n                text: \"Here's an example of a success SweetAlert!\",\r\n                icon: \"success\",\r\n                buttonsStyling: false,\r\n                confirmButtonText: \"Ok, got it!\",\r\n                customClass: {\r\n                    confirmButton: \"btn btn-success\"\r\n                }\r\n            });\r\n        });\r\n\r\n        questionButton.addEventListener('click', e => {\r\n            e.preventDefault();\r\n\r\n            Swal.fire({\r\n                text: \"Here's an example of a question SweetAlert!\",\r\n                icon: \"question\",\r\n                buttonsStyling: false,\r\n                confirmButtonText: \"Ok, got it!\",\r\n                customClass: {\r\n                    confirmButton: \"btn btn-primary\"\r\n                }\r\n            });\r\n        });\r\n    }\r\n\r\n    return {\r\n        // Public Functions\r\n        init: function() {\r\n            exampleBasic();\r\n            exampleHtml();\r\n            exampleStates();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function() {\r\n    KTGeneralSweetAlertDemos.init();\r\n});\r\n"], "names": [], "sourceRoot": ""}