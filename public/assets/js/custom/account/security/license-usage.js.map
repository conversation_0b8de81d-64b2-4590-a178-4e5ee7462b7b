{"version": 3, "file": "js/custom/account/security/license-usage.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC", "sources": ["webpack://keenthemes/../../../themes/metronic/html/demo3/src/js/custom/account/security/license-usage.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTAccountSecurityLicenseUsage = function () {\r\n    // Private functions\r\n    var initLicenceCopy = function() {\r\n        KTUtil.each(document.querySelectorAll('#kt_security_license_usage_table [data-action=\"copy\"]'), function(button) {\r\n            var tr = button.closest('tr');\r\n            var license = KTUtil.find(tr, '[data-bs-target=\"license\"]');\r\n\r\n            var clipboard = new ClipboardJS(button, {\r\n                target: license,\r\n                text: function() {\r\n                    return license.innerHTML;\r\n                }\r\n            });\r\n        \r\n            clipboard.on('success', function(e) {\r\n                // Icons\r\n                var svgIcon = button.querySelector('.svg-icon');                \r\n                var checkIcon = button.querySelector('.bi.bi-check');\r\n                \r\n                // exit if check icon is already shown\r\n                if (checkIcon) {\r\n                   return;  \r\n                }\r\n\r\n                // Create check icon\r\n                checkIcon = document.createElement('i');\r\n                checkIcon.classList.add('bi');\r\n                checkIcon.classList.add('bi-check');\r\n                checkIcon.classList.add('fs-2x');\r\n\r\n                // Append check icon\r\n                button.appendChild(checkIcon);\r\n\r\n                // Highlight target\r\n                license.classList.add('text-success');\r\n\r\n                // Hide copy icon\r\n                svgIcon.classList.add('d-none');\r\n\r\n                // Set 3 seconds timeout to hide the check icon and show copy icon back\r\n                setTimeout(function() {\r\n                    // Remove check icon\r\n                    svgIcon.classList.remove('d-none');\r\n                    // Show check icon back\r\n                    button.removeChild(checkIcon);\r\n\r\n                    // Remove highlight\r\n                    license.classList.remove('text-success');\r\n                }, 3000);\r\n            });\r\n        });\r\n    }\r\n\r\n    // Public methods\r\n    return {\r\n        init: function () {\r\n            initLicenceCopy();\r\n        }\r\n    }\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function() {\r\n    KTAccountSecurityLicenseUsage.init();\r\n});\r\n"], "names": [], "sourceRoot": ""}