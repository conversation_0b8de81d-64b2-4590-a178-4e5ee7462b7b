{"version": 3, "file": "js/custom/account/security/security-summary.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC", "sources": ["webpack://keenthemes/../../../themes/metronic/html/demo3/src/js/custom/account/security/security-summary.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTAccountSecuritySummary = function () {\r\n    // Private functions\r\n    var initChart = function(tabSelector, chartSelector, data1, data2, initByDefault) {\r\n        var element = document.querySelector(chartSelector);\r\n        var height = parseInt(KTUtil.css(element, 'height'));\r\n\r\n        if (!element) {\r\n            return;\r\n        }\r\n \r\n        var options = {\r\n            series: [{\r\n                name: 'Net Profit',\r\n                data: data1\r\n            }, {\r\n                name: 'Revenue',\r\n                data: data2\r\n            }],\r\n            chart: {\r\n                fontFamily: 'inherit',\r\n                type: 'bar',\r\n                height: height,\r\n                toolbar: {\r\n                    show: false\r\n                }\r\n            },\r\n            plotOptions: {\r\n                bar: {\r\n                    horizontal: false,\r\n                    columnWidth: ['35%'],\r\n                    endingShape: 'rounded'\r\n                },\r\n            },\r\n            legend: {\r\n                show: false\r\n            },\r\n            dataLabels: {\r\n                enabled: false\r\n            },\r\n            stroke: {\r\n                show: true,\r\n                width: 2,\r\n                colors: ['transparent']\r\n            },\r\n            xaxis: {\r\n                categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],\r\n                axisBorder: {\r\n                    show: false,\r\n                },\r\n                axisTicks: {\r\n                    show: false\r\n                },\r\n                labels: {\r\n                    style: {\r\n                        colors: KTUtil.getCssVariableValue('--bs-gray-400'),\r\n                        fontSize: '12px'\r\n                    }\r\n                }\r\n            },\r\n            yaxis: {\r\n                labels: {\r\n                    style: {\r\n                        colors: KTUtil.getCssVariableValue('--bs-gray-400'),\r\n                        fontSize: '12px'\r\n                    }\r\n                }\r\n            },\r\n            fill: {\r\n                opacity: 1\r\n            },\r\n            states: {\r\n                normal: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                hover: {\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                },\r\n                active: {\r\n                    allowMultipleDataPointsSelection: false,\r\n                    filter: {\r\n                        type: 'none',\r\n                        value: 0\r\n                    }\r\n                }\r\n            },\r\n            tooltip: {\r\n                style: {\r\n                    fontSize: '12px'\r\n                },\r\n                y: {\r\n                    formatter: function (val) {\r\n                        return \"$\" + val + \" thousands\"\r\n                    }\r\n                }\r\n            },\r\n            colors: [KTUtil.getCssVariableValue('--bs-primary'), KTUtil.getCssVariableValue('--bs-gray-200')],\r\n            grid: {\r\n                borderColor: KTUtil.getCssVariableValue('--bs-gray-200'),\r\n                strokeDashArray: 4,\r\n                yaxis: {\r\n                    lines: {\r\n                        show: true\r\n                    }\r\n                }\r\n            }\r\n        };\r\n\r\n        var chart = new ApexCharts(element, options);\r\n\r\n        var init = false;\r\n        var tab = document.querySelector(tabSelector);\r\n        \r\n        if (initByDefault === true) {\r\n            chart.render();\r\n            init = true;\r\n        }        \r\n\r\n        tab.addEventListener('shown.bs.tab', function (event) {\r\n            if (init == false) {\r\n                chart.render();\r\n                init = true;\r\n            }\r\n        })\r\n    } \r\n\r\n    // Public methods\r\n    return {\r\n        init: function () {\r\n            initChart('#kt_security_summary_tab_hours_agents', '#kt_security_summary_chart_hours_agents', [50, 70, 90, 117, 80, 65, 80, 90, 115, 95, 70, 84], [50, 70, 90, 117, 80, 65, 70, 90, 115, 95, 70, 84], true);\r\n            initChart('#kt_security_summary_tab_hours_clients', '#kt_security_summary_chart_hours_clients', [50, 70, 90, 117, 80, 65, 80, 90, 115, 95, 70, 84], [50, 70, 90, 117, 80, 65, 80, 90, 115, 95, 70, 84], false);\r\n           \r\n            initChart('#kt_security_summary_tab_day', '#kt_security_summary_chart_day_agents', [50, 70, 80, 100, 90, 65, 80, 90, 115, 95, 70, 84], [50, 70, 90, 117, 60, 65, 80, 90, 100, 95, 70, 84], false);\r\n            initChart('#kt_security_summary_tab_day_clients', '#kt_security_summary_chart_day_clients', [50, 70, 100, 90, 80, 65, 80, 90, 115, 95, 70, 84], [50, 70, 90, 115, 80, 65, 80, 90, 115, 95, 70, 84], false);\r\n           \r\n            initChart('#kt_security_summary_tab_week', '#kt_security_summary_chart_week_agents', [50, 70, 75, 117, 80, 65, 80, 90, 115, 95, 50, 84], [50, 60, 90, 117, 80, 65, 80, 90, 115, 95, 70, 84], false);\r\n            initChart('#kt_security_summary_tab_week_clients', '#kt_security_summary_chart_week_clients', [50, 70, 90, 117, 80, 65, 80, 90, 100, 80, 70, 84], [50, 70, 90, 117, 80, 65, 80, 90, 100, 95, 70, 84], false);\r\n        }\r\n    }\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function() {\r\n    KTAccountSecuritySummary.init();\r\n});\r\n"], "names": [], "sourceRoot": ""}