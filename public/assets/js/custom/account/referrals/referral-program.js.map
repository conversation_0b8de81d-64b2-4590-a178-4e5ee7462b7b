{"version": 3, "file": "js/custom/account/referrals/referral-program.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,UAAU;AACvB;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC", "sources": ["webpack://keenthemes/../../../themes/metronic/html/demo3/src/js/custom/account/referrals/referral-program.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTAccountReferralsReferralProgram = function () {\r\n    // Private functions\r\n\r\n    var initReferralProgrammClipboard = function() {\r\n        var button = document.querySelector('#kt_referral_program_link_copy_btn');\r\n        var input = document.querySelector('#kt_referral_link_input');\r\n        var clipboard = new ClipboardJS(button);\r\n\r\n        clipboard.on('success', function(e) {\r\n            var buttonCaption = button.innerHTML;\r\n            //Add bgcolor\r\n            input.classList.add('bg-success');\r\n            input.classList.add('text-inverse-success');\r\n\r\n            button.innerHTML = 'Copied!';\r\n\r\n            setTimeout(function() {\r\n                button.innerHTML = buttonCaption;\r\n\r\n                // Remove bgcolor\r\n                input.classList.remove('bg-success'); \r\n                input.classList.remove('text-inverse-success'); \r\n            }, 3000);  // 3seconds\r\n\r\n            e.clearSelection();\r\n        });\r\n    }\r\n\r\n    // Public methods\r\n    return {\r\n        init: function () {\r\n            initReferralProgrammClipboard();\r\n        }\r\n    }\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function() {\r\n    KTAccountReferralsReferralProgram.init();\r\n});\r\n"], "names": [], "sourceRoot": ""}