{"version": 3, "file": "js/custom/account/settings/close-account.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B;AAC7B,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B;AAC7B;AACA,qBAAqB;AACrB;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC", "sources": ["webpack://keenthemes/../../../themes/metronic/html/demo3/src/js/custom/account/settings/close-account.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTAccountClose = function () {\r\n    // Private variables\r\n    var form;\r\n    var validation;\r\n    var submitButton;\r\n\r\n    // Private functions\r\n    var initValidation = function () {\r\n        // Init form validation rules. For more info check the FormValidation plugin's official documentation:https://formvalidation.io/\r\n        validation = FormValidation.formValidation(\r\n            form,\r\n            {\r\n                fields: {\r\n                    close: {\r\n                        validators: {\r\n                            notEmpty: {\r\n                                message: 'Please check the box to close your account'\r\n                            }\r\n                        }\r\n                    }\r\n                },\r\n                plugins: {\r\n                    trigger: new FormValidation.plugins.Trigger(),\r\n                    submitButton: new FormValidation.plugins.SubmitButton(),\r\n                    //defaultSubmit: new FormValidation.plugins.DefaultSubmit(), // Uncomment this line to enable normal button submit after form validation\r\n                    bootstrap: new FormValidation.plugins.Bootstrap5({\r\n                        rowSelector: '.fv-row',\r\n                        eleInvalidClass: '',\r\n                        eleValidClass: ''\r\n                    })\r\n                }\r\n            }\r\n        );\r\n    }\r\n\r\n    var handleForm = function () {\r\n        submitButton.addEventListener('click', function (e) {\r\n            e.preventDefault();\r\n\r\n            validation.validate().then(function (status) {\r\n                if (status == 'Valid') {\r\n\r\n                    swal.fire({\r\n                        text: \"Are you sure you would like to close your account?\",\r\n                        icon: \"warning\",\r\n                        buttonsStyling: false,\r\n                        showDenyButton: true,\r\n                        confirmButtonText: \"Yes\",\r\n                        denyButtonText: 'No',\r\n                        customClass: {\r\n                            confirmButton: \"btn btn-light-primary\",\r\n                            denyButton: \"btn btn-danger\"\r\n                        }\r\n                    }).then((result) => {\r\n                        if (result.isConfirmed) {\r\n                            Swal.fire({\r\n                                text: 'Your account has been closed.', \r\n                                icon: 'success',\r\n                                confirmButtonText: \"Ok\",\r\n                                buttonsStyling: false,\r\n                                customClass: {\r\n                                    confirmButton: \"btn btn-light-primary\"\r\n                                }\r\n                            })\r\n                        } else if (result.isDenied) {\r\n                            Swal.fire({\r\n                                text: 'Account not closed.', \r\n                                icon: 'info',\r\n                                confirmButtonText: \"Ok\",\r\n                                buttonsStyling: false,\r\n                                customClass: {\r\n                                    confirmButton: \"btn btn-light-primary\"\r\n                                }\r\n                            })\r\n                        }\r\n                    });\r\n\r\n                } else {\r\n                    swal.fire({\r\n                        text: \"Sorry, looks like there are some errors detected, please try again.\",\r\n                        icon: \"error\",\r\n                        buttonsStyling: false,\r\n                        confirmButtonText: \"Ok, got it!\",\r\n                        customClass: {\r\n                            confirmButton: \"btn btn-light-primary\"\r\n                        }\r\n                    });\r\n                }\r\n            });\r\n        });\r\n    }\r\n\r\n    // Public methods\r\n    return {\r\n        init: function () {\r\n            form = document.querySelector('#kt_account_close_form');\r\n            submitButton = document.querySelector('#kt_account_close_submit');\r\n\r\n            initValidation();\r\n            handleForm();\r\n        }\r\n    }\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function() {\r\n    KTAccountClose.init();\r\n});\r\n"], "names": [], "sourceRoot": ""}