"use strict";var KTAccountClose=function(){var t,o,n;return{init:function(){t=document.querySelector("#kt_account_close_form"),n=document.querySelector("#kt_account_close_submit"),o=FormValidation.formValidation(t,{fields:{close:{validators:{notEmpty:{message:"Please check the box to close your account"}}}},plugins:{trigger:new FormValidation.plugins.Trigger,submitButton:new FormValidation.plugins.SubmitButton,bootstrap:new FormValidation.plugins.Bootstrap5({rowSelector:".fv-row",eleInvalidClass:"",eleValidClass:""})}}),n.addEventListener("click",(function(t){t.preventDefault(),o.validate().then((function(t){"Valid"==t?swal.fire({text:"Are you sure you would like to close your account?",icon:"warning",buttonsStyling:!1,showDenyButton:!0,confirmButtonText:"Yes",denyButtonText:"No",customClass:{confirmButton:"btn btn-light-primary",denyButton:"btn btn-danger"}}).then((t=>{t.isConfirmed?Swal.fire({text:"Your account has been closed.",icon:"success",confirmButtonText:"Ok",buttonsStyling:!1,customClass:{confirmButton:"btn btn-light-primary"}}):t.isDenied&&Swal.fire({text:"Account not closed.",icon:"info",confirmButtonText:"Ok",buttonsStyling:!1,customClass:{confirmButton:"btn btn-light-primary"}})})):swal.fire({text:"Sorry, looks like there are some errors detected, please try again.",icon:"error",buttonsStyling:!1,confirmButtonText:"Ok, got it!",customClass:{confirmButton:"btn btn-light-primary"}})}))}))}}}();KTUtil.onDOMContentLoaded((function(){KTAccountClose.init()}));