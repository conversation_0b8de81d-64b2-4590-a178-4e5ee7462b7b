{"version": 3, "file": "js/custom/account/settings/overview.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC", "sources": ["webpack://keenthemes/../../../themes/metronic/html/demo3/src/js/custom/account/settings/overview.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTAccountSettingsOverview = function () {\r\n    // Private functions\r\n    var initSettings = function() {\r\n\r\n    }\r\n\r\n    // Public methods\r\n    return {\r\n        init: function () {\r\n            initSettings();\r\n        }\r\n    }\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function() {\r\n    KTAccountSettingsOverview.init();\r\n});\r\n"], "names": [], "sourceRoot": ""}