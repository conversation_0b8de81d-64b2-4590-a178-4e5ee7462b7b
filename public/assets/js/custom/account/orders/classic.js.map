{"version": 3, "file": "js/custom/account/orders/classic.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,yEAAyE;AACzE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC,E", "sources": ["webpack://keenthemes/../../../themes/metronic/html/demo3/src/js/custom/account/orders/classic.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTDatatablesClassic = function () {\r\n    // Private functions\r\n\r\n    var initClassic = function () {\r\n\r\n        // Set date data order\r\n        const table = document.getElementById('kt_orders_classic');\r\n        const tableRows = table.querySelectorAll('tbody tr');\r\n\r\n        tableRows.forEach(row => {\r\n            const dateRow = row.querySelectorAll('td');\r\n            const realDate = moment(dateRow[1].innerHTML, \"MMM D, YYYY\").format('x');\r\n            dateRow[1].setAttribute('data-order', realDate);\r\n        });\r\n\r\n        // Init datatable --- more info on datatables: https://datatables.net/manual/\r\n        const datatable = $(table).DataTable({\r\n            \"info\": false,\r\n            'order': []\r\n        });\r\n\r\n        // Filter dropdown elements\r\n        const filterOrders = document.getElementById('kt_filter_orders');\r\n        const filterYear = document.getElementById('kt_filter_year');\r\n\r\n        // Filter by order status --- official docs reference: https://datatables.net/reference/api/search()\r\n        filterOrders.addEventListener('change', function (e) {\r\n            datatable.column(3).search(e.target.value).draw();\r\n        });\r\n\r\n        // Filter by date --- official docs reference: https://momentjs.com/docs/\r\n        var minDate;\r\n        var maxDate;\r\n        filterYear.addEventListener('change', function (e) {\r\n            const value = e.target.value;\r\n            switch (value) {\r\n                case 'thisyear': {\r\n                    minDate = moment().startOf('year').format('x');\r\n                    maxDate = moment().endOf('year').format('x');\r\n                    datatable.draw();\r\n                    break;\r\n                }\r\n                case 'thismonth': {\r\n                    minDate = moment().startOf('month').format('x');\r\n                    maxDate = moment().endOf('month').format('x');\r\n                    datatable.draw();\r\n                    break;\r\n                }\r\n                case 'lastmonth': {\r\n                    minDate = moment().subtract(1, 'months').startOf('month').format('x');\r\n                    maxDate = moment().subtract(1, 'months').endOf('month').format('x');\r\n                    datatable.draw();\r\n                    break;\r\n                }\r\n                case 'last90days': {\r\n                    minDate = moment().subtract(30, 'days').format('x');\r\n                    maxDate = moment().format('x');\r\n                    datatable.draw();\r\n                    break;\r\n                }\r\n                default: {\r\n                    minDate = moment().subtract(100, 'years').startOf('month').format('x');\r\n                    maxDate = moment().add(1, 'months').endOf('month').format('x');\r\n                    datatable.draw();\r\n                    break;\r\n                }\r\n            }\r\n        });\r\n        \r\n        // Date range filter --- offical docs reference: https://datatables.net/examples/plug-ins/range_filtering.html\r\n        $.fn.dataTable.ext.search.push(\r\n            function (settings, data, dataIndex) {\r\n                var min = minDate;\r\n                var max = maxDate;\r\n                var date = parseFloat(moment(data[1]).format('x')) || 0; // use data for the age column\r\n\r\n                if ((isNaN(min) && isNaN(max)) ||\r\n                    (isNaN(min) && date <= max) ||\r\n                    (min <= date && isNaN(max)) ||\r\n                    (min <= date && date <= max)) {\r\n                    return true;\r\n                }\r\n                return false;\r\n            }\r\n        );\r\n\r\n        // Search --- official docs reference: https://datatables.net/reference/api/search()\r\n        var filterSearch = document.getElementById('kt_filter_search');\r\n        filterSearch.addEventListener('keyup', function (e) {\r\n            datatable.search(e.target.value).draw();\r\n        });\r\n    }\r\n\r\n    // Public methods\r\n    return {\r\n        init: function () {\r\n            initClassic();\r\n        }\r\n    }\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function() {\r\n    KTDatatablesClassic.init();\r\n});"], "names": [], "sourceRoot": ""}