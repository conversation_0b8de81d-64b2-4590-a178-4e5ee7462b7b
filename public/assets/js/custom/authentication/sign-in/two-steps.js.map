{"version": 3, "file": "js/custom/authentication/sign-in/two-steps.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA,6BAA6B;AAC7B;AACA,qBAAqB;AACrB,iBAAiB;AACjB,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA,iBAAiB;AACjB;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC,E", "sources": ["webpack://keenthemes/../../../themes/metronic/html/demo3/src/js/custom/authentication/sign-in/two-steps.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class Definition\r\nvar KTSigninTwoSteps = function() {\r\n    // Elements\r\n    var form;\r\n    var submitButton;\r\n\r\n    // Handle form\r\n    var handleForm = function(e) {        \r\n        // Handle form submit\r\n        submitButton.addEventListener('click', function (e) {\r\n            e.preventDefault();\r\n\r\n            var validated = true;\r\n\r\n            var inputs = [].slice.call(form.querySelectorAll('input[maxlength=\"1\"]'));\r\n            inputs.map(function (input) {\r\n                if (input.value === '' || input.value.length === 0) {\r\n                    validated = false;\r\n                }\r\n            });\r\n\r\n            if (validated === true) {\r\n                // Show loading indication\r\n                submitButton.setAttribute('data-kt-indicator', 'on');\r\n\r\n                // Disable button to avoid multiple click \r\n                submitButton.disabled = true;\r\n\r\n                // Simulate ajax request\r\n                setTimeout(function() {\r\n                    // Hide loading indication\r\n                    submitButton.removeAttribute('data-kt-indicator');\r\n\r\n                    // Enable button\r\n                    submitButton.disabled = false;\r\n\r\n                    // Show message popup. For more info check the plugin's official documentation: https://sweetalert2.github.io/\r\n                    Swal.fire({\r\n                        text: \"You have been successfully verified!\",\r\n                        icon: \"success\",\r\n                        buttonsStyling: false,\r\n                        confirmButtonText: \"Ok, got it!\",\r\n                        customClass: {\r\n                            confirmButton: \"btn btn-primary\"\r\n                        }\r\n                    }).then(function (result) {\r\n                        if (result.isConfirmed) { \r\n                            inputs.map(function (input) {\r\n                                input.value = '';\r\n                            });\r\n                        }\r\n                    });\r\n                }, 1000); \r\n            } else {\r\n                swal.fire({\r\n                    text: \"Please enter valid securtiy code and try again.\",\r\n                    icon: \"error\",\r\n                    buttonsStyling: false,\r\n                    confirmButtonText: \"Ok, got it!\",\r\n                    customClass: {\r\n                        confirmButton: \"btn fw-bold btn-light-primary\"\r\n                    }\r\n                }).then(function() {\r\n                    KTUtil.scrollTop();\r\n                });\r\n            }\r\n        });\r\n    }\r\n\r\n    // Public functions\r\n    return {\r\n        // Initialization\r\n        init: function() {\r\n            form = document.querySelector('#kt_sing_in_two_steps_form');\r\n            submitButton = document.querySelector('#kt_sing_in_two_steps_submit');\r\n\r\n            handleForm();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function() {\r\n    KTSigninTwoSteps.init();\r\n});"], "names": [], "sourceRoot": ""}