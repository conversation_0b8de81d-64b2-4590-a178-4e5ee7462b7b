{"version": 3, "file": "js/custom/authentication/sign-in/general.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA,iDAAiD;AACjD;AACA,yBAAyB;AACzB,qBAAqB;AACrB,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA,aAAa;AACb,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC", "sources": ["webpack://keenthemes/../../../themes/metronic/html/demo3/src/js/custom/authentication/sign-in/general.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTSigninGeneral = function() {\r\n    // Elements\r\n    var form;\r\n    var submitButton;\r\n    var validator;\r\n\r\n    // Handle form\r\n    var handleForm = function(e) {\r\n        // Init form validation rules. For more info check the FormValidation plugin's official documentation:https://formvalidation.io/\r\n        validator = FormValidation.formValidation(\r\n\t\t\tform,\r\n\t\t\t{\r\n\t\t\t\tfields: {\t\t\t\t\t\r\n\t\t\t\t\t'email': {\r\n                        validators: {\r\n\t\t\t\t\t\t\tnotEmpty: {\r\n\t\t\t\t\t\t\t\tmessage: 'Email address is required'\r\n\t\t\t\t\t\t\t},\r\n                            emailAddress: {\r\n\t\t\t\t\t\t\t\tmessage: 'The value is not a valid email address'\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n                    'password': {\r\n                        validators: {\r\n                            notEmpty: {\r\n                                message: 'The password is required'\r\n                            }\r\n                        }\r\n                    } \r\n\t\t\t\t},\r\n\t\t\t\tplugins: {\r\n\t\t\t\t\ttrigger: new FormValidation.plugins.Trigger(),\r\n\t\t\t\t\tbootstrap: new FormValidation.plugins.Bootstrap5({\r\n                        rowSelector: '.fv-row'\r\n                    })\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t);\t\t\r\n\r\n        // Handle form submit\r\n        submitButton.addEventListener('click', function (e) {\r\n            // Prevent button default action\r\n            e.preventDefault();\r\n\r\n            // Validate form\r\n            validator.validate().then(function (status) {\r\n                if (status == 'Valid') {\r\n                    // Show loading indication\r\n                    submitButton.setAttribute('data-kt-indicator', 'on');\r\n\r\n                    // Disable button to avoid multiple click \r\n                    submitButton.disabled = true;\r\n                    \r\n\r\n                    // Simulate ajax request\r\n                    setTimeout(function() {\r\n                        // Hide loading indication\r\n                        submitButton.removeAttribute('data-kt-indicator');\r\n\r\n                        // Enable button\r\n                        submitButton.disabled = false;\r\n\r\n                        // Show message popup. For more info check the plugin's official documentation: https://sweetalert2.github.io/\r\n                        Swal.fire({\r\n                            text: \"You have successfully logged in!\",\r\n                            icon: \"success\",\r\n                            buttonsStyling: false,\r\n                            confirmButtonText: \"Ok, got it!\",\r\n                            customClass: {\r\n                                confirmButton: \"btn btn-primary\"\r\n                            }\r\n                        }).then(function (result) {\r\n                            if (result.isConfirmed) { \r\n                                form.querySelector('[name=\"email\"]').value= \"\";\r\n                                form.querySelector('[name=\"password\"]').value= \"\";                                \r\n                                //form.submit(); // submit form\r\n                            }\r\n                        });\r\n                    }, 2000);   \t\t\t\t\t\t\r\n                } else {\r\n                    // Show error popup. For more info check the plugin's official documentation: https://sweetalert2.github.io/\r\n                    Swal.fire({\r\n                        text: \"Sorry, looks like there are some errors detected, please try again.\",\r\n                        icon: \"error\",\r\n                        buttonsStyling: false,\r\n                        confirmButtonText: \"Ok, got it!\",\r\n                        customClass: {\r\n                            confirmButton: \"btn btn-primary\"\r\n                        }\r\n                    });\r\n                }\r\n            });\r\n\t\t});\r\n    }\r\n\r\n    // Public functions\r\n    return {\r\n        // Initialization\r\n        init: function() {\r\n            form = document.querySelector('#kt_sign_in_form');\r\n            submitButton = document.querySelector('#kt_sign_in_submit');\r\n            \r\n            handleForm();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function() {\r\n    KTSigninGeneral.init();\r\n});\r\n"], "names": [], "sourceRoot": ""}