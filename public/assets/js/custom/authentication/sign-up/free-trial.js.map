{"version": 3, "file": "js/custom/authentication/sign-up/free-trial.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,6BAA6B;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA,6BAA6B;AAC7B;AACA;AACA;AACA,iCAAiC;AACjC;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA,+CAA+C;AAC/C,wDAAwD;AACxD;AACA;AACA,yBAAyB;AACzB,qBAAqB;AACrB,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA,OAAO;AACP,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA,C", "sources": ["webpack://keenthemes/../../../themes/metronic/html/demo3/src/js/custom/authentication/sign-up/free-trial.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class Definition\r\nvar KTSignupFreeTrial = function() {\r\n    // Elements\r\n    var form;\r\n    var submitButton;\r\n    var validator;\r\n    var passwordMeter;\r\n\r\n    // Handle form\r\n    var handleForm = function(e) {\r\n        // Init form validation rules. For more info check the FormValidation plugin's official documentation:https://formvalidation.io/\r\n        validator = FormValidation.formValidation(\r\n\t\t\tform,\r\n\t\t\t{\r\n\t\t\t\tfields: {\t\t\t\t\t \r\n\t\t\t\t\t'email': {\r\n                        validators: {\r\n\t\t\t\t\t\t\tnotEmpty: {\r\n\t\t\t\t\t\t\t\tmessage: 'Email address is required'\r\n\t\t\t\t\t\t\t},\r\n                            emailAddress: {\r\n\t\t\t\t\t\t\t\tmessage: 'The value is not a valid email address'\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n                    'password': {\r\n                        validators: {\r\n                            notEmpty: {\r\n                                message: 'The password is required'\r\n                            },\r\n                            callback: {\r\n                                message: 'Please enter valid password',\r\n                                callback: function(input) {\r\n                                    if (input.value.length > 0) {\r\n                                        return validatePassword();\r\n                                    }\r\n                                }\r\n                            }\r\n                        }\r\n                    },\r\n                    'confirm-password': {\r\n                        validators: {\r\n                            notEmpty: {\r\n                                message: 'The password confirmation is required'\r\n                            },\r\n                            identical: {\r\n                                compare: function() {\r\n                                    return form.querySelector('[name=\"password\"]').value;\r\n                                },\r\n                                message: 'The password and its confirm are not the same'\r\n                            }\r\n                        }\r\n                    },\r\n                    'toc': {\r\n                        validators: {\r\n                            notEmpty: {\r\n                                message: 'You must accept the terms and conditions'\r\n                            }\r\n                        }\r\n                    }\r\n                },\r\n                plugins: {\r\n\t\t\t\t\ttrigger: new FormValidation.plugins.Trigger({\r\n                        event: {\r\n                            password: false\r\n                        }  \r\n                    }),\r\n\t\t\t\t\tbootstrap: new FormValidation.plugins.Bootstrap5({\r\n                        rowSelector: '.fv-row',\r\n                        eleInvalidClass: '',\r\n                        eleValidClass: ''\r\n                    })\r\n                }\t\t\t \r\n\t\t\t}\r\n\t\t);\r\n\r\n        submitButton.addEventListener('click', function (e) {\r\n            e.preventDefault();\r\n\r\n            validator.revalidateField('password');\r\n\r\n            validator.validate().then(function(status) {\r\n\t\t        if (status == 'Valid') {\r\n                    // Show loading indication\r\n                    submitButton.setAttribute('data-kt-indicator', 'on');\r\n\r\n                    // Disable button to avoid multiple click \r\n                    submitButton.disabled = true;\r\n\r\n                    // Simulate ajax request\r\n                    setTimeout(function() {\r\n                        // Hide loading indication\r\n                        submitButton.removeAttribute('data-kt-indicator');\r\n\r\n                        // Enable button\r\n                        submitButton.disabled = false;\r\n\r\n                        // Show message popup. For more info check the plugin's official documentation: https://sweetalert2.github.io/\r\n                        Swal.fire({\r\n                            text: \"You have successfully registered!\",\r\n                            icon: \"success\",\r\n                            buttonsStyling: false,\r\n                            confirmButtonText: \"Ok, got it!\",\r\n                            customClass: {\r\n                                confirmButton: \"btn btn-primary\"\r\n                            }\r\n                        }).then(function (result) {\r\n                            if (result.isConfirmed) { \r\n                                form.reset();  // reset form                    \r\n                                passwordMeter.reset();  // reset password meter\r\n                                //form.submit();\r\n                            }\r\n                        });\r\n                    }, 1500);   \t\t\t\t\t\t\r\n                } else {\r\n                    // Show error popup. For more info check the plugin's official documentation: https://sweetalert2.github.io/\r\n                    Swal.fire({\r\n                        text: \"Sorry, looks like there are some errors detected, please try again.\",\r\n                        icon: \"error\",\r\n                        buttonsStyling: false,\r\n                        confirmButtonText: \"Ok, got it!\",\r\n                        customClass: {\r\n                            confirmButton: \"btn btn-primary\"\r\n                        }\r\n                    });\r\n                }\r\n\t\t    });\r\n        });\r\n\r\n        form.querySelector('input[name=\"password\"]').addEventListener('input', function() {\r\n            if (this.value.length > 0) {\r\n                validator.updateFieldStatus('password', 'NotValidated');\r\n            }\r\n        });\r\n    }\r\n\r\n    // Password input validation\r\n    var validatePassword = function() {\r\n        return  (passwordMeter.getScore() === 100);\r\n    }\r\n\r\n    // Public functions\r\n    return {\r\n        // Initialization\r\n        init: function() {\r\n            form = document.querySelector('#kt_free_trial_form');\r\n            submitButton = document.querySelector('#kt_free_trial_submit');\r\n            passwordMeter = KTPasswordMeter.getInstance(form.querySelector('[data-kt-password-meter=\"true\"]'));\r\n\r\n            handleForm();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function() {\r\n    KTSignupFreeTrial.init();\r\n});\r\n\r\n\r\n "], "names": [], "sourceRoot": ""}