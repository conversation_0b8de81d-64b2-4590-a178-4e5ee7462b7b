{"version": 3, "file": "js/custom/authentication/sign-up/coming-soon.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA,yBAAyB;AACzB,qBAAqB;AACrB,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA,aAAa;AACb,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC", "sources": ["webpack://keenthemes/../../../themes/metronic/html/demo3/src/js/custom/authentication/sign-up/coming-soon.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class Definition\r\nvar KTSignupComingSoon = function() {\r\n    // Elements\r\n    var form;\r\n    var submitButton;\r\n\tvar validator; \r\n\r\n    var counterDays;\r\n    var counterHours;\r\n    var counterMinutes;\r\n    var counterSeconds;\r\n\r\n    var handleForm = function(e) {\r\n        var validation;\t\t \r\n\r\n        // Init form validation rules. For more info check the FormValidation plugin's official documentation:https://formvalidation.io/\r\n        validator = FormValidation.formValidation(\r\n\t\t\tform,\r\n\t\t\t{\r\n\t\t\t\tfields: {\t\t\t\t\t\r\n\t\t\t\t\t'email': {\r\n                        validators: {\r\n\t\t\t\t\t\t\tnotEmpty: {\r\n\t\t\t\t\t\t\t\tmessage: 'Email address is required'\r\n\t\t\t\t\t\t\t},\r\n                            emailAddress: {\r\n\t\t\t\t\t\t\t\tmessage: 'The value is not a valid email address'\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} \r\n\t\t\t\t},\r\n\t\t\t\tplugins: {\r\n\t\t\t\t\ttrigger: new FormValidation.plugins.Trigger(),\r\n\t\t\t\t\tbootstrap: new FormValidation.plugins.Bootstrap5({\r\n                        rowSelector: '.fv-row',\r\n                        eleInvalidClass: '',\r\n                        eleValidClass: ''\r\n                    })\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t);\t\t\r\n\r\n        submitButton.addEventListener('click', function (e) {\r\n            e.preventDefault();\r\n\r\n            // Validate form\r\n            validator.validate().then(function (status) {\r\n                if (status == 'Valid') {\r\n                    // Show loading indication\r\n                    submitButton.setAttribute('data-kt-indicator', 'on');\r\n\r\n                    // Disable button to avoid multiple click \r\n                    submitButton.disabled = true;\r\n\r\n                    // Simulate ajax request\r\n                    setTimeout(function() {\r\n                        // Hide loading indication\r\n                        submitButton.removeAttribute('data-kt-indicator');\r\n\r\n                        // Enable button\r\n                        submitButton.disabled = false;\r\n\r\n                        // Show message popup. For more info check the plugin's official documentation: https://sweetalert2.github.io/\r\n                        Swal.fire({\r\n                            text: \"You have successfully subscribed !\",\r\n                            icon: \"success\",\r\n                            buttonsStyling: false,\r\n                            confirmButtonText: \"Ok, got it!\",\r\n                            customClass: {\r\n                                confirmButton: \"btn btn-primary\"\r\n                            }\r\n                        }).then(function (result) {\r\n                            if (result.isConfirmed) { \r\n                                form.querySelector('[name=\"email\"]').value= \"\";                            \r\n                                //form.submit();\r\n                            }\r\n                        });\r\n                    }, 2000);   \t\t\t\t\t\t\r\n                } else {\r\n                    // Show error popup. For more info check the plugin's official documentation: https://sweetalert2.github.io/\r\n                    Swal.fire({\r\n                        text: \"Sorry, looks like there are some errors detected, please try again.\",\r\n                        icon: \"error\",\r\n                        buttonsStyling: false,\r\n                        confirmButtonText: \"Ok, got it!\",\r\n                        customClass: {\r\n                            confirmButton: \"btn btn-primary\"\r\n                        }\r\n                    });\r\n                }\r\n            });\r\n\t\t});\r\n    }\r\n\r\n    var initCounter = function() {\r\n        // Set the date we're counting down to\r\n        var currentTime = new Date(); \r\n        var countDownDate = new Date(currentTime.getTime() + 1000 * 60 * 60 * 24 * 15 + 1000 * 60 * 60 * 10 + 1000 * 60 * 15).getTime();\r\n\r\n        var count = function() {\r\n            // Get todays date and time\r\n            var now = new Date().getTime();\r\n\r\n            // Find the distance between now an the count down date\r\n            var distance = countDownDate - now;\r\n\r\n            // Time calculations for days, hours, minutes and seconds\r\n            var days = Math.floor(distance / (1000 * 60 * 60 * 24));\r\n            var hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));\r\n            var minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));\r\n            var seconds = Math.floor((distance % (1000 * 60)) / 1000);\r\n\r\n            // Display the result\r\n            counterDays.innerHTML = days;\r\n            counterHours.innerHTML = hours;\r\n            counterMinutes.innerHTML = minutes;\r\n            counterSeconds.innerHTML = seconds;\r\n        };\r\n\r\n        // Update the count down every 1 second\r\n        var x = setInterval(count, 1000);\r\n\r\n        // Initial count\r\n        count();\r\n    }\r\n\r\n    // Public Functions\r\n    return {\r\n        // public functions\r\n        init: function() {\r\n            form = document.querySelector('#kt_coming_soon_form');\r\n            submitButton = document.querySelector('#kt_coming_soon_submit');\r\n            counterDays = document.querySelector('#kt_coming_soon_counter_days');\r\n            counterHours = document.querySelector('#kt_coming_soon_counter_hours');\r\n            counterMinutes = document.querySelector('#kt_coming_soon_counter_minutes');\r\n            counterSeconds = document.querySelector('#kt_coming_soon_counter_seconds');\r\n\r\n            handleForm();\r\n            initCounter();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function() {\r\n    KTSignupComingSoon.init();\r\n});\r\n"], "names": [], "sourceRoot": ""}