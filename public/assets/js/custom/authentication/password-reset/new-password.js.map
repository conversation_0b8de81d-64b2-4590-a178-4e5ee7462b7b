{"version": 3, "file": "js/custom/authentication/password-reset/new-password.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA,6BAA6B;AAC7B;AACA;AACA;AACA,iCAAiC;AACjC;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA,wDAAwD;AACxD;AACA;AACA,yBAAyB;AACzB,qBAAqB;AACrB,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA,OAAO;AACP,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC", "sources": ["webpack://keenthemes/../../../themes/metronic/html/demo3/src/js/custom/authentication/password-reset/new-password.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class Definition\r\nvar KTPasswordResetNewPassword = function() {\r\n    // Elements\r\n    var form;\r\n    var submitButton;\r\n    var validator;\r\n    var passwordMeter;\r\n\r\n    var handleForm = function(e) {\r\n        // Init form validation rules. For more info check the FormValidation plugin's official documentation:https://formvalidation.io/\r\n        validator = FormValidation.formValidation(\r\n\t\t\tform,\r\n\t\t\t{\r\n\t\t\t\tfields: {\t\t\t\t\t \r\n                    'password': {\r\n                        validators: {\r\n                            notEmpty: {\r\n                                message: 'The password is required'\r\n                            },\r\n                            callback: {\r\n                                message: 'Please enter valid password',\r\n                                callback: function(input) {\r\n                                    if (input.value.length > 0) {        \r\n                                        return validatePassword();\r\n                                    }\r\n                                }\r\n                            }\r\n                        }\r\n                    },\r\n                    'confirm-password': {\r\n                        validators: {\r\n                            notEmpty: {\r\n                                message: 'The password confirmation is required'\r\n                            },\r\n                            identical: {\r\n                                compare: function() {\r\n                                    return form.querySelector('[name=\"password\"]').value;\r\n                                },\r\n                                message: 'The password and its confirm are not the same'\r\n                            }\r\n                        }\r\n                    },\r\n                    'toc': {\r\n                        validators: {\r\n                            notEmpty: {\r\n                                message: 'You must accept the terms and conditions'\r\n                            }\r\n                        }\r\n                    }\r\n\t\t\t\t},\r\n\t\t\t\tplugins: {\r\n\t\t\t\t\ttrigger: new FormValidation.plugins.Trigger({\r\n                        event: {\r\n                            password: false\r\n                        }  \r\n                    }),\r\n\t\t\t\t\tbootstrap: new FormValidation.plugins.Bootstrap5({\r\n                        rowSelector: '.fv-row',\r\n                        eleInvalidClass: '',\r\n                        eleValidClass: ''\r\n                    })\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t);\r\n\r\n        submitButton.addEventListener('click', function (e) {\r\n            e.preventDefault();\r\n\r\n            validator.revalidateField('password');\r\n\r\n            validator.validate().then(function(status) {\r\n\t\t        if (status == 'Valid') {\r\n                    // Show loading indication\r\n                    submitButton.setAttribute('data-kt-indicator', 'on');\r\n\r\n                    // Disable button to avoid multiple click \r\n                    submitButton.disabled = true;\r\n\r\n                    // Simulate ajax request\r\n                    setTimeout(function() {\r\n                        // Hide loading indication\r\n                        submitButton.removeAttribute('data-kt-indicator');\r\n\r\n                        // Enable button\r\n                        submitButton.disabled = false;\r\n\r\n                        // Show message popup. For more info check the plugin's official documentation: https://sweetalert2.github.io/\r\n                        Swal.fire({\r\n                            text: \"You have successfully reset your password!\",\r\n                            icon: \"success\",\r\n                            buttonsStyling: false,\r\n                            confirmButtonText: \"Ok, got it!\",\r\n                            customClass: {\r\n                                confirmButton: \"btn btn-primary\"\r\n                            }\r\n                        }).then(function (result) {\r\n                            if (result.isConfirmed) { \r\n                                form.querySelector('[name=\"password\"]').value= \"\";   \r\n                                form.querySelector('[name=\"confirm-password\"]').value= \"\";      \r\n                                passwordMeter.reset();  // reset password meter\r\n                                //form.submit();\r\n                            }\r\n                        });\r\n                    }, 1500);   \t\t\t\t\t\t\r\n                } else {\r\n                    // Show error popup. For more info check the plugin's official documentation: https://sweetalert2.github.io/\r\n                    Swal.fire({\r\n                        text: \"Sorry, looks like there are some errors detected, please try again.\",\r\n                        icon: \"error\",\r\n                        buttonsStyling: false,\r\n                        confirmButtonText: \"Ok, got it!\",\r\n                        customClass: {\r\n                            confirmButton: \"btn btn-primary\"\r\n                        }\r\n                    });\r\n                }\r\n\t\t    });\r\n        });\r\n\r\n        form.querySelector('input[name=\"password\"]').addEventListener('input', function() {\r\n            if (this.value.length > 0) {\r\n                validator.updateFieldStatus('password', 'NotValidated');\r\n            }\r\n        });\r\n    }\r\n\r\n    var validatePassword = function() {\r\n        \r\n\r\n        return  (passwordMeter.getScore() === 100);\r\n    }\r\n\r\n    // Public Functions\r\n    return {\r\n        // public functions\r\n        init: function() {\r\n            form = document.querySelector('#kt_new_password_form');\r\n            submitButton = document.querySelector('#kt_new_password_submit');\r\n            passwordMeter = KTPasswordMeter.getInstance(form.querySelector('[data-kt-password-meter=\"true\"]'));\r\n\r\n            handleForm();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function() {\r\n    KTPasswordResetNewPassword.init();\r\n});\r\n"], "names": [], "sourceRoot": ""}