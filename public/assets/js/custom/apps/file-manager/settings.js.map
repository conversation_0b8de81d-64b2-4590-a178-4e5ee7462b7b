{"version": 3, "file": "js/custom/apps/file-manager/settings.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC", "sources": ["webpack://keenthemes/../../../themes/metronic/html/demo3/src/js/custom/apps/file-manager/settings.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTAppFileManagerSettings = function () {\r\n    var form;\r\n\r\n\t// Private functions\r\n\tvar handleForm = function() {\r\n\t\tconst saveButton = form.querySelector('#kt_file_manager_settings_submit');\r\n\r\n        saveButton.addEventListener('click', e => {\r\n            e.preventDefault();\r\n\r\n            saveButton.setAttribute(\"data-kt-indicator\", \"on\");\r\n\r\n            // Simulate process for demo only\r\n            setTimeout(function(){\r\n                toastr.options = {\r\n                    \"closeButton\": true,\r\n                    \"debug\": false,\r\n                    \"newestOnTop\": false,\r\n                    \"progressBar\": false,\r\n                    \"positionClass\": \"toast-top-right\",\r\n                    \"preventDuplicates\": false,\r\n                    \"showDuration\": \"300\",\r\n                    \"hideDuration\": \"1000\",\r\n                    \"timeOut\": \"5000\",\r\n                    \"extendedTimeOut\": \"1000\",\r\n                    \"showEasing\": \"swing\",\r\n                    \"hideEasing\": \"linear\",\r\n                    \"showMethod\": \"fadeIn\",\r\n                    \"hideMethod\": \"fadeOut\"\r\n                };\r\n\r\n                toastr.success('File manager settings have been saved');\r\n\r\n                saveButton.removeAttribute(\"data-kt-indicator\");\r\n            }, 1000);\r\n        });\r\n\t}\r\n\r\n\t// Public methods\r\n\treturn {\r\n\t\tinit: function(element) {\r\n            form = document.querySelector('#kt_file_manager_settings');\r\n\r\n\t\t\thandleForm();\r\n        }\r\n\t};\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function () {\r\n    KTAppFileManagerSettings.init();\r\n});\r\n"], "names": [], "sourceRoot": ""}