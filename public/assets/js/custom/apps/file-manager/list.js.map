{"version": 3, "file": "js/custom/apps/file-manager/list.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wCAAwC;AACxC;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,kBAAkB;AACpC,kBAAkB,cAAc;AAChC,kBAAkB,cAAc;AAChC,kBAAkB,cAAc;AAChC,kBAAkB,gBAAgB;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,kBAAkB;AACpC,kBAAkB,cAAc;AAChC,kBAAkB,cAAc;AAChC,kBAAkB,cAAc;AAChC,kBAAkB,gBAAgB;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA,yBAAyB;AACzB,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA,iBAAiB;AACjB,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA,qBAAqB;AACrB,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+DAA+D;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC;AACjC;AACA,iFAAiF;AACjF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uDAAuD,WAAW;AAClE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B;AAC7B,0BAA0B;AAC1B;AACA;AACA;AACA,qBAAqB;AACrB;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC;AACjC;AACA;AACA;AACA;AACA;AACA,0CAA0C;AAC1C,4HAA4H,SAAS;AACrI;AACA;AACA;AACA;AACA,iCAAiC;AACjC,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC;AACjC;AACA,yBAAyB;AACzB;AACA,iBAAiB;AACjB;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB;AACtB,wGAAwG,UAAU;AAClH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4CAA4C;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,iDAAiD;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA,0CAA0C;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA,iBAAiB;AACjB,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B;AAC7B;AACA,kDAAkD;AAClD,sDAAsD;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qCAAqC;AACrC;AACA;AACA;AACA,6BAA6B;AAC7B,yBAAyB;AACzB,sBAAsB;AACtB;AACA;AACA,iBAAiB;AACjB;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC,E", "sources": ["webpack://keenthemes/../../../themes/metronic/html/demo3/src/js/custom/apps/file-manager/list.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTFileManagerList = function () {\r\n    // Define shared variables\r\n    var datatable;\r\n    var table\r\n\r\n    // Define template element variables\r\n    var uploadTemplate;\r\n    var renameTemplate;\r\n    var actionTemplate;\r\n    var checkboxTemplate;\r\n\r\n\r\n    // Private functions\r\n    const initTemplates = () => {\r\n        uploadTemplate = document.querySelector('[data-kt-filemanager-template=\"upload\"]');\r\n        renameTemplate = document.querySelector('[data-kt-filemanager-template=\"rename\"]');\r\n        actionTemplate = document.querySelector('[data-kt-filemanager-template=\"action\"]');\r\n        checkboxTemplate = document.querySelector('[data-kt-filemanager-template=\"checkbox\"]');\r\n    }\r\n\r\n    const initDatatable = () => {\r\n        // Set date data order\r\n        const tableRows = table.querySelectorAll('tbody tr');\r\n\r\n        tableRows.forEach(row => {\r\n            const dateRow = row.querySelectorAll('td');\r\n            const dateCol = dateRow[3]; // select date from 4th column in table\r\n            const realDate = moment(dateCol.innerHTML, \"DD MMM YYYY, LT\").format();\r\n            dateCol.setAttribute('data-order', realDate);\r\n        });\r\n\r\n        const foldersListOptions = {\r\n            \"info\": false,\r\n            'order': [],\r\n            \"scrollY\": \"700px\",\r\n            \"scrollCollapse\": true,\r\n            \"paging\": false,\r\n            'ordering': false,\r\n            'columns': [\r\n                { data: 'checkbox' },\r\n                { data: 'name' },\r\n                { data: 'size' },\r\n                { data: 'date' },\r\n                { data: 'action' },\r\n            ],\r\n            'language': {\r\n                emptyTable: `<div class=\"d-flex flex-column flex-center\">\r\n                    <img src=\"assets/media/illustrations/sketchy-1/5.png\" class=\"mw-400px\" />\r\n                    <div class=\"fs-1 fw-bolder text-dark\">No items found.</div>\r\n                    <div class=\"fs-6\">Start creating new folders or uploading a new file!</div>\r\n                </div>`\r\n            }\r\n        };\r\n\r\n        const filesListOptions = {\r\n            \"info\": false,\r\n            'order': [],\r\n            'pageLength': 10,\r\n            \"lengthChange\": false,\r\n            'ordering': false,\r\n            'columns': [\r\n                { data: 'checkbox' },\r\n                { data: 'name' },\r\n                { data: 'size' },\r\n                { data: 'date' },\r\n                { data: 'action' },\r\n            ],\r\n            'language': {\r\n                emptyTable: `<div class=\"d-flex flex-column flex-center\">\r\n                    <img src=\"assets/media/illustrations/sketchy-1/5.png\" class=\"mw-400px\" />\r\n                    <div class=\"fs-1 fw-bolder text-dark mb-4\">No items found.</div>\r\n                    <div class=\"fs-6\">Start creating new folders or uploading a new file!</div>\r\n                </div>`\r\n            },\r\n            conditionalPaging: true\r\n        };\r\n\r\n        // Define datatable options to load\r\n        var loadOptions;\r\n        if (table.getAttribute('data-kt-filemanager-table') === 'folders') {\r\n            loadOptions = foldersListOptions;\r\n        } else {\r\n            loadOptions = filesListOptions;\r\n        }\r\n\r\n        // Init datatable --- more info on datatables: https://datatables.net/manual/\r\n        datatable = $(table).DataTable(loadOptions);\r\n\r\n        // Re-init functions on every table re-draw -- more info: https://datatables.net/reference/event/draw\r\n        datatable.on('draw', function () {\r\n            initToggleToolbar();\r\n            handleDeleteRows();\r\n            toggleToolbars();\r\n            resetNewFolder();\r\n            KTMenu.createInstances();\r\n            initCopyLink();\r\n            countTotalItems();\r\n            handleRename();\r\n        });\r\n    }\r\n\r\n    // Search Datatable --- official docs reference: https://datatables.net/reference/api/search()\r\n    const handleSearchDatatable = () => {\r\n        const filterSearch = document.querySelector('[data-kt-filemanager-table-filter=\"search\"]');\r\n        filterSearch.addEventListener('keyup', function (e) {\r\n            datatable.search(e.target.value).draw();\r\n        });\r\n    }\r\n\r\n    // Delete customer\r\n    const handleDeleteRows = () => {\r\n        // Select all delete buttons\r\n        const deleteButtons = table.querySelectorAll('[data-kt-filemanager-table-filter=\"delete_row\"]');\r\n\r\n        deleteButtons.forEach(d => {\r\n            // Delete button on click\r\n            d.addEventListener('click', function (e) {\r\n                e.preventDefault();\r\n\r\n                // Select parent row\r\n                const parent = e.target.closest('tr');\r\n\r\n                // Get customer name\r\n                const fileName = parent.querySelectorAll('td')[1].innerText;\r\n\r\n                // SweetAlert2 pop up --- official docs reference: https://sweetalert2.github.io/\r\n                Swal.fire({\r\n                    text: \"Are you sure you want to delete \" + fileName + \"?\",\r\n                    icon: \"warning\",\r\n                    showCancelButton: true,\r\n                    buttonsStyling: false,\r\n                    confirmButtonText: \"Yes, delete!\",\r\n                    cancelButtonText: \"No, cancel\",\r\n                    customClass: {\r\n                        confirmButton: \"btn fw-bold btn-danger\",\r\n                        cancelButton: \"btn fw-bold btn-active-light-primary\"\r\n                    }\r\n                }).then(function (result) {\r\n                    if (result.value) {\r\n                        Swal.fire({\r\n                            text: \"You have deleted \" + fileName + \"!.\",\r\n                            icon: \"success\",\r\n                            buttonsStyling: false,\r\n                            confirmButtonText: \"Ok, got it!\",\r\n                            customClass: {\r\n                                confirmButton: \"btn fw-bold btn-primary\",\r\n                            }\r\n                        }).then(function () {\r\n                            // Remove current row\r\n                            datatable.row($(parent)).remove().draw();\r\n                        });\r\n                    } else if (result.dismiss === 'cancel') {\r\n                        Swal.fire({\r\n                            text: customerName + \" was not deleted.\",\r\n                            icon: \"error\",\r\n                            buttonsStyling: false,\r\n                            confirmButtonText: \"Ok, got it!\",\r\n                            customClass: {\r\n                                confirmButton: \"btn fw-bold btn-primary\",\r\n                            }\r\n                        });\r\n                    }\r\n                });\r\n            })\r\n        });\r\n    }\r\n\r\n    // Init toggle toolbar\r\n    const initToggleToolbar = () => {\r\n        // Toggle selected action toolbar\r\n        // Select all checkboxes\r\n        var checkboxes = table.querySelectorAll('[type=\"checkbox\"]');\r\n        if (table.getAttribute('data-kt-filemanager-table') === 'folders') {\r\n            checkboxes = document.querySelectorAll('#kt_file_manager_list_wrapper [type=\"checkbox\"]');\r\n        }\r\n\r\n        // Select elements\r\n        const deleteSelected = document.querySelector('[data-kt-filemanager-table-select=\"delete_selected\"]');\r\n\r\n        // Toggle delete selected toolbar\r\n        checkboxes.forEach(c => {\r\n            // Checkbox on click event\r\n            c.addEventListener('click', function () {\r\n                console.log(c);\r\n                setTimeout(function () {\r\n                    toggleToolbars();\r\n                }, 50);\r\n            });\r\n        });\r\n\r\n        // Deleted selected rows\r\n        deleteSelected.addEventListener('click', function () {\r\n            // SweetAlert2 pop up --- official docs reference: https://sweetalert2.github.io/\r\n            Swal.fire({\r\n                text: \"Are you sure you want to delete selected files or folders?\",\r\n                icon: \"warning\",\r\n                showCancelButton: true,\r\n                buttonsStyling: false,\r\n                confirmButtonText: \"Yes, delete!\",\r\n                cancelButtonText: \"No, cancel\",\r\n                customClass: {\r\n                    confirmButton: \"btn fw-bold btn-danger\",\r\n                    cancelButton: \"btn fw-bold btn-active-light-primary\"\r\n                }\r\n            }).then(function (result) {\r\n                if (result.value) {\r\n                    Swal.fire({\r\n                        text: \"You have deleted all selected  files or folders!.\",\r\n                        icon: \"success\",\r\n                        buttonsStyling: false,\r\n                        confirmButtonText: \"Ok, got it!\",\r\n                        customClass: {\r\n                            confirmButton: \"btn fw-bold btn-primary\",\r\n                        }\r\n                    }).then(function () {\r\n                        // Remove all selected customers\r\n                        checkboxes.forEach(c => {\r\n                            if (c.checked) {\r\n                                datatable.row($(c.closest('tbody tr'))).remove().draw();\r\n                            }\r\n                        });\r\n\r\n                        // Remove header checked box\r\n                        const headerCheckbox = table.querySelectorAll('[type=\"checkbox\"]')[0];\r\n                        headerCheckbox.checked = false;\r\n                    });\r\n                } else if (result.dismiss === 'cancel') {\r\n                    Swal.fire({\r\n                        text: \"Selected  files or folders was not deleted.\",\r\n                        icon: \"error\",\r\n                        buttonsStyling: false,\r\n                        confirmButtonText: \"Ok, got it!\",\r\n                        customClass: {\r\n                            confirmButton: \"btn fw-bold btn-primary\",\r\n                        }\r\n                    });\r\n                }\r\n            });\r\n        });\r\n    }\r\n\r\n    // Toggle toolbars\r\n    const toggleToolbars = () => {\r\n        // Define variables\r\n        const toolbarBase = document.querySelector('[data-kt-filemanager-table-toolbar=\"base\"]');\r\n        const toolbarSelected = document.querySelector('[data-kt-filemanager-table-toolbar=\"selected\"]');\r\n        const selectedCount = document.querySelector('[data-kt-filemanager-table-select=\"selected_count\"]');\r\n\r\n        // Select refreshed checkbox DOM elements \r\n        const allCheckboxes = table.querySelectorAll('tbody [type=\"checkbox\"]');\r\n\r\n        // Detect checkboxes state & count\r\n        let checkedState = false;\r\n        let count = 0;\r\n\r\n        // Count checked boxes\r\n        allCheckboxes.forEach(c => {\r\n            if (c.checked) {\r\n                checkedState = true;\r\n                count++;\r\n            }\r\n        });\r\n\r\n        // Toggle toolbars\r\n        if (checkedState) {\r\n            selectedCount.innerHTML = count;\r\n            toolbarBase.classList.add('d-none');\r\n            toolbarSelected.classList.remove('d-none');\r\n        } else {\r\n            toolbarBase.classList.remove('d-none');\r\n            toolbarSelected.classList.add('d-none');\r\n        }\r\n    }\r\n\r\n    // Handle new folder\r\n    const handleNewFolder = () => {\r\n        // Select button\r\n        const newFolder = document.getElementById('kt_file_manager_new_folder');\r\n\r\n        // Handle click action\r\n        newFolder.addEventListener('click', e => {\r\n            e.preventDefault();\r\n\r\n            // Ignore if input already exist\r\n            if (table.querySelector('#kt_file_manager_new_folder_row')) {\r\n                return;\r\n            }\r\n\r\n            // Add new blank row to datatable\r\n            const tableBody = table.querySelector('tbody');\r\n            const rowElement = uploadTemplate.cloneNode(true); // Clone template markup\r\n            tableBody.prepend(rowElement);\r\n\r\n            // Define template interactive elements\r\n            const rowForm = rowElement.querySelector('#kt_file_manager_add_folder_form');\r\n            const rowButton = rowElement.querySelector('#kt_file_manager_add_folder');\r\n            const cancelButton = rowElement.querySelector('#kt_file_manager_cancel_folder');\r\n            const folderIcon = rowElement.querySelector('.svg-icon-2x');\r\n            const rowInput = rowElement.querySelector('[name=\"new_folder_name\"]');\r\n\r\n            // Define validator\r\n            // Init form validation rules. For more info check the FormValidation plugin's official documentation:https://formvalidation.io/\r\n            var validator = FormValidation.formValidation(\r\n                rowForm,\r\n                {\r\n                    fields: {\r\n                        'new_folder_name': {\r\n                            validators: {\r\n                                notEmpty: {\r\n                                    message: 'Folder name is required'\r\n                                }\r\n                            }\r\n                        },\r\n                    },\r\n                    plugins: {\r\n                        trigger: new FormValidation.plugins.Trigger(),\r\n                        bootstrap: new FormValidation.plugins.Bootstrap5({\r\n                            rowSelector: '.fv-row',\r\n                            eleInvalidClass: '',\r\n                            eleValidClass: ''\r\n                        })\r\n                    }\r\n                }\r\n            );\r\n\r\n            // Handle add new folder button\r\n            rowButton.addEventListener('click', e => {\r\n                e.preventDefault();\r\n\r\n                // Activate indicator\r\n                rowButton.setAttribute(\"data-kt-indicator\", \"on\");\r\n\r\n                // Validate form before submit\r\n                if (validator) {\r\n                    validator.validate().then(function (status) {\r\n                        console.log('validated!');\r\n\r\n                        if (status == 'Valid') {\r\n                            // Simulate process for demo only\r\n                            setTimeout(function () {\r\n                                // Create folder link\r\n                                const folderLink = document.createElement('a');\r\n                                const folderLinkClasses = ['text-gray-800', 'text-hover-primary'];\r\n                                folderLink.setAttribute('href', '?page=apps/file-manager/blank');\r\n                                folderLink.classList.add(...folderLinkClasses);\r\n                                folderLink.innerText = rowInput.value;\r\n\r\n                                const newRow = datatable.row.add({\r\n                                    'checkbox': checkboxTemplate.innerHTML,\r\n                                    'name': folderIcon.outerHTML + folderLink.outerHTML,\r\n                                    \"size\": '-',\r\n                                    \"date\": '-',\r\n                                    'action': actionTemplate.innerHTML\r\n                                }).node();\r\n                                $(newRow).find('td').eq(4).attr('data-kt-filemanager-table', 'action_dropdown');\r\n                                $(newRow).find('td').eq(4).addClass('text-end'); // Add custom class to last 'td' element --- more info: https://datatables.net/forums/discussion/22341/row-add-cell-class\r\n\r\n                                // Re-sort datatable to allow new folder added at the top\r\n                                var index = datatable.row(0).index(),\r\n                                    rowCount = datatable.data().length - 1,\r\n                                    insertedRow = datatable.row(rowCount).data(),\r\n                                    tempRow;\r\n\r\n                                for (var i = rowCount; i > index; i--) {\r\n                                    tempRow = datatable.row(i - 1).data();\r\n                                    datatable.row(i).data(tempRow);\r\n                                    datatable.row(i - 1).data(insertedRow);\r\n                                }\r\n\r\n                                toastr.options = {\r\n                                    \"closeButton\": true,\r\n                                    \"debug\": false,\r\n                                    \"newestOnTop\": false,\r\n                                    \"progressBar\": false,\r\n                                    \"positionClass\": \"toast-top-right\",\r\n                                    \"preventDuplicates\": false,\r\n                                    \"showDuration\": \"300\",\r\n                                    \"hideDuration\": \"1000\",\r\n                                    \"timeOut\": \"5000\",\r\n                                    \"extendedTimeOut\": \"1000\",\r\n                                    \"showEasing\": \"swing\",\r\n                                    \"hideEasing\": \"linear\",\r\n                                    \"showMethod\": \"fadeIn\",\r\n                                    \"hideMethod\": \"fadeOut\"\r\n                                };\r\n\r\n                                toastr.success(rowInput.value + ' was created!');\r\n\r\n                                // Disable indicator\r\n                                rowButton.removeAttribute(\"data-kt-indicator\");\r\n\r\n                                // Reset input\r\n                                rowInput.value = '';\r\n\r\n                                datatable.draw(false);\r\n\r\n                            }, 2000);\r\n                        } else {\r\n                            // Disable indicator\r\n                            rowButton.removeAttribute(\"data-kt-indicator\");\r\n                        }\r\n                    });\r\n                }\r\n            });\r\n\r\n            // Handle cancel new folder button\r\n            cancelButton.addEventListener('click', e => {\r\n                e.preventDefault();\r\n\r\n                // Activate indicator\r\n                cancelButton.setAttribute(\"data-kt-indicator\", \"on\");\r\n\r\n                setTimeout(function () {\r\n                    // Disable indicator\r\n                    cancelButton.removeAttribute(\"data-kt-indicator\");\r\n\r\n                    // Toggle toastr\r\n                    toastr.options = {\r\n                        \"closeButton\": true,\r\n                        \"debug\": false,\r\n                        \"newestOnTop\": false,\r\n                        \"progressBar\": false,\r\n                        \"positionClass\": \"toast-top-right\",\r\n                        \"preventDuplicates\": false,\r\n                        \"showDuration\": \"300\",\r\n                        \"hideDuration\": \"1000\",\r\n                        \"timeOut\": \"5000\",\r\n                        \"extendedTimeOut\": \"1000\",\r\n                        \"showEasing\": \"swing\",\r\n                        \"hideEasing\": \"linear\",\r\n                        \"showMethod\": \"fadeIn\",\r\n                        \"hideMethod\": \"fadeOut\"\r\n                    };\r\n\r\n                    toastr.error('Cancelled new folder creation');\r\n                    resetNewFolder();\r\n                }, 1000);\r\n            });\r\n        });\r\n    }\r\n\r\n    // Reset add new folder input\r\n    const resetNewFolder = () => {\r\n        const newFolderRow = table.querySelector('#kt_file_manager_new_folder_row');\r\n\r\n        if (newFolderRow) {\r\n            newFolderRow.parentNode.removeChild(newFolderRow);\r\n        }\r\n    }\r\n\r\n    // Handle rename file or folder\r\n    const handleRename = () => {\r\n        const renameButton = table.querySelectorAll('[data-kt-filemanager-table=\"rename\"]');     \r\n\r\n        renameButton.forEach(button => {\r\n            button.addEventListener('click', renameCallback);\r\n        });\r\n    }\r\n\r\n    // Rename callback\r\n    const renameCallback = (e) => {\r\n        e.preventDefault();\r\n\r\n        // Define shared value\r\n        let nameValue;\r\n\r\n        // Stop renaming if there's an input existing\r\n        if (table.querySelectorAll('#kt_file_manager_rename_input').length > 0) {\r\n            Swal.fire({\r\n                text: \"Unsaved input detected. Please save or cancel the current item\",\r\n                icon: \"warning\",\r\n                buttonsStyling: false,\r\n                confirmButtonText: \"Ok, got it!\",\r\n                customClass: {\r\n                    confirmButton: \"btn fw-bold btn-danger\"\r\n                }\r\n            });\r\n\r\n            return;\r\n        }\r\n\r\n        // Select parent row\r\n        const parent = e.target.closest('tr');\r\n\r\n        // Get name column\r\n        const nameCol = parent.querySelectorAll('td')[1];\r\n        const colIcon = nameCol.querySelector('.svg-icon');\r\n        nameValue = nameCol.innerText;\r\n\r\n        // Set rename input template\r\n        const renameInput = renameTemplate.cloneNode(true);\r\n        renameInput.querySelector('#kt_file_manager_rename_folder_icon').innerHTML = colIcon.outerHTML;\r\n\r\n        // Swap current column content with input template\r\n        nameCol.innerHTML = renameInput.innerHTML;\r\n\r\n        // Set input value with current file/folder name\r\n        parent.querySelector('#kt_file_manager_rename_input').value = nameValue;\r\n\r\n        // Rename file / folder validator\r\n        var renameValidator = FormValidation.formValidation(\r\n            nameCol,\r\n            {\r\n                fields: {\r\n                    'rename_folder_name': {\r\n                        validators: {\r\n                            notEmpty: {\r\n                                message: 'Name is required'\r\n                            }\r\n                        }\r\n                    },\r\n                },\r\n                plugins: {\r\n                    trigger: new FormValidation.plugins.Trigger(),\r\n                    bootstrap: new FormValidation.plugins.Bootstrap5({\r\n                        rowSelector: '.fv-row',\r\n                        eleInvalidClass: '',\r\n                        eleValidClass: ''\r\n                    })\r\n                }\r\n            }\r\n        );\r\n\r\n        // Rename input button action\r\n        const renameInputButton = document.querySelector('#kt_file_manager_rename_folder');\r\n        renameInputButton.addEventListener('click', e => {\r\n            e.preventDefault();\r\n\r\n            // Detect if valid\r\n            if (renameValidator) {\r\n                renameValidator.validate().then(function (status) {\r\n                    console.log('validated!');\r\n\r\n                    if (status == 'Valid') {\r\n                        // Pop up confirmation\r\n                        Swal.fire({\r\n                            text: \"Are you sure you want to rename \" + nameValue + \"?\",\r\n                            icon: \"warning\",\r\n                            showCancelButton: true,\r\n                            buttonsStyling: false,\r\n                            confirmButtonText: \"Yes, rename it!\",\r\n                            cancelButtonText: \"No, cancel\",\r\n                            customClass: {\r\n                                confirmButton: \"btn fw-bold btn-danger\",\r\n                                cancelButton: \"btn fw-bold btn-active-light-primary\"\r\n                            }\r\n                        }).then(function (result) {\r\n                            if (result.value) {\r\n                                Swal.fire({\r\n                                    text: \"You have renamed \" + nameValue + \"!.\",\r\n                                    icon: \"success\",\r\n                                    buttonsStyling: false,\r\n                                    confirmButtonText: \"Ok, got it!\",\r\n                                    customClass: {\r\n                                        confirmButton: \"btn fw-bold btn-primary\",\r\n                                    }\r\n                                }).then(function () {\r\n                                    // Get new file / folder name value\r\n                                    const newValue = document.querySelector('#kt_file_manager_rename_input').value;\r\n\r\n                                    // New column data template\r\n                                    const newData = `<div class=\"d-flex align-items-center\">\r\n                                        ${colIcon.outerHTML}\r\n                                        <a href=\"?page=apps/file-manager/files/\" class=\"text-gray-800 text-hover-primary\">${newValue}</a>\r\n                                    </div>`;\r\n\r\n                                    // Draw datatable with new content -- Add more events here for any server-side events\r\n                                    datatable.cell($(nameCol)).data(newData).draw();\r\n                                });\r\n                            } else if (result.dismiss === 'cancel') {\r\n                                Swal.fire({\r\n                                    text: nameValue + \" was not renamed.\",\r\n                                    icon: \"error\",\r\n                                    buttonsStyling: false,\r\n                                    confirmButtonText: \"Ok, got it!\",\r\n                                    customClass: {\r\n                                        confirmButton: \"btn fw-bold btn-primary\",\r\n                                    }\r\n                                });\r\n                            }\r\n                        });\r\n                    }\r\n                });\r\n            }\r\n        });\r\n\r\n        // Cancel rename input\r\n        const cancelInputButton = document.querySelector('#kt_file_manager_rename_folder_cancel');\r\n        cancelInputButton.addEventListener('click', e => {\r\n            e.preventDefault();\r\n\r\n            // Simulate process for demo only\r\n            cancelInputButton.setAttribute(\"data-kt-indicator\", \"on\");\r\n\r\n            setTimeout(function () {\r\n                const revertTemplate = `<div class=\"d-flex align-items-center\">\r\n                    ${colIcon.outerHTML}\r\n                    <a href=\"?page=apps/file-manager/files/\" class=\"text-gray-800 text-hover-primary\">${nameValue}</a>\r\n                </div>`;\r\n\r\n                // Remove spinner\r\n                cancelInputButton.removeAttribute(\"data-kt-indicator\");\r\n\r\n                // Draw datatable with new content -- Add more events here for any server-side events\r\n                datatable.cell($(nameCol)).data(revertTemplate).draw();\r\n\r\n                // Toggle toastr\r\n                toastr.options = {\r\n                    \"closeButton\": true,\r\n                    \"debug\": false,\r\n                    \"newestOnTop\": false,\r\n                    \"progressBar\": false,\r\n                    \"positionClass\": \"toast-top-right\",\r\n                    \"preventDuplicates\": false,\r\n                    \"showDuration\": \"300\",\r\n                    \"hideDuration\": \"1000\",\r\n                    \"timeOut\": \"5000\",\r\n                    \"extendedTimeOut\": \"1000\",\r\n                    \"showEasing\": \"swing\",\r\n                    \"hideEasing\": \"linear\",\r\n                    \"showMethod\": \"fadeIn\",\r\n                    \"hideMethod\": \"fadeOut\"\r\n                };\r\n\r\n                toastr.error('Cancelled rename function');\r\n            }, 1000);\r\n        });\r\n    }\r\n\r\n    // Init dropzone\r\n    const initDropzone = () => {\r\n        // set the dropzone container id\r\n        const id = \"#kt_modal_upload_dropzone\";\r\n        const dropzone = document.querySelector(id);\r\n\r\n        // set the preview element template\r\n        var previewNode = dropzone.querySelector(\".dropzone-item\");\r\n        previewNode.id = \"\";\r\n        var previewTemplate = previewNode.parentNode.innerHTML;\r\n        previewNode.parentNode.removeChild(previewNode);\r\n\r\n        var myDropzone = new Dropzone(id, { // Make the whole body a dropzone\r\n            url: \"path/to/your/server\", // Set the url for your upload script location\r\n            parallelUploads: 10,\r\n            previewTemplate: previewTemplate,\r\n            maxFilesize: 1, // Max filesize in MB\r\n            autoProcessQueue: false, // Stop auto upload\r\n            autoQueue: false, // Make sure the files aren't queued until manually added\r\n            previewsContainer: id + \" .dropzone-items\", // Define the container to display the previews\r\n            clickable: id + \" .dropzone-select\" // Define the element that should be used as click trigger to select files.\r\n        });\r\n\r\n        myDropzone.on(\"addedfile\", function (file) {\r\n            // Hook each start button\r\n            file.previewElement.querySelector(id + \" .dropzone-start\").onclick = function () {\r\n                // myDropzone.enqueueFile(file); -- default dropzone function\r\n\r\n                // Process simulation for demo only\r\n                const progressBar = file.previewElement.querySelector('.progress-bar');\r\n                progressBar.style.opacity = \"1\";\r\n                var width = 1;\r\n                var timer = setInterval(function () {\r\n                    if (width >= 100) {\r\n                        myDropzone.emit(\"success\", file);\r\n                        myDropzone.emit(\"complete\", file);\r\n                        clearInterval(timer);\r\n                    } else {\r\n                        width++;\r\n                        progressBar.style.width = width + '%';\r\n                    }\r\n                }, 20);\r\n            };\r\n\r\n            const dropzoneItems = dropzone.querySelectorAll('.dropzone-item');\r\n            dropzoneItems.forEach(dropzoneItem => {\r\n                dropzoneItem.style.display = '';\r\n            });\r\n            dropzone.querySelector('.dropzone-upload').style.display = \"inline-block\";\r\n            dropzone.querySelector('.dropzone-remove-all').style.display = \"inline-block\";\r\n        });\r\n\r\n        // Hide the total progress bar when nothing's uploading anymore\r\n        myDropzone.on(\"complete\", function (file) {\r\n            const progressBars = dropzone.querySelectorAll('.dz-complete');\r\n            setTimeout(function () {\r\n                progressBars.forEach(progressBar => {\r\n                    progressBar.querySelector('.progress-bar').style.opacity = \"0\";\r\n                    progressBar.querySelector('.progress').style.opacity = \"0\";\r\n                    progressBar.querySelector('.dropzone-start').style.opacity = \"0\";\r\n                });\r\n            }, 300);\r\n        });\r\n\r\n        // Setup the buttons for all transfers\r\n        dropzone.querySelector(\".dropzone-upload\").addEventListener('click', function () {\r\n            // myDropzone.processQueue(); --- default dropzone process\r\n\r\n            // Process simulation for demo only\r\n            myDropzone.files.forEach(file => {\r\n                const progressBar = file.previewElement.querySelector('.progress-bar');\r\n                progressBar.style.opacity = \"1\";\r\n                var width = 1;\r\n                var timer = setInterval(function () {\r\n                    if (width >= 100) {\r\n                        myDropzone.emit(\"success\", file);\r\n                        myDropzone.emit(\"complete\", file);\r\n                        clearInterval(timer);\r\n                    } else {\r\n                        width++;\r\n                        progressBar.style.width = width + '%';\r\n                    }\r\n                }, 20);\r\n            });\r\n        });\r\n\r\n        // Setup the button for remove all files\r\n        dropzone.querySelector(\".dropzone-remove-all\").addEventListener('click', function () {\r\n            Swal.fire({\r\n                text: \"Are you sure you would like to remove all files?\",\r\n                icon: \"warning\",\r\n                showCancelButton: true,\r\n                buttonsStyling: false,\r\n                confirmButtonText: \"Yes, remove it!\",\r\n                cancelButtonText: \"No, return\",\r\n                customClass: {\r\n                    confirmButton: \"btn btn-primary\",\r\n                    cancelButton: \"btn btn-active-light\"\r\n                }\r\n            }).then(function (result) {\r\n                if (result.value) {\r\n                    dropzone.querySelector('.dropzone-upload').style.display = \"none\";\r\n                    dropzone.querySelector('.dropzone-remove-all').style.display = \"none\";\r\n                    myDropzone.removeAllFiles(true);\r\n                } else if (result.dismiss === 'cancel') {\r\n                    Swal.fire({\r\n                        text: \"Your files was not removed!.\",\r\n                        icon: \"error\",\r\n                        buttonsStyling: false,\r\n                        confirmButtonText: \"Ok, got it!\",\r\n                        customClass: {\r\n                            confirmButton: \"btn btn-primary\",\r\n                        }\r\n                    });\r\n                }\r\n            });\r\n        });\r\n\r\n        // On all files completed upload\r\n        myDropzone.on(\"queuecomplete\", function (progress) {\r\n            const uploadIcons = dropzone.querySelectorAll('.dropzone-upload');\r\n            uploadIcons.forEach(uploadIcon => {\r\n                uploadIcon.style.display = \"none\";\r\n            });\r\n        });\r\n\r\n        // On all files removed\r\n        myDropzone.on(\"removedfile\", function (file) {\r\n            if (myDropzone.files.length < 1) {\r\n                dropzone.querySelector('.dropzone-upload').style.display = \"none\";\r\n                dropzone.querySelector('.dropzone-remove-all').style.display = \"none\";\r\n            }\r\n        });\r\n    }\r\n\r\n    // Init copy link\r\n    const initCopyLink = () => {\r\n        // Select all copy link elements\r\n        const elements = table.querySelectorAll('[data-kt-filemanger-table=\"copy_link\"]');\r\n\r\n        elements.forEach(el => {\r\n            // Define elements\r\n            const button = el.querySelector('button');\r\n            const generator = el.querySelector('[data-kt-filemanger-table=\"copy_link_generator\"]');\r\n            const result = el.querySelector('[data-kt-filemanger-table=\"copy_link_result\"]');\r\n            const input = el.querySelector('input');\r\n\r\n            // Click action\r\n            button.addEventListener('click', e => {\r\n                e.preventDefault();\r\n\r\n                // Reset toggle\r\n                generator.classList.remove('d-none');\r\n                result.classList.add('d-none');\r\n\r\n                var linkTimeout;\r\n                clearTimeout(linkTimeout);\r\n                linkTimeout = setTimeout(() => {\r\n                    generator.classList.add('d-none');\r\n                    result.classList.remove('d-none');\r\n                    input.select();\r\n                }, 2000);\r\n            });\r\n        });\r\n    }\r\n\r\n    // Handle move to folder\r\n    const handleMoveToFolder = () => {\r\n        const element = document.querySelector('#kt_modal_move_to_folder');\r\n        const form = element.querySelector('#kt_modal_move_to_folder_form');\r\n        const saveButton = form.querySelector('#kt_modal_move_to_folder_submit');\r\n        const moveModal = new bootstrap.Modal(element);\r\n\r\n        // Init form validation rules. For more info check the FormValidation plugin's official documentation:https://formvalidation.io/\r\n        var validator = FormValidation.formValidation(\r\n            form,\r\n            {\r\n                fields: {\r\n                    'move_to_folder': {\r\n                        validators: {\r\n                            notEmpty: {\r\n                                message: 'Please select a folder.'\r\n                            }\r\n                        }\r\n                    },\r\n                },\r\n\r\n                plugins: {\r\n                    trigger: new FormValidation.plugins.Trigger(),\r\n                    bootstrap: new FormValidation.plugins.Bootstrap5({\r\n                        rowSelector: '.fv-row',\r\n                        eleInvalidClass: '',\r\n                        eleValidClass: ''\r\n                    })\r\n                }\r\n            }\r\n        );\r\n\r\n        saveButton.addEventListener('click', e => {\r\n            e.preventDefault();\r\n\r\n            saveButton.setAttribute(\"data-kt-indicator\", \"on\");\r\n\r\n            if (validator) {\r\n                validator.validate().then(function (status) {\r\n                    console.log('validated!');\r\n\r\n                    if (status == 'Valid') {\r\n                        // Simulate process for demo only\r\n                        setTimeout(function () {\r\n\r\n                            Swal.fire({\r\n                                text: \"Are you sure you would like to move to this folder\",\r\n                                icon: \"warning\",\r\n                                showCancelButton: true,\r\n                                buttonsStyling: false,\r\n                                confirmButtonText: \"Yes, move it!\",\r\n                                cancelButtonText: \"No, return\",\r\n                                customClass: {\r\n                                    confirmButton: \"btn btn-primary\",\r\n                                    cancelButton: \"btn btn-active-light\"\r\n                                }\r\n                            }).then(function (result) {\r\n                                if (result.isConfirmed) {\r\n                                    form.reset(); // Reset form\t\r\n                                    moveModal.hide(); // Hide modal\t\t\t\r\n\r\n                                    toastr.options = {\r\n                                        \"closeButton\": true,\r\n                                        \"debug\": false,\r\n                                        \"newestOnTop\": false,\r\n                                        \"progressBar\": false,\r\n                                        \"positionClass\": \"toast-top-right\",\r\n                                        \"preventDuplicates\": false,\r\n                                        \"showDuration\": \"300\",\r\n                                        \"hideDuration\": \"1000\",\r\n                                        \"timeOut\": \"5000\",\r\n                                        \"extendedTimeOut\": \"1000\",\r\n                                        \"showEasing\": \"swing\",\r\n                                        \"hideEasing\": \"linear\",\r\n                                        \"showMethod\": \"fadeIn\",\r\n                                        \"hideMethod\": \"fadeOut\"\r\n                                    };\r\n\r\n                                    toastr.success('1 item has been moved.');\r\n\r\n                                    saveButton.removeAttribute(\"data-kt-indicator\");\r\n                                } else {\r\n                                    Swal.fire({\r\n                                        text: \"Your action has been cancelled!.\",\r\n                                        icon: \"error\",\r\n                                        buttonsStyling: false,\r\n                                        confirmButtonText: \"Ok, got it!\",\r\n                                        customClass: {\r\n                                            confirmButton: \"btn btn-primary\",\r\n                                        }\r\n                                    });\r\n\r\n                                    saveButton.removeAttribute(\"data-kt-indicator\");\r\n                                }\r\n                            });\r\n                        }, 500);\r\n                    } else {\r\n                        saveButton.removeAttribute(\"data-kt-indicator\");\r\n                    }\r\n                });\r\n            }\r\n        });\r\n    }\r\n\r\n    // Count total number of items\r\n    const countTotalItems = () => {\r\n        const counter = document.getElementById('kt_file_manager_items_counter');\r\n\r\n        // Count total number of elements in datatable --- more info: https://datatables.net/reference/api/count()\r\n        counter.innerText = datatable.rows().count() + ' items';\r\n    }\r\n\r\n    // Public methods\r\n    return {\r\n        init: function () {\r\n            table = document.querySelector('#kt_file_manager_list');\r\n\r\n            if (!table) {\r\n                return;\r\n            }\r\n\r\n            initTemplates();\r\n            initDatatable();\r\n            initToggleToolbar();\r\n            handleSearchDatatable();\r\n            handleDeleteRows();\r\n            handleNewFolder();\r\n            initDropzone();\r\n            initCopyLink();\r\n            handleRename();\r\n            handleMoveToFolder();\r\n            countTotalItems();\r\n            KTMenu.createInstances();\r\n        }\r\n    }\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function () {\r\n    KTFileManagerList.init();\r\n});"], "names": [], "sourceRoot": ""}