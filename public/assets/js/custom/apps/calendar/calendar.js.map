{"version": 3, "file": "js/custom/apps/calendar/calendar.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,cAAc;AACd;AACA;AACA;AACA,iBAAiB;AACjB;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gEAAgE;AAChE,yFAAyF;AACzF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qCAAqC;AACrC;AACA;AACA;AACA;AACA;AACA,6BAA6B;AAC7B;AACA,6CAA6C;AAC7C,yBAAyB;AACzB,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA,iBAAiB;AACjB;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,cAAc;AACd;AACA;AACA;AACA,iBAAiB;AACjB;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gEAAgE;AAChE,yFAAyF;AACzF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qCAAqC;AACrC;AACA;AACA;AACA;AACA;AACA,6BAA6B;AAC7B;AACA,6CAA6C;AAC7C,yBAAyB;AACzB,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA,iBAAiB;AACjB;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,sCAAsC;AACtC,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA,kCAAkC;AAClC,kCAAkC;AAClC,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA,kCAAkC;AAClC,kCAAkC;AAClC,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC", "sources": ["webpack://keenthemes/../../../themes/metronic/html/demo3/src/js/custom/apps/calendar/calendar.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTAppCalendar = function () {\r\n    // Shared variables\r\n    // Calendar variables\r\n    var calendar;\r\n    var data = {\r\n        id: '',\r\n        eventName: '',\r\n        eventDescription: '',\r\n        eventLocation: '',\r\n        startDate: '',\r\n        endDate: '',\r\n        allDay: false\r\n    };\r\n    var popover;\r\n    var popoverState = false;\r\n\r\n    // Add event variables\r\n    var eventName;\r\n    var eventDescription;\r\n    var eventLocation;\r\n    var startDatepicker;\r\n    var startFlatpickr;\r\n    var endDatepicker;\r\n    var endFlatpickr;\r\n    var startTimepicker;\r\n    var startTimeFlatpickr;\r\n    var endTimepicker\r\n    var endTimeFlatpickr;\r\n    var modal;\r\n    var modalTitle;\r\n    var form;\r\n    var validator;\r\n    var addButton;\r\n    var submitButton;\r\n    var cancelButton;\r\n    var closeButton;\r\n\r\n    // View event variables\r\n    var viewEventName;\r\n    var viewAllDay;\r\n    var viewEventDescription;\r\n    var viewEventLocation;\r\n    var viewStartDate;\r\n    var viewEndDate;\r\n    var viewModal;\r\n    var viewEditButton;\r\n    var viewDeleteButton;\r\n\r\n\r\n    // Private functions\r\n    var initCalendarApp = function () {\r\n        // Define variables\r\n        var calendarEl = document.getElementById('kt_calendar_app');\r\n        var todayDate = moment().startOf('day');\r\n        var YM = todayDate.format('YYYY-MM');\r\n        var YESTERDAY = todayDate.clone().subtract(1, 'day').format('YYYY-MM-DD');\r\n        var TODAY = todayDate.format('YYYY-MM-DD');\r\n        var TOMORROW = todayDate.clone().add(1, 'day').format('YYYY-MM-DD');\r\n\r\n        // Init calendar --- more info: https://fullcalendar.io/docs/initialize-globals\r\n        calendar = new FullCalendar.Calendar(calendarEl, {\r\n            headerToolbar: {\r\n                left: 'prev,next today',\r\n                center: 'title',\r\n                right: 'dayGridMonth,timeGridWeek,timeGridDay'\r\n            },\r\n            initialDate: TODAY,\r\n            navLinks: true, // can click day/week names to navigate views\r\n            selectable: true,\r\n            selectMirror: true,\r\n\r\n            // Select dates action --- more info: https://fullcalendar.io/docs/select-callback\r\n            select: function (arg) {\r\n                hidePopovers();\r\n                formatArgs(arg);\r\n                handleNewEvent();\r\n            },\r\n\r\n            // Click event --- more info: https://fullcalendar.io/docs/eventClick\r\n            eventClick: function (arg) {\r\n                hidePopovers();\r\n\r\n                formatArgs({\r\n                    id: arg.event.id,\r\n                    title: arg.event.title,\r\n                    description: arg.event.extendedProps.description,\r\n                    location: arg.event.extendedProps.location,\r\n                    startStr: arg.event.startStr,\r\n                    endStr: arg.event.endStr,\r\n                    allDay: arg.event.allDay\r\n                });\r\n                handleViewEvent();\r\n            },\r\n\r\n            // MouseEnter event --- more info: https://fullcalendar.io/docs/eventMouseEnter\r\n            eventMouseEnter: function (arg) {\r\n                formatArgs({\r\n                    id: arg.event.id,\r\n                    title: arg.event.title,\r\n                    description: arg.event.extendedProps.description,\r\n                    location: arg.event.extendedProps.location,\r\n                    startStr: arg.event.startStr,\r\n                    endStr: arg.event.endStr,\r\n                    allDay: arg.event.allDay\r\n                });\r\n\r\n                // Show popover preview\r\n                initPopovers(arg.el);\r\n            },\r\n\r\n            editable: true,\r\n            dayMaxEvents: true, // allow \"more\" link when too many events\r\n            events: [\r\n                {\r\n                    id: uid(),\r\n                    title: 'All Day Event',\r\n                    start: YM + '-01',\r\n                    end: YM + '-02',\r\n                    description: 'Toto lorem ipsum dolor sit incid idunt ut',\r\n                    className: \"fc-event-danger fc-event-solid-warning\",\r\n                    location: 'Federation Square'\r\n                },\r\n                {\r\n                    id: uid(),\r\n                    title: 'Reporting',\r\n                    start: YM + '-14T13:30:00',\r\n                    description: 'Lorem ipsum dolor incid idunt ut labore',\r\n                    end: YM + '-14T14:30:00',\r\n                    className: \"fc-event-success\",\r\n                    location: 'Meeting Room 7.03'\r\n                },\r\n                {\r\n                    id: uid(),\r\n                    title: 'Company Trip',\r\n                    start: YM + '-02',\r\n                    description: 'Lorem ipsum dolor sit tempor incid',\r\n                    end: YM + '-03',\r\n                    className: \"fc-event-primary\",\r\n                    location: 'Seoul, Korea'\r\n\r\n                },\r\n                {\r\n                    id: uid(),\r\n                    title: 'ICT Expo 2021 - Product Release',\r\n                    start: YM + '-03',\r\n                    description: 'Lorem ipsum dolor sit tempor inci',\r\n                    end: YM + '-05',\r\n                    className: \"fc-event-light fc-event-solid-primary\",\r\n                    location: 'Melbourne Exhibition Hall'\r\n                },\r\n                {\r\n                    id: uid(),\r\n                    title: 'Dinner',\r\n                    start: YM + '-12',\r\n                    description: 'Lorem ipsum dolor sit amet, conse ctetur',\r\n                    end: YM + '-13',\r\n                    location: 'Squire\\'s Loft'\r\n                },\r\n                {\r\n                    id: uid(),\r\n                    title: 'Repeating Event',\r\n                    start: YM + '-09T16:00:00',\r\n                    end: YM + '-09T17:00:00',\r\n                    description: 'Lorem ipsum dolor sit ncididunt ut labore',\r\n                    className: \"fc-event-danger\",\r\n                    location: 'General Area'\r\n                },\r\n                {\r\n                    id: uid(),\r\n                    title: 'Repeating Event',\r\n                    description: 'Lorem ipsum dolor sit amet, labore',\r\n                    start: YM + '-16T16:00:00',\r\n                    end: YM + '-16T17:00:00',\r\n                    location: 'General Area'\r\n                },\r\n                {\r\n                    id: uid(),\r\n                    title: 'Conference',\r\n                    start: YESTERDAY,\r\n                    end: TOMORROW,\r\n                    description: 'Lorem ipsum dolor eius mod tempor labore',\r\n                    className: \"fc-event-primary\",\r\n                    location: 'Conference Hall A'\r\n                },\r\n                {\r\n                    id: uid(),\r\n                    title: 'Meeting',\r\n                    start: TODAY + 'T10:30:00',\r\n                    end: TODAY + 'T12:30:00',\r\n                    description: 'Lorem ipsum dolor eiu idunt ut labore',\r\n                    location: 'Meeting Room 11.06'\r\n                },\r\n                {\r\n                    id: uid(),\r\n                    title: 'Lunch',\r\n                    start: TODAY + 'T12:00:00',\r\n                    end: TODAY + 'T14:00:00',\r\n                    className: \"fc-event-info\",\r\n                    description: 'Lorem ipsum dolor sit amet, ut labore',\r\n                    location: 'Cafeteria'\r\n                },\r\n                {\r\n                    id: uid(),\r\n                    title: 'Meeting',\r\n                    start: TODAY + 'T14:30:00',\r\n                    end: TODAY + 'T15:30:00',\r\n                    className: \"fc-event-warning\",\r\n                    description: 'Lorem ipsum conse ctetur adipi scing',\r\n                    location: 'Meeting Room 11.10'\r\n                },\r\n                {\r\n                    id: uid(),\r\n                    title: 'Happy Hour',\r\n                    start: TODAY + 'T17:30:00',\r\n                    end: TODAY + 'T21:30:00',\r\n                    className: \"fc-event-info\",\r\n                    description: 'Lorem ipsum dolor sit amet, conse ctetur',\r\n                    location: 'The English Pub'\r\n                },\r\n                {\r\n                    id: uid(),\r\n                    title: 'Dinner',\r\n                    start: TOMORROW + 'T18:00:00',\r\n                    end: TOMORROW + 'T21:00:00',\r\n                    className: \"fc-event-solid-danger fc-event-light\",\r\n                    description: 'Lorem ipsum dolor sit ctetur adipi scing',\r\n                    location: 'New York Steakhouse'\r\n                },\r\n                {\r\n                    id: uid(),\r\n                    title: 'Birthday Party',\r\n                    start: TOMORROW + 'T12:00:00',\r\n                    end: TOMORROW + 'T14:00:00',\r\n                    className: \"fc-event-primary\",\r\n                    description: 'Lorem ipsum dolor sit amet, scing',\r\n                    location: 'The English Pub'\r\n                },\r\n                {\r\n                    id: uid(),\r\n                    title: 'Site visit',\r\n                    start: YM + '-28',\r\n                    end: YM + '-29',\r\n                    className: \"fc-event-solid-info fc-event-light\",\r\n                    description: 'Lorem ipsum dolor sit amet, labore',\r\n                    location: '271, Spring Street'\r\n                }\r\n            ],\r\n\r\n            // Reset popovers when changing calendar views --- more info: https://fullcalendar.io/docs/datesSet\r\n            datesSet: function(){\r\n                hidePopovers();\r\n            }\r\n        });\r\n\r\n        calendar.render();\r\n    }\r\n\r\n    // Initialize popovers --- more info: https://getbootstrap.com/docs/4.0/components/popovers/\r\n    const initPopovers = (element) => {\r\n        hidePopovers();\r\n\r\n        // Generate popover content\r\n        const startDate = data.allDay ? moment(data.startDate).format('Do MMM, YYYY') : moment(data.startDate).format('Do MMM, YYYY - h:mm a');\r\n        const endDate = data.allDay ? moment(data.endDate).format('Do MMM, YYYY') : moment(data.endDate).format('Do MMM, YYYY - h:mm a');\r\n        const popoverHtml = '<div class=\"fw-bolder mb-2\">' + data.eventName + '</div><div class=\"fs-7\"><span class=\"fw-bold\">Start:</span> ' + startDate + '</div><div class=\"fs-7 mb-4\"><span class=\"fw-bold\">End:</span> ' + endDate + '</div><div id=\"kt_calendar_event_view_button\" type=\"button\" class=\"btn btn-sm btn-light-primary\">View More</div>';\r\n\r\n        // Popover options\r\n        var options = {\r\n            container: 'body',\r\n            trigger: 'manual',\r\n            boundary: 'window',\r\n            placement: 'auto',\r\n            dismiss: true,\r\n            html: true,\r\n            title: 'Event Summary',\r\n            content: popoverHtml,\r\n        }\r\n\r\n        // Initialize popover\r\n        popover = KTApp.initBootstrapPopover(element, options);\r\n\r\n        // Show popover\r\n        popover.show();\r\n\r\n        // Update popover state\r\n        popoverState = true;\r\n\r\n        // Open view event modal\r\n        handleViewButton();\r\n    }\r\n\r\n    // Hide active popovers\r\n    const hidePopovers = () => {\r\n        if (popoverState) {\r\n            popover.dispose();\r\n            popoverState = false;\r\n        }\r\n    }\r\n\r\n    // Init validator\r\n    const initValidator = () => {\r\n        // Init form validation rules. For more info check the FormValidation plugin's official documentation:https://formvalidation.io/\r\n        validator = FormValidation.formValidation(\r\n            form,\r\n            {\r\n                fields: {\r\n                    'calendar_event_name': {\r\n                        validators: {\r\n                            notEmpty: {\r\n                                message: 'Event name is required'\r\n                            }\r\n                        }\r\n                    },\r\n                    'calendar_event_start_date': {\r\n                        validators: {\r\n                            notEmpty: {\r\n                                message: 'Start date is required'\r\n                            }\r\n                        }\r\n                    },\r\n                    'calendar_event_end_date': {\r\n                        validators: {\r\n                            notEmpty: {\r\n                                message: 'End date is required'\r\n                            }\r\n                        }\r\n                    }\r\n                },\r\n\r\n                plugins: {\r\n                    trigger: new FormValidation.plugins.Trigger(),\r\n                    bootstrap: new FormValidation.plugins.Bootstrap5({\r\n                        rowSelector: '.fv-row',\r\n                        eleInvalidClass: '',\r\n                        eleValidClass: ''\r\n                    })\r\n                }\r\n            }\r\n        );\r\n    }\r\n\r\n    // Initialize datepickers --- more info: https://flatpickr.js.org/\r\n    const initDatepickers = () => {\r\n        startFlatpickr = flatpickr(startDatepicker, {\r\n            enableTime: false,\r\n            dateFormat: \"Y-m-d\",\r\n        });\r\n\r\n        endFlatpickr = flatpickr(endDatepicker, {\r\n            enableTime: false,\r\n            dateFormat: \"Y-m-d\",\r\n        });\r\n\r\n        startTimeFlatpickr = flatpickr(startTimepicker, {\r\n            enableTime: true,\r\n            noCalendar: true,\r\n            dateFormat: \"H:i\",\r\n        });\r\n\r\n        endTimeFlatpickr = flatpickr(endTimepicker, {\r\n            enableTime: true,\r\n            noCalendar: true,\r\n            dateFormat: \"H:i\",\r\n        });\r\n    }\r\n\r\n    // Handle add button\r\n    const handleAddButton = () => {\r\n        addButton.addEventListener('click', e => {\r\n            hidePopovers();\r\n\r\n            // Reset form data\r\n            data = {\r\n                id: '',\r\n                eventName: '',\r\n                eventDescription: '',\r\n                startDate: new Date(),\r\n                endDate: new Date(),\r\n                allDay: false\r\n            };\r\n            handleNewEvent();\r\n        });\r\n    }\r\n\r\n    // Handle add new event\r\n    const handleNewEvent = () => {\r\n        // Update modal title\r\n        modalTitle.innerText = \"Add a New Event\";\r\n\r\n        modal.show();\r\n\r\n        // Select datepicker wrapper elements\r\n        const datepickerWrappers = form.querySelectorAll('[data-kt-calendar=\"datepicker\"]');\r\n\r\n        // Handle all day toggle\r\n        const allDayToggle = form.querySelector('#kt_calendar_datepicker_allday');\r\n        allDayToggle.addEventListener('click', e => {\r\n            if (e.target.checked) {\r\n                datepickerWrappers.forEach(dw => {\r\n                    dw.classList.add('d-none');\r\n                });\r\n            } else {\r\n                endFlatpickr.setDate(data.startDate, true, 'Y-m-d');\r\n                datepickerWrappers.forEach(dw => {\r\n                    dw.classList.remove('d-none');\r\n                });\r\n            }\r\n        });\r\n\r\n        populateForm(data);\r\n\r\n        // Handle submit form\r\n        submitButton.addEventListener('click', function (e) {\r\n            // Prevent default button action\r\n            e.preventDefault();\r\n\r\n            // Validate form before submit\r\n            if (validator) {\r\n                validator.validate().then(function (status) {\r\n                    console.log('validated!');\r\n\r\n                    if (status == 'Valid') {\r\n                        // Show loading indication\r\n                        submitButton.setAttribute('data-kt-indicator', 'on');\r\n\r\n                        // Disable submit button whilst loading\r\n                        submitButton.disabled = true;\r\n\r\n                        // Simulate form submission\r\n                        setTimeout(function () {\r\n                            // Simulate form submission\r\n                            submitButton.removeAttribute('data-kt-indicator');\r\n\r\n                            // Show popup confirmation \r\n                            Swal.fire({\r\n                                text: \"New event added to calendar!\",\r\n                                icon: \"success\",\r\n                                buttonsStyling: false,\r\n                                confirmButtonText: \"Ok, got it!\",\r\n                                customClass: {\r\n                                    confirmButton: \"btn btn-primary\"\r\n                                }\r\n                            }).then(function (result) {\r\n                                if (result.isConfirmed) {\r\n                                    modal.hide();\r\n\r\n                                    // Enable submit button after loading\r\n                                    submitButton.disabled = false;\r\n\r\n                                    // Detect if is all day event\r\n                                    let allDayEvent = false;\r\n                                    if (allDayToggle.checked) { allDayEvent = true; }\r\n                                    if (startTimeFlatpickr.selectedDates.length === 0) { allDayEvent = true; }\r\n\r\n                                    // Merge date & time\r\n                                    var startDateTime = moment(startFlatpickr.selectedDates[0]).format();\r\n                                    var endDateTime = moment(endFlatpickr.selectedDates[endFlatpickr.selectedDates.length - 1]).format();\r\n                                    if (!allDayEvent) {\r\n                                        const startDate = moment(startFlatpickr.selectedDates[0]).format('YYYY-MM-DD');\r\n                                        const endDate = startDate;\r\n                                        const startTime = moment(startTimeFlatpickr.selectedDates[0]).format('HH:mm:ss');\r\n                                        const endTime = moment(endTimeFlatpickr.selectedDates[0]).format('HH:mm:ss');\r\n\r\n                                        startDateTime = startDate + 'T' + startTime;\r\n                                        endDateTime = endDate + 'T' + endTime;\r\n                                    }\r\n\r\n                                    // Add new event to calendar\r\n                                    calendar.addEvent({\r\n                                        id: uid(),\r\n                                        title: eventName.value,\r\n                                        description: eventDescription.value,\r\n                                        location: eventLocation.value,\r\n                                        start: startDateTime,\r\n                                        end: endDateTime,\r\n                                        allDay: allDayEvent\r\n                                    });\r\n                                    calendar.render();\r\n\r\n                                    // Reset form for demo purposes only\r\n                                    form.reset();\r\n                                }\r\n                            });\r\n\r\n                            //form.submit(); // Submit form\r\n                        }, 2000);\r\n                    } else {\r\n                        // Show popup warning \r\n                        Swal.fire({\r\n                            text: \"Sorry, looks like there are some errors detected, please try again.\",\r\n                            icon: \"error\",\r\n                            buttonsStyling: false,\r\n                            confirmButtonText: \"Ok, got it!\",\r\n                            customClass: {\r\n                                confirmButton: \"btn btn-primary\"\r\n                            }\r\n                        });\r\n                    }\r\n                });\r\n            }\r\n        });\r\n    }\r\n\r\n    // Handle edit event\r\n    const handleEditEvent = () => {\r\n        // Update modal title\r\n        modalTitle.innerText = \"Edit an Event\";\r\n\r\n        modal.show();\r\n\r\n        // Select datepicker wrapper elements\r\n        const datepickerWrappers = form.querySelectorAll('[data-kt-calendar=\"datepicker\"]');\r\n\r\n        // Handle all day toggle\r\n        const allDayToggle = form.querySelector('#kt_calendar_datepicker_allday');\r\n        allDayToggle.addEventListener('click', e => {\r\n            if (e.target.checked) {\r\n                datepickerWrappers.forEach(dw => {\r\n                    dw.classList.add('d-none');\r\n                });\r\n            } else {\r\n                endFlatpickr.setDate(data.startDate, true, 'Y-m-d');\r\n                datepickerWrappers.forEach(dw => {\r\n                    dw.classList.remove('d-none');\r\n                });\r\n            }\r\n        });\r\n\r\n        populateForm(data);\r\n\r\n        // Handle submit form\r\n        submitButton.addEventListener('click', function (e) {\r\n            // Prevent default button action\r\n            e.preventDefault();\r\n\r\n            // Validate form before submit\r\n            if (validator) {\r\n                validator.validate().then(function (status) {\r\n                    console.log('validated!');\r\n\r\n                    if (status == 'Valid') {\r\n                        // Show loading indication\r\n                        submitButton.setAttribute('data-kt-indicator', 'on');\r\n\r\n                        // Disable submit button whilst loading\r\n                        submitButton.disabled = true;\r\n\r\n                        // Simulate form submission\r\n                        setTimeout(function () {\r\n                            // Simulate form submission\r\n                            submitButton.removeAttribute('data-kt-indicator');\r\n\r\n                            // Show popup confirmation \r\n                            Swal.fire({\r\n                                text: \"New event added to calendar!\",\r\n                                icon: \"success\",\r\n                                buttonsStyling: false,\r\n                                confirmButtonText: \"Ok, got it!\",\r\n                                customClass: {\r\n                                    confirmButton: \"btn btn-primary\"\r\n                                }\r\n                            }).then(function (result) {\r\n                                if (result.isConfirmed) {\r\n                                    modal.hide();\r\n\r\n                                    // Enable submit button after loading\r\n                                    submitButton.disabled = false;\r\n\r\n                                    // Remove old event\r\n                                    calendar.getEventById(data.id).remove();\r\n\r\n                                    // Detect if is all day event\r\n                                    let allDayEvent = false;\r\n                                    if (allDayToggle.checked) { allDayEvent = true; }\r\n                                    if (startTimeFlatpickr.selectedDates.length === 0) { allDayEvent = true; }\r\n\r\n                                    // Merge date & time\r\n                                    var startDateTime = moment(startFlatpickr.selectedDates[0]).format();\r\n                                    var endDateTime = moment(endFlatpickr.selectedDates[endFlatpickr.selectedDates.length - 1]).format();\r\n                                    if (!allDayEvent) {\r\n                                        const startDate = moment(startFlatpickr.selectedDates[0]).format('YYYY-MM-DD');\r\n                                        const endDate = startDate;\r\n                                        const startTime = moment(startTimeFlatpickr.selectedDates[0]).format('HH:mm:ss');\r\n                                        const endTime = moment(endTimeFlatpickr.selectedDates[0]).format('HH:mm:ss');\r\n\r\n                                        startDateTime = startDate + 'T' + startTime;\r\n                                        endDateTime = endDate + 'T' + endTime;\r\n                                    }\r\n\r\n                                    // Add new event to calendar\r\n                                    calendar.addEvent({\r\n                                        id: uid(),\r\n                                        title: eventName.value,\r\n                                        description: eventDescription.value,\r\n                                        location: eventLocation.value,\r\n                                        start: startDateTime,\r\n                                        end: endDateTime,\r\n                                        allDay: allDayEvent\r\n                                    });\r\n                                    calendar.render();\r\n\r\n                                    // Reset form for demo purposes only\r\n                                    form.reset();\r\n                                }\r\n                            });\r\n\r\n                            //form.submit(); // Submit form\r\n                        }, 2000);\r\n                    } else {\r\n                        // Show popup warning \r\n                        Swal.fire({\r\n                            text: \"Sorry, looks like there are some errors detected, please try again.\",\r\n                            icon: \"error\",\r\n                            buttonsStyling: false,\r\n                            confirmButtonText: \"Ok, got it!\",\r\n                            customClass: {\r\n                                confirmButton: \"btn btn-primary\"\r\n                            }\r\n                        });\r\n                    }\r\n                });\r\n            }\r\n        });\r\n    }\r\n\r\n    // Handle view event\r\n    const handleViewEvent = () => {\r\n        viewModal.show();\r\n\r\n        // Detect all day event\r\n        var eventNameMod;\r\n        var startDateMod;\r\n        var endDateMod;\r\n\r\n        // Generate labels\r\n        if (data.allDay) {\r\n            eventNameMod = 'All Day';\r\n            startDateMod = moment(data.startDate).format('Do MMM, YYYY');\r\n            endDateMod = moment(data.endDate).format('Do MMM, YYYY');\r\n        } else {\r\n            eventNameMod = '';\r\n            startDateMod = moment(data.startDate).format('Do MMM, YYYY - h:mm a');\r\n            endDateMod = moment(data.endDate).format('Do MMM, YYYY - h:mm a');\r\n        }\r\n\r\n        // Populate view data\r\n        viewEventName.innerText = data.eventName;\r\n        viewAllDay.innerText = eventNameMod;\r\n        viewEventDescription.innerText = data.eventDescription ? data.eventDescription : '--';\r\n        viewEventLocation.innerText = data.eventLocation ? data.eventLocation : '--';\r\n        viewStartDate.innerText = startDateMod;\r\n        viewEndDate.innerText = endDateMod;\r\n    }\r\n\r\n    // Handle delete event\r\n    const handleDeleteEvent = () => {\r\n        viewDeleteButton.addEventListener('click', e => {\r\n            e.preventDefault();\r\n\r\n            Swal.fire({\r\n                text: \"Are you sure you would like to delete this event?\",\r\n                icon: \"warning\",\r\n                showCancelButton: true,\r\n                buttonsStyling: false,\r\n                confirmButtonText: \"Yes, delete it!\",\r\n                cancelButtonText: \"No, return\",\r\n                customClass: {\r\n                    confirmButton: \"btn btn-primary\",\r\n                    cancelButton: \"btn btn-active-light\"\r\n                }\r\n            }).then(function (result) {\r\n                if (result.value) {\r\n                    calendar.getEventById(data.id).remove();\r\n\r\n                    viewModal.hide(); // Hide modal\t\t\t\t\r\n                } else if (result.dismiss === 'cancel') {\r\n                    Swal.fire({\r\n                        text: \"Your event was not deleted!.\",\r\n                        icon: \"error\",\r\n                        buttonsStyling: false,\r\n                        confirmButtonText: \"Ok, got it!\",\r\n                        customClass: {\r\n                            confirmButton: \"btn btn-primary\",\r\n                        }\r\n                    });\r\n                }\r\n            });\r\n        });\r\n    }\r\n\r\n    // Handle edit button\r\n    const handleEditButton = () => {\r\n        viewEditButton.addEventListener('click', e => {\r\n            e.preventDefault();\r\n\r\n            viewModal.hide();\r\n            handleEditEvent();\r\n        });\r\n    }\r\n\r\n    // Handle cancel button\r\n    const handleCancelButton = () => {\r\n        // Edit event modal cancel button\r\n        cancelButton.addEventListener('click', function (e) {\r\n            e.preventDefault();\r\n\r\n            Swal.fire({\r\n                text: \"Are you sure you would like to cancel?\",\r\n                icon: \"warning\",\r\n                showCancelButton: true,\r\n                buttonsStyling: false,\r\n                confirmButtonText: \"Yes, cancel it!\",\r\n                cancelButtonText: \"No, return\",\r\n                customClass: {\r\n                    confirmButton: \"btn btn-primary\",\r\n                    cancelButton: \"btn btn-active-light\"\r\n                }\r\n            }).then(function (result) {\r\n                if (result.value) {\r\n                    form.reset(); // Reset form\t\r\n                    modal.hide(); // Hide modal\t\t\t\t\r\n                } else if (result.dismiss === 'cancel') {\r\n                    Swal.fire({\r\n                        text: \"Your form has not been cancelled!.\",\r\n                        icon: \"error\",\r\n                        buttonsStyling: false,\r\n                        confirmButtonText: \"Ok, got it!\",\r\n                        customClass: {\r\n                            confirmButton: \"btn btn-primary\",\r\n                        }\r\n                    });\r\n                }\r\n            });\r\n        });\r\n    }\r\n\r\n    // Handle close button\r\n    const handleCloseButton = () => {\r\n        // Edit event modal close button\r\n        closeButton.addEventListener('click', function (e) {\r\n            e.preventDefault();\r\n\r\n            Swal.fire({\r\n                text: \"Are you sure you would like to cancel?\",\r\n                icon: \"warning\",\r\n                showCancelButton: true,\r\n                buttonsStyling: false,\r\n                confirmButtonText: \"Yes, cancel it!\",\r\n                cancelButtonText: \"No, return\",\r\n                customClass: {\r\n                    confirmButton: \"btn btn-primary\",\r\n                    cancelButton: \"btn btn-active-light\"\r\n                }\r\n            }).then(function (result) {\r\n                if (result.value) {\r\n                    form.reset(); // Reset form\t\r\n                    modal.hide(); // Hide modal\t\t\t\t\r\n                } else if (result.dismiss === 'cancel') {\r\n                    Swal.fire({\r\n                        text: \"Your form has not been cancelled!.\",\r\n                        icon: \"error\",\r\n                        buttonsStyling: false,\r\n                        confirmButtonText: \"Ok, got it!\",\r\n                        customClass: {\r\n                            confirmButton: \"btn btn-primary\",\r\n                        }\r\n                    });\r\n                }\r\n            });\r\n        });\r\n    }\r\n\r\n    // Handle view button\r\n    const handleViewButton = () => {\r\n        const viewButton = document.querySelector('#kt_calendar_event_view_button');\r\n        viewButton.addEventListener('click', e => {\r\n            e.preventDefault();\r\n\r\n            hidePopovers();\r\n            handleViewEvent();\r\n        });\r\n    }\r\n\r\n    // Helper functions\r\n\r\n    // Reset form validator on modal close\r\n    const resetFormValidator = (element) => {\r\n        // Target modal hidden event --- For more info: https://getbootstrap.com/docs/5.0/components/modal/#events\r\n        element.addEventListener('hidden.bs.modal', e => {\r\n            if (validator) {\r\n                // Reset form validator. For more info: https://formvalidation.io/guide/api/reset-form\r\n                validator.resetForm(true);\r\n            }\r\n        });\r\n    }\r\n\r\n    // Populate form \r\n    const populateForm = () => {\r\n        eventName.value = data.eventName ? data.eventName : '';\r\n        eventDescription.value = data.eventDescription ? data.eventDescription : '';\r\n        eventLocation.value = data.eventLocation ? data.eventLocation : '';\r\n        startFlatpickr.setDate(data.startDate, true, 'Y-m-d');\r\n\r\n        // Handle null end dates\r\n        const endDate = data.endDate ? data.endDate : moment(data.startDate).format();\r\n        endFlatpickr.setDate(endDate, true, 'Y-m-d');\r\n\r\n        const allDayToggle = form.querySelector('#kt_calendar_datepicker_allday');\r\n        const datepickerWrappers = form.querySelectorAll('[data-kt-calendar=\"datepicker\"]');\r\n        if (data.allDay) {\r\n            allDayToggle.checked = true;\r\n            datepickerWrappers.forEach(dw => {\r\n                dw.classList.add('d-none');\r\n            });\r\n        } else {\r\n            startTimeFlatpickr.setDate(data.startDate, true, 'Y-m-d H:i');\r\n            endTimeFlatpickr.setDate(data.endDate, true, 'Y-m-d H:i');\r\n            endFlatpickr.setDate(data.startDate, true, 'Y-m-d');\r\n            allDayToggle.checked = false;\r\n            datepickerWrappers.forEach(dw => {\r\n                dw.classList.remove('d-none');\r\n            });\r\n        }\r\n    }\r\n\r\n    // Format FullCalendar reponses\r\n    const formatArgs = (res) => {\r\n        data.id = res.id;\r\n        data.eventName = res.title;\r\n        data.eventDescription = res.description;\r\n        data.eventLocation = res.location;\r\n        data.startDate = res.startStr;\r\n        data.endDate = res.endStr;\r\n        data.allDay = res.allDay;\r\n    }\r\n\r\n    // Generate unique IDs for events\r\n    const uid = () => {\r\n        return Date.now().toString() + Math.floor(Math.random() * 1000).toString();\r\n    }\r\n\r\n    return {\r\n        // Public Functions\r\n        init: function () {\r\n            // Define variables\r\n            // Add event modal\r\n            const element = document.getElementById('kt_modal_add_event');\r\n            form = element.querySelector('#kt_modal_add_event_form');\r\n            eventName = form.querySelector('[name=\"calendar_event_name\"]');\r\n            eventDescription = form.querySelector('[name=\"calendar_event_description\"]');\r\n            eventLocation = form.querySelector('[name=\"calendar_event_location\"]');\r\n            startDatepicker = form.querySelector('#kt_calendar_datepicker_start_date');\r\n            endDatepicker = form.querySelector('#kt_calendar_datepicker_end_date');\r\n            startTimepicker = form.querySelector('#kt_calendar_datepicker_start_time');\r\n            endTimepicker = form.querySelector('#kt_calendar_datepicker_end_time');\r\n            addButton = document.querySelector('[data-kt-calendar=\"add\"]');\r\n            submitButton = form.querySelector('#kt_modal_add_event_submit');\r\n            cancelButton = form.querySelector('#kt_modal_add_event_cancel');\r\n            closeButton = element.querySelector('#kt_modal_add_event_close');\r\n            modalTitle = form.querySelector('[data-kt-calendar=\"title\"]');\r\n            modal = new bootstrap.Modal(element);\r\n\r\n            // View event modal\r\n            const viewElement = document.getElementById('kt_modal_view_event');\r\n            viewModal = new bootstrap.Modal(viewElement);\r\n            viewEventName = viewElement.querySelector('[data-kt-calendar=\"event_name\"]');\r\n            viewAllDay = viewElement.querySelector('[data-kt-calendar=\"all_day\"]');\r\n            viewEventDescription = viewElement.querySelector('[data-kt-calendar=\"event_description\"]');\r\n            viewEventLocation = viewElement.querySelector('[data-kt-calendar=\"event_location\"]');\r\n            viewStartDate = viewElement.querySelector('[data-kt-calendar=\"event_start_date\"]');\r\n            viewEndDate = viewElement.querySelector('[data-kt-calendar=\"event_end_date\"]');\r\n            viewEditButton = viewElement.querySelector('#kt_modal_view_event_edit');\r\n            viewDeleteButton = viewElement.querySelector('#kt_modal_view_event_delete');\r\n\r\n            initCalendarApp();\r\n            initValidator();\r\n            initDatepickers();\r\n            handleEditButton();\r\n            handleAddButton();\r\n            handleDeleteEvent();\r\n            handleCancelButton();\r\n            handleCloseButton();\r\n            resetFormValidator(element);\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function () {\r\n    KTAppCalendar.init();\r\n});\r\n"], "names": [], "sourceRoot": ""}