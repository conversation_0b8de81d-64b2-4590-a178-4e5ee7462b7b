{"version": 3, "file": "js/custom/apps/user-management/users/list/table.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kEAAkE;AAClE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA,cAAc;AACd;AACA;AACA,cAAc;AACd;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yFAAyF;AACzF;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,8BAA8B;AAChD,kBAAkB,8BAA8B;AAChD;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA,yBAAyB;AACzB;AACA;AACA,yBAAyB;AACzB,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA,iBAAiB;AACjB,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA,qBAAqB;AACrB,0CAA0C;AAC1C,6CAA6C;AAC7C,qBAAqB;AACrB,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC,E", "sources": ["webpack://keenthemes/../../../themes/metronic/html/demo3/src/js/custom/apps/user-management/users/list/table.js"], "sourcesContent": ["\"use strict\";\r\n\r\nvar KTUsersList = function () {\r\n    // Define shared variables\r\n    var table = document.getElementById('kt_table_users');\r\n    var datatable;\r\n    var toolbarBase;\r\n    var toolbarSelected;\r\n    var selectedCount;\r\n\r\n    // Private functions\r\n    var initUserTable = function () {\r\n        // Set date data order\r\n        const tableRows = table.querySelectorAll('tbody tr');\r\n\r\n        tableRows.forEach(row => {\r\n            const dateRow = row.querySelectorAll('td');\r\n            const lastLogin = dateRow[3].innerText.toLowerCase(); // Get last login time\r\n            let timeCount = 0;\r\n            let timeFormat = 'minutes';\r\n\r\n            // Determine date & time format -- add more formats when necessary\r\n            if (lastLogin.includes('yesterday')) {\r\n                timeCount = 1;\r\n                timeFormat = 'days';\r\n            } else if (lastLogin.includes('mins')) {\r\n                timeCount = parseInt(lastLogin.replace(/\\D/g, ''));\r\n                timeFormat = 'minutes';\r\n            } else if (lastLogin.includes('hours')) {\r\n                timeCount = parseInt(lastLogin.replace(/\\D/g, ''));\r\n                timeFormat = 'hours';\r\n            } else if (lastLogin.includes('days')) {\r\n                timeCount = parseInt(lastLogin.replace(/\\D/g, ''));\r\n                timeFormat = 'days';\r\n            } else if (lastLogin.includes('weeks')) {\r\n                timeCount = parseInt(lastLogin.replace(/\\D/g, ''));\r\n                timeFormat = 'weeks';\r\n            }\r\n\r\n            // Subtract date/time from today -- more info on moment datetime subtraction: https://momentjs.com/docs/#/durations/subtract/\r\n            const realDate = moment().subtract(timeCount, timeFormat).format();\r\n\r\n            // Insert real date to last login attribute\r\n            dateRow[3].setAttribute('data-order', realDate);\r\n\r\n            // Set real date for joined column\r\n            const joinedDate = moment(dateRow[5].innerHTML, \"DD MMM YYYY, LT\").format(); // select date from 5th column in table\r\n            dateRow[5].setAttribute('data-order', joinedDate);\r\n        });\r\n\r\n        // Init datatable --- more info on datatables: https://datatables.net/manual/\r\n        datatable = $(table).DataTable({\r\n            \"info\": false,\r\n            'order': [],\r\n            \"pageLength\": 10,\r\n            \"lengthChange\": false,\r\n            'columnDefs': [\r\n                { orderable: false, targets: 0 }, // Disable ordering on column 0 (checkbox)\r\n                { orderable: false, targets: 6 }, // Disable ordering on column 6 (actions)                \r\n            ]\r\n        });\r\n\r\n        // Re-init functions on every table re-draw -- more info: https://datatables.net/reference/event/draw\r\n        datatable.on('draw', function () {\r\n            initToggleToolbar();\r\n            handleDeleteRows();\r\n            toggleToolbars();\r\n        });\r\n    }\r\n\r\n    // Search Datatable --- official docs reference: https://datatables.net/reference/api/search()\r\n    var handleSearchDatatable = () => {\r\n        const filterSearch = document.querySelector('[data-kt-user-table-filter=\"search\"]');\r\n        filterSearch.addEventListener('keyup', function (e) {\r\n            datatable.search(e.target.value).draw();\r\n        });\r\n    }\r\n\r\n    // Filter Datatable\r\n    var handleFilterDatatable = () => {\r\n        // Select filter options\r\n        const filterForm = document.querySelector('[data-kt-user-table-filter=\"form\"]');\r\n        const filterButton = filterForm.querySelector('[data-kt-user-table-filter=\"filter\"]');\r\n        const selectOptions = filterForm.querySelectorAll('select');\r\n\r\n        // Filter datatable on submit\r\n        filterButton.addEventListener('click', function () {\r\n            var filterString = '';\r\n\r\n            // Get filter values\r\n            selectOptions.forEach((item, index) => {\r\n                if (item.value && item.value !== '') {\r\n                    if (index !== 0) {\r\n                        filterString += ' ';\r\n                    }\r\n\r\n                    // Build filter value options\r\n                    filterString += item.value;\r\n                }\r\n            });\r\n\r\n            // Filter datatable --- official docs reference: https://datatables.net/reference/api/search()\r\n            datatable.search(filterString).draw();\r\n        });\r\n    }\r\n\r\n    // Reset Filter\r\n    var handleResetForm = () => {\r\n        // Select reset button\r\n        const resetButton = document.querySelector('[data-kt-user-table-filter=\"reset\"]');\r\n\r\n        // Reset datatable\r\n        resetButton.addEventListener('click', function () {\r\n            // Select filter options\r\n            const filterForm = document.querySelector('[data-kt-user-table-filter=\"form\"]');\r\n            const selectOptions = filterForm.querySelectorAll('select');\r\n\r\n            // Reset select2 values -- more info: https://select2.org/programmatic-control/add-select-clear-items\r\n            selectOptions.forEach(select => {\r\n                $(select).val('').trigger('change');\r\n            });\r\n\r\n            // Reset datatable --- official docs reference: https://datatables.net/reference/api/search()\r\n            datatable.search('').draw();\r\n        });\r\n    }\r\n\r\n\r\n    // Delete subscirption\r\n    var handleDeleteRows = () => {\r\n        // Select all delete buttons\r\n        const deleteButtons = table.querySelectorAll('[data-kt-users-table-filter=\"delete_row\"]');\r\n\r\n        deleteButtons.forEach(d => {\r\n            // Delete button on click\r\n            d.addEventListener('click', function (e) {\r\n                e.preventDefault();\r\n\r\n                // Select parent row\r\n                const parent = e.target.closest('tr');\r\n\r\n                // Get user name\r\n                const userName = parent.querySelectorAll('td')[1].querySelectorAll('a')[1].innerText;\r\n\r\n                // SweetAlert2 pop up --- official docs reference: https://sweetalert2.github.io/\r\n                Swal.fire({\r\n                    text: \"Are you sure you want to delete \" + userName + \"?\",\r\n                    icon: \"warning\",\r\n                    showCancelButton: true,\r\n                    buttonsStyling: false,\r\n                    confirmButtonText: \"Yes, delete!\",\r\n                    cancelButtonText: \"No, cancel\",\r\n                    customClass: {\r\n                        confirmButton: \"btn fw-bold btn-danger\",\r\n                        cancelButton: \"btn fw-bold btn-active-light-primary\"\r\n                    }\r\n                }).then(function (result) {\r\n                    if (result.value) {\r\n                        Swal.fire({\r\n                            text: \"You have deleted \" + userName + \"!.\",\r\n                            icon: \"success\",\r\n                            buttonsStyling: false,\r\n                            confirmButtonText: \"Ok, got it!\",\r\n                            customClass: {\r\n                                confirmButton: \"btn fw-bold btn-primary\",\r\n                            }\r\n                        }).then(function () {\r\n                            // Remove current row\r\n                            datatable.row($(parent)).remove().draw();\r\n                        }).then(function () {\r\n                            // Detect checked checkboxes\r\n                            toggleToolbars();\r\n                        });\r\n                    } else if (result.dismiss === 'cancel') {\r\n                        Swal.fire({\r\n                            text: customerName + \" was not deleted.\",\r\n                            icon: \"error\",\r\n                            buttonsStyling: false,\r\n                            confirmButtonText: \"Ok, got it!\",\r\n                            customClass: {\r\n                                confirmButton: \"btn fw-bold btn-primary\",\r\n                            }\r\n                        });\r\n                    }\r\n                });\r\n            })\r\n        });\r\n    }\r\n\r\n    // Init toggle toolbar\r\n    var initToggleToolbar = () => {\r\n        // Toggle selected action toolbar\r\n        // Select all checkboxes\r\n        const checkboxes = table.querySelectorAll('[type=\"checkbox\"]');\r\n\r\n        // Select elements\r\n        toolbarBase = document.querySelector('[data-kt-user-table-toolbar=\"base\"]');\r\n        toolbarSelected = document.querySelector('[data-kt-user-table-toolbar=\"selected\"]');\r\n        selectedCount = document.querySelector('[data-kt-user-table-select=\"selected_count\"]');\r\n        const deleteSelected = document.querySelector('[data-kt-user-table-select=\"delete_selected\"]');\r\n\r\n        // Toggle delete selected toolbar\r\n        checkboxes.forEach(c => {\r\n            // Checkbox on click event\r\n            c.addEventListener('click', function () {\r\n                setTimeout(function () {\r\n                    toggleToolbars();\r\n                }, 50);\r\n            });\r\n        });\r\n\r\n        // Deleted selected rows\r\n        deleteSelected.addEventListener('click', function () {\r\n            // SweetAlert2 pop up --- official docs reference: https://sweetalert2.github.io/\r\n            Swal.fire({\r\n                text: \"Are you sure you want to delete selected customers?\",\r\n                icon: \"warning\",\r\n                showCancelButton: true,\r\n                buttonsStyling: false,\r\n                confirmButtonText: \"Yes, delete!\",\r\n                cancelButtonText: \"No, cancel\",\r\n                customClass: {\r\n                    confirmButton: \"btn fw-bold btn-danger\",\r\n                    cancelButton: \"btn fw-bold btn-active-light-primary\"\r\n                }\r\n            }).then(function (result) {\r\n                if (result.value) {\r\n                    Swal.fire({\r\n                        text: \"You have deleted all selected customers!.\",\r\n                        icon: \"success\",\r\n                        buttonsStyling: false,\r\n                        confirmButtonText: \"Ok, got it!\",\r\n                        customClass: {\r\n                            confirmButton: \"btn fw-bold btn-primary\",\r\n                        }\r\n                    }).then(function () {\r\n                        // Remove all selected customers\r\n                        checkboxes.forEach(c => {\r\n                            if (c.checked) {\r\n                                datatable.row($(c.closest('tbody tr'))).remove().draw();\r\n                            }\r\n                        });\r\n\r\n                        // Remove header checked box\r\n                        const headerCheckbox = table.querySelectorAll('[type=\"checkbox\"]')[0];\r\n                        headerCheckbox.checked = false;\r\n                    }).then(function () {\r\n                        toggleToolbars(); // Detect checked checkboxes\r\n                        initToggleToolbar(); // Re-init toolbar to recalculate checkboxes\r\n                    });\r\n                } else if (result.dismiss === 'cancel') {\r\n                    Swal.fire({\r\n                        text: \"Selected customers was not deleted.\",\r\n                        icon: \"error\",\r\n                        buttonsStyling: false,\r\n                        confirmButtonText: \"Ok, got it!\",\r\n                        customClass: {\r\n                            confirmButton: \"btn fw-bold btn-primary\",\r\n                        }\r\n                    });\r\n                }\r\n            });\r\n        });\r\n    }\r\n\r\n    // Toggle toolbars\r\n    const toggleToolbars = () => {\r\n        // Select refreshed checkbox DOM elements \r\n        const allCheckboxes = table.querySelectorAll('tbody [type=\"checkbox\"]');\r\n\r\n        // Detect checkboxes state & count\r\n        let checkedState = false;\r\n        let count = 0;\r\n\r\n        // Count checked boxes\r\n        allCheckboxes.forEach(c => {\r\n            if (c.checked) {\r\n                checkedState = true;\r\n                count++;\r\n            }\r\n        });\r\n\r\n        // Toggle toolbars\r\n        if (checkedState) {\r\n            selectedCount.innerHTML = count;\r\n            toolbarBase.classList.add('d-none');\r\n            toolbarSelected.classList.remove('d-none');\r\n        } else {\r\n            toolbarBase.classList.remove('d-none');\r\n            toolbarSelected.classList.add('d-none');\r\n        }\r\n    }\r\n\r\n    return {\r\n        // Public functions  \r\n        init: function () {\r\n            if (!table) {\r\n                return;\r\n            }\r\n\r\n            initUserTable();\r\n            initToggleToolbar();\r\n            handleSearchDatatable();\r\n            handleResetForm();\r\n            handleDeleteRows();\r\n            handleFilterDatatable();\r\n\r\n        }\r\n    }\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function () {\r\n    KTUsersList.init();\r\n});"], "names": [], "sourceRoot": ""}