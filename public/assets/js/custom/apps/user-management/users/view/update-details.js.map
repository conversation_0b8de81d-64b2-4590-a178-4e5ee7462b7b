{"version": 3, "file": "js/custom/apps/user-management/users/view/update-details.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA,kCAAkC;AAClC,kCAAkC;AAClC,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA,kCAAkC;AAClC,kCAAkC;AAClC,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA,iBAAiB;AACjB;AACA,iCAAiC;AACjC,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC,E", "sources": ["webpack://keenthemes/../../../themes/metronic/html/demo3/src/js/custom/apps/user-management/users/view/update-details.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTUsersUpdateDetails = function () {\r\n    // Shared variables\r\n    const element = document.getElementById('kt_modal_update_details');\r\n    const form = element.querySelector('#kt_modal_update_user_form');\r\n    const modal = new bootstrap.Modal(element);\r\n\r\n    // Init add schedule modal\r\n    var initUpdateDetails = () => {\r\n\r\n        // Close button handler\r\n        const closeButton = element.querySelector('[data-kt-users-modal-action=\"close\"]');\r\n        closeButton.addEventListener('click', e => {\r\n            e.preventDefault();\r\n\r\n            Swal.fire({\r\n                text: \"Are you sure you would like to cancel?\",\r\n                icon: \"warning\",\r\n                showCancelButton: true,\r\n                buttonsStyling: false,\r\n                confirmButtonText: \"Yes, cancel it!\",\r\n                cancelButtonText: \"No, return\",\r\n                customClass: {\r\n                    confirmButton: \"btn btn-primary\",\r\n                    cancelButton: \"btn btn-active-light\"\r\n                }\r\n            }).then(function (result) {\r\n                if (result.value) {\r\n                    form.reset(); // Reset form\t\r\n                    modal.hide(); // Hide modal\t\t\t\t\r\n                } else if (result.dismiss === 'cancel') {\r\n                    Swal.fire({\r\n                        text: \"Your form has not been cancelled!.\",\r\n                        icon: \"error\",\r\n                        buttonsStyling: false,\r\n                        confirmButtonText: \"Ok, got it!\",\r\n                        customClass: {\r\n                            confirmButton: \"btn btn-primary\",\r\n                        }\r\n                    });\r\n                }\r\n            });\r\n        });\r\n\r\n        // Cancel button handler\r\n        const cancelButton = element.querySelector('[data-kt-users-modal-action=\"cancel\"]');\r\n        cancelButton.addEventListener('click', e => {\r\n            e.preventDefault();\r\n\r\n            Swal.fire({\r\n                text: \"Are you sure you would like to cancel?\",\r\n                icon: \"warning\",\r\n                showCancelButton: true,\r\n                buttonsStyling: false,\r\n                confirmButtonText: \"Yes, cancel it!\",\r\n                cancelButtonText: \"No, return\",\r\n                customClass: {\r\n                    confirmButton: \"btn btn-primary\",\r\n                    cancelButton: \"btn btn-active-light\"\r\n                }\r\n            }).then(function (result) {\r\n                if (result.value) {\r\n                    form.reset(); // Reset form\t\r\n                    modal.hide(); // Hide modal\t\t\t\t\r\n                } else if (result.dismiss === 'cancel') {\r\n                    Swal.fire({\r\n                        text: \"Your form has not been cancelled!.\",\r\n                        icon: \"error\",\r\n                        buttonsStyling: false,\r\n                        confirmButtonText: \"Ok, got it!\",\r\n                        customClass: {\r\n                            confirmButton: \"btn btn-primary\",\r\n                        }\r\n                    });\r\n                }\r\n            });\r\n        });\r\n\r\n        // Submit button handler\r\n        const submitButton = element.querySelector('[data-kt-users-modal-action=\"submit\"]');\r\n        submitButton.addEventListener('click', function (e) {\r\n            // Prevent default button action\r\n            e.preventDefault();\r\n\r\n            // Show loading indication\r\n            submitButton.setAttribute('data-kt-indicator', 'on');\r\n\r\n            // Disable button to avoid multiple click \r\n            submitButton.disabled = true;\r\n\r\n            // Simulate form submission. For more info check the plugin's official documentation: https://sweetalert2.github.io/\r\n            setTimeout(function () {\r\n                // Remove loading indication\r\n                submitButton.removeAttribute('data-kt-indicator');\r\n\r\n                // Enable button\r\n                submitButton.disabled = false;\r\n\r\n                // Show popup confirmation \r\n                Swal.fire({\r\n                    text: \"Form has been successfully submitted!\",\r\n                    icon: \"success\",\r\n                    buttonsStyling: false,\r\n                    confirmButtonText: \"Ok, got it!\",\r\n                    customClass: {\r\n                        confirmButton: \"btn btn-primary\"\r\n                    }\r\n                }).then(function (result) {\r\n                    if (result.isConfirmed) {\r\n                        modal.hide();\r\n                    }\r\n                });\r\n\r\n                //form.submit(); // Submit form\r\n            }, 2000);\r\n        });\r\n    }\r\n\r\n    return {\r\n        // Public functions\r\n        init: function () {\r\n            initUpdateDetails();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function () {\r\n    KTUsersUpdateDetails.init();\r\n});"], "names": [], "sourceRoot": ""}