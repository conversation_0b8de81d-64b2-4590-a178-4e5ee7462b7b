{"version": 3, "file": "js/custom/apps/user-management/users/view/view.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA,yBAAyB;AACzB,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA,iBAAiB;AACjB,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA,iCAAiC;AACjC,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA,kCAAkC;AAClC,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC,E", "sources": ["webpack://keenthemes/../../../themes/metronic/html/demo3/src/js/custom/apps/user-management/users/view/view.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTUsersViewMain = function () {\r\n\r\n    // Init login session button\r\n    var initLoginSession = () => {\r\n        const button = document.getElementById('kt_modal_sign_out_sesions');\r\n\r\n        button.addEventListener('click', e => {\r\n            e.preventDefault();\r\n\r\n            Swal.fire({\r\n                text: \"Are you sure you would like sign out all sessions?\",\r\n                icon: \"warning\",\r\n                showCancelButton: true,\r\n                buttonsStyling: false,\r\n                confirmButtonText: \"Yes, sign out!\",\r\n                cancelButtonText: \"No, return\",\r\n                customClass: {\r\n                    confirmButton: \"btn btn-primary\",\r\n                    cancelButton: \"btn btn-active-light\"\r\n                }\r\n            }).then(function (result) {\r\n                if (result.value) {\r\n                    Swal.fire({\r\n                        text: \"You have signed out all sessions!.\",\r\n                        icon: \"success\",\r\n                        buttonsStyling: false,\r\n                        confirmButtonText: \"Ok, got it!\",\r\n                        customClass: {\r\n                            confirmButton: \"btn btn-primary\",\r\n                        }\r\n                    });\r\n                } else if (result.dismiss === 'cancel') {\r\n                    Swal.fire({\r\n                        text: \"Your sessions are still preserved!.\",\r\n                        icon: \"error\",\r\n                        buttonsStyling: false,\r\n                        confirmButtonText: \"Ok, got it!\",\r\n                        customClass: {\r\n                            confirmButton: \"btn btn-primary\",\r\n                        }\r\n                    });\r\n                }\r\n            });\r\n        });\r\n    }\r\n\r\n\r\n    // Init sign out single user\r\n    var initSignOutUser = () => {\r\n        const signOutButtons = document.querySelectorAll('[data-kt-users-sign-out=\"single_user\"]');\r\n\r\n        signOutButtons.forEach(button => {\r\n            button.addEventListener('click', e => {\r\n                e.preventDefault();\r\n\r\n                const deviceName = button.closest('tr').querySelectorAll('td')[1].innerText;\r\n\r\n                Swal.fire({\r\n                    text: \"Are you sure you would like sign out \" + deviceName + \"?\",\r\n                    icon: \"warning\",\r\n                    showCancelButton: true,\r\n                    buttonsStyling: false,\r\n                    confirmButtonText: \"Yes, sign out!\",\r\n                    cancelButtonText: \"No, return\",\r\n                    customClass: {\r\n                        confirmButton: \"btn btn-primary\",\r\n                        cancelButton: \"btn btn-active-light\"\r\n                    }\r\n                }).then(function (result) {\r\n                    if (result.value) {\r\n                        Swal.fire({\r\n                            text: \"You have signed out \" + deviceName + \"!.\",\r\n                            icon: \"success\",\r\n                            buttonsStyling: false,\r\n                            confirmButtonText: \"Ok, got it!\",\r\n                            customClass: {\r\n                                confirmButton: \"btn btn-primary\",\r\n                            }\r\n                        }).then(function(){\r\n                            button.closest('tr').remove();\r\n                        });\r\n                    } else if (result.dismiss === 'cancel') {\r\n                        Swal.fire({\r\n                            text: deviceName + \"'s session is still preserved!.\",\r\n                            icon: \"error\",\r\n                            buttonsStyling: false,\r\n                            confirmButtonText: \"Ok, got it!\",\r\n                            customClass: {\r\n                                confirmButton: \"btn btn-primary\",\r\n                            }\r\n                        });\r\n                    }\r\n                });\r\n            });\r\n        });\r\n\r\n\r\n    }\r\n\r\n    // Delete two step authentication handler\r\n    const initDeleteTwoStep = () => {\r\n        const deleteButton = document.getElementById('kt_users_delete_two_step');\r\n\r\n        deleteButton.addEventListener('click', e => {\r\n            e.preventDefault();\r\n\r\n            Swal.fire({\r\n                text: \"Are you sure you would like remove this two-step authentication?\",\r\n                icon: \"warning\",\r\n                showCancelButton: true,\r\n                buttonsStyling: false,\r\n                confirmButtonText: \"Yes, remove it!\",\r\n                cancelButtonText: \"No, return\",\r\n                customClass: {\r\n                    confirmButton: \"btn btn-primary\",\r\n                    cancelButton: \"btn btn-active-light\"\r\n                }\r\n            }).then(function (result) {\r\n                if (result.value) {\r\n                    Swal.fire({\r\n                        text: \"You have removed this two-step authentication!.\",\r\n                        icon: \"success\",\r\n                        buttonsStyling: false,\r\n                        confirmButtonText: \"Ok, got it!\",\r\n                        customClass: {\r\n                            confirmButton: \"btn btn-primary\",\r\n                        }\r\n                    });\r\n                } else if (result.dismiss === 'cancel') {\r\n                    Swal.fire({\r\n                        text: \"Your two-step authentication is still valid!.\",\r\n                        icon: \"error\",\r\n                        buttonsStyling: false,\r\n                        confirmButtonText: \"Ok, got it!\",\r\n                        customClass: {\r\n                            confirmButton: \"btn btn-primary\",\r\n                        }\r\n                    });\r\n                }\r\n            });\r\n        })\r\n    }\r\n\r\n    // Email preference form handler\r\n    const initEmailPreferenceForm = () => {\r\n        // Define variables\r\n        const form = document.getElementById('kt_users_email_notification_form');\r\n        const submitButton = form.querySelector('#kt_users_email_notification_submit');\r\n        const cancelButton = form.querySelector('#kt_users_email_notification_cancel');\r\n\r\n        // Submit action handler\r\n        submitButton.addEventListener('click', e => {\r\n            e.preventDefault();\r\n\r\n            // Show loading indication\r\n            submitButton.setAttribute('data-kt-indicator', 'on');\r\n\r\n            // Disable button to avoid multiple click \r\n            submitButton.disabled = true;\r\n\r\n            // Simulate form submission. For more info check the plugin's official documentation: https://sweetalert2.github.io/\r\n            setTimeout(function () {\r\n                // Remove loading indication\r\n                submitButton.removeAttribute('data-kt-indicator');\r\n\r\n                // Enable button\r\n                submitButton.disabled = false;\r\n\r\n                // Show popup confirmation \r\n                Swal.fire({\r\n                    text: \"Form has been successfully submitted!\",\r\n                    icon: \"success\",\r\n                    buttonsStyling: false,\r\n                    confirmButtonText: \"Ok, got it!\",\r\n                    customClass: {\r\n                        confirmButton: \"btn btn-primary\"\r\n                    }\r\n                });\r\n\r\n                //form.submit(); // Submit form\r\n            }, 2000);\r\n        });\r\n\r\n        cancelButton.addEventListener('click', e => {\r\n            e.preventDefault();\r\n\r\n            Swal.fire({\r\n                text: \"Are you sure you would like to cancel?\",\r\n                icon: \"warning\",\r\n                showCancelButton: true,\r\n                buttonsStyling: false,\r\n                confirmButtonText: \"Yes, cancel it!\",\r\n                cancelButtonText: \"No, return\",\r\n                customClass: {\r\n                    confirmButton: \"btn btn-primary\",\r\n                    cancelButton: \"btn btn-active-light\"\r\n                }\r\n            }).then(function (result) {\r\n                if (result.value) {\r\n                    form.reset(); // Reset form\t\t\t\t\r\n                } else if (result.dismiss === 'cancel') {\r\n                    Swal.fire({\r\n                        text: \"Your form has not been cancelled!.\",\r\n                        icon: \"error\",\r\n                        buttonsStyling: false,\r\n                        confirmButtonText: \"Ok, got it!\",\r\n                        customClass: {\r\n                            confirmButton: \"btn btn-primary\",\r\n                        }\r\n                    });\r\n                }\r\n            });\r\n        });\r\n    }\r\n\r\n\r\n    return {\r\n        // Public functions\r\n        init: function () {\r\n            initLoginSession();\r\n            initSignOutUser();\r\n            initDeleteTwoStep();\r\n            initEmailPreferenceForm();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function () {\r\n    KTUsersViewMain.init();\r\n});"], "names": [], "sourceRoot": ""}