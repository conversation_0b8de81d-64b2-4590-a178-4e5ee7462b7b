{"version": 3, "file": "js/custom/apps/user-management/users/view/add-auth-app.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA,kCAAkC;AAClC;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC,E", "sources": ["webpack://keenthemes/../../../themes/metronic/html/demo3/src/js/custom/apps/user-management/users/view/add-auth-app.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTUsersAddAuthApp = function () {\r\n    // Shared variables\r\n    const element = document.getElementById('kt_modal_add_auth_app');\r\n    const modal = new bootstrap.Modal(element);\r\n\r\n    // Init add schedule modal\r\n    var initAddAuthApp = () => {\r\n\r\n        // Close button handler\r\n        const closeButton = element.querySelector('[data-kt-users-modal-action=\"close\"]');\r\n        closeButton.addEventListener('click', e => {\r\n            e.preventDefault();\r\n\r\n            Swal.fire({\r\n                text: \"Are you sure you would like to close?\",\r\n                icon: \"warning\",\r\n                showCancelButton: true,\r\n                buttonsStyling: false,\r\n                confirmButtonText: \"Yes, close it!\",\r\n                cancelButtonText: \"No, return\",\r\n                customClass: {\r\n                    confirmButton: \"btn btn-primary\",\r\n                    cancelButton: \"btn btn-active-light\"\r\n                }\r\n            }).then(function (result) {\r\n                if (result.value) {\r\n                    modal.hide(); // Hide modal\t\t\t\t\r\n                } \r\n            });\r\n        });\r\n\r\n    }\r\n\r\n    // QR code to text code swapper\r\n    var initCodeSwap = () => {\r\n        const qrCode = element.querySelector('[ data-kt-add-auth-action=\"qr-code\"]');\r\n        const textCode = element.querySelector('[ data-kt-add-auth-action=\"text-code\"]');\r\n        const qrCodeButton = element.querySelector('[ data-kt-add-auth-action=\"qr-code-button\"]');\r\n        const textCodeButton = element.querySelector('[ data-kt-add-auth-action=\"text-code-button\"]');\r\n        const qrCodeLabel = element.querySelector('[ data-kt-add-auth-action=\"qr-code-label\"]');\r\n        const textCodeLabel = element.querySelector('[ data-kt-add-auth-action=\"text-code-label\"]');\r\n\r\n        const toggleClass = () =>{\r\n            qrCode.classList.toggle('d-none');\r\n            qrCodeButton.classList.toggle('d-none');\r\n            qrCodeLabel.classList.toggle('d-none');\r\n            textCode.classList.toggle('d-none');\r\n            textCodeButton.classList.toggle('d-none');\r\n            textCodeLabel.classList.toggle('d-none');\r\n        }\r\n\r\n        // Swap to text code handler\r\n        textCodeButton.addEventListener('click', e =>{\r\n            e.preventDefault();\r\n\r\n            toggleClass();\r\n        });\r\n\r\n        qrCodeButton.addEventListener('click', e =>{\r\n            e.preventDefault();\r\n\r\n            toggleClass();\r\n        });\r\n    }\r\n\r\n    return {\r\n        // Public functions\r\n        init: function () {\r\n            initAddAuthApp();\r\n            initCodeSwap();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function () {\r\n    KTUsersAddAuthApp.init();\r\n});"], "names": [], "sourceRoot": ""}