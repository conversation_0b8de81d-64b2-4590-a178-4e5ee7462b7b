{"version": 3, "file": "js/custom/apps/user-management/users/view/add-task.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA,kCAAkC;AAClC,kCAAkC;AAClC,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA,kCAAkC;AAClC,kCAAkC;AAClC,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B;AAC7B;AACA;AACA;AACA,6BAA6B;AAC7B;AACA,6CAA6C;AAC7C,yBAAyB;AACzB,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA,iBAAiB;AACjB;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA,0CAA0C;AAC1C;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA,iBAAiB;AACjB,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC;AACjC;AACA;AACA;AACA,iCAAiC;AACjC;AACA,qDAAqD;AACrD,6BAA6B;AAC7B,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B;AAC7B;AACA,6BAA6B;AAC7B;AACA,qBAAqB;AACrB;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC,E", "sources": ["webpack://keenthemes/../../../themes/metronic/html/demo3/src/js/custom/apps/user-management/users/view/add-task.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTUsersAddTask = function () {\r\n    // Shared variables\r\n    const element = document.getElementById('kt_modal_add_task');\r\n    const form = element.querySelector('#kt_modal_add_task_form');\r\n    const modal = new bootstrap.Modal(element);\r\n\r\n    // Init add task modal\r\n    var initAddTask = () => {\r\n\r\n        // Init flatpickr -- for more info: https://flatpickr.js.org/\r\n        $(\"#kt_modal_add_task_datepicker\").flatpickr({\r\n            dateFormat: \"Y-m-d\",\r\n        });\r\n\r\n        // Init form validation rules. For more info check the FormValidation plugin's official documentation:https://formvalidation.io/\r\n        var validator = FormValidation.formValidation(\r\n            form,\r\n            {\r\n                fields: {\r\n                    'task_duedate': {\r\n                        validators: {\r\n                            notEmpty: {\r\n                                message: 'Task due date is required'\r\n                            }\r\n                        }\r\n                    },\r\n                    'task_name': {\r\n                        validators: {\r\n                            notEmpty: {\r\n                                message: 'Task name is required'\r\n                            }\r\n                        }\r\n                    },\r\n                },\r\n\r\n                plugins: {\r\n                    trigger: new FormValidation.plugins.Trigger(),\r\n                    bootstrap: new FormValidation.plugins.Bootstrap5({\r\n                        rowSelector: '.fv-row',\r\n                        eleInvalidClass: '',\r\n                        eleValidClass: ''\r\n                    })\r\n                }\r\n            }\r\n        );\r\n\r\n        // Close button handler\r\n        const closeButton = element.querySelector('[data-kt-users-modal-action=\"close\"]');\r\n        closeButton.addEventListener('click', e => {\r\n            e.preventDefault();\r\n\r\n            Swal.fire({\r\n                text: \"Are you sure you would like to cancel?\",\r\n                icon: \"warning\",\r\n                showCancelButton: true,\r\n                buttonsStyling: false,\r\n                confirmButtonText: \"Yes, cancel it!\",\r\n                cancelButtonText: \"No, return\",\r\n                customClass: {\r\n                    confirmButton: \"btn btn-primary\",\r\n                    cancelButton: \"btn btn-active-light\"\r\n                }\r\n            }).then(function (result) {\r\n                if (result.value) {\r\n                    form.reset(); // Reset form\t\r\n                    modal.hide(); // Hide modal\t\t\t\t\r\n                } else if (result.dismiss === 'cancel') {\r\n                    Swal.fire({\r\n                        text: \"Your form has not been cancelled!.\",\r\n                        icon: \"error\",\r\n                        buttonsStyling: false,\r\n                        confirmButtonText: \"Ok, got it!\",\r\n                        customClass: {\r\n                            confirmButton: \"btn btn-primary\",\r\n                        }\r\n                    });\r\n                }\r\n            });\r\n        });\r\n\r\n        // Cancel button handler\r\n        const cancelButton = element.querySelector('[data-kt-users-modal-action=\"cancel\"]');\r\n        cancelButton.addEventListener('click', e => {\r\n            e.preventDefault();\r\n\r\n            Swal.fire({\r\n                text: \"Are you sure you would like to cancel?\",\r\n                icon: \"warning\",\r\n                showCancelButton: true,\r\n                buttonsStyling: false,\r\n                confirmButtonText: \"Yes, cancel it!\",\r\n                cancelButtonText: \"No, return\",\r\n                customClass: {\r\n                    confirmButton: \"btn btn-primary\",\r\n                    cancelButton: \"btn btn-active-light\"\r\n                }\r\n            }).then(function (result) {\r\n                if (result.value) {\r\n                    form.reset(); // Reset form\t\r\n                    modal.hide(); // Hide modal\t\t\t\t\r\n                } else if (result.dismiss === 'cancel') {\r\n                    Swal.fire({\r\n                        text: \"Your form has not been cancelled!.\",\r\n                        icon: \"error\",\r\n                        buttonsStyling: false,\r\n                        confirmButtonText: \"Ok, got it!\",\r\n                        customClass: {\r\n                            confirmButton: \"btn btn-primary\",\r\n                        }\r\n                    });\r\n                }\r\n            });\r\n        });\r\n\r\n        // Submit button handler\r\n        const submitButton = element.querySelector('[data-kt-users-modal-action=\"submit\"]');\r\n        submitButton.addEventListener('click', function (e) {\r\n            // Prevent default button action\r\n            e.preventDefault();\r\n\r\n            // Validate form before submit\r\n            if (validator) {\r\n                validator.validate().then(function (status) {\r\n                    console.log('validated!');\r\n\r\n                    if (status == 'Valid') {\r\n                        // Show loading indication\r\n                        submitButton.setAttribute('data-kt-indicator', 'on');\r\n\r\n                        // Disable button to avoid multiple click \r\n                        submitButton.disabled = true;\r\n\r\n                        // Simulate form submission. For more info check the plugin's official documentation: https://sweetalert2.github.io/\r\n                        setTimeout(function () {\r\n                            // Remove loading indication\r\n                            submitButton.removeAttribute('data-kt-indicator');\r\n\r\n                            // Enable button\r\n                            submitButton.disabled = false;\r\n\r\n                            // Show popup confirmation \r\n                            Swal.fire({\r\n                                text: \"Form has been successfully submitted!\",\r\n                                icon: \"success\",\r\n                                buttonsStyling: false,\r\n                                confirmButtonText: \"Ok, got it!\",\r\n                                customClass: {\r\n                                    confirmButton: \"btn btn-primary\"\r\n                                }\r\n                            }).then(function (result) {\r\n                                if (result.isConfirmed) {\r\n                                    modal.hide();\r\n                                }\r\n                            });\r\n\r\n                            //form.submit(); // Submit form\r\n                        }, 2000);\r\n                    } else {\r\n                        // Show popup warning. For more info check the plugin's official documentation: https://sweetalert2.github.io/\r\n                        Swal.fire({\r\n                            text: \"Sorry, looks like there are some errors detected, please try again.\",\r\n                            icon: \"error\",\r\n                            buttonsStyling: false,\r\n                            confirmButtonText: \"Ok, got it!\",\r\n                            customClass: {\r\n                                confirmButton: \"btn btn-primary\"\r\n                            }\r\n                        });\r\n                    }\r\n                });\r\n            }\r\n        });\r\n    }\r\n\r\n    // Init update task status\r\n    var initUpdateTaskStatus = () => {\r\n        const allTaskMenus = document.querySelectorAll('[data-kt-menu-id=\"kt-users-tasks\"]');\r\n\r\n        allTaskMenus.forEach(el => {\r\n            const resetButton = el.querySelector('[data-kt-users-update-task-status=\"reset\"]');\r\n            const submitButton = el.querySelector('[data-kt-users-update-task-status=\"submit\"]');\r\n            const taskForm = el.querySelector('[data-kt-menu-id=\"kt-users-tasks-form\"]');\r\n\r\n            // Init form validation rules. For more info check the FormValidation plugin's official documentation:https://formvalidation.io/\r\n            var validator = FormValidation.formValidation(\r\n                taskForm,\r\n                {\r\n                    fields: {\r\n                        'task_status': {\r\n                            validators: {\r\n                                notEmpty: {\r\n                                    message: 'Task due date is required'\r\n                                }\r\n                            }\r\n                        },\r\n                    },\r\n\r\n                    plugins: {\r\n                        trigger: new FormValidation.plugins.Trigger(),\r\n                        bootstrap: new FormValidation.plugins.Bootstrap5({\r\n                            rowSelector: '.fv-row',\r\n                            eleInvalidClass: '',\r\n                            eleValidClass: ''\r\n                        })\r\n                    }\r\n                }\r\n            );\r\n\r\n            // Revalidate country field. For more info, plase visit the official plugin site: https://select2.org/\r\n            $(taskForm.querySelector('[name=\"task_status\"]')).on('change', function () {\r\n                // Revalidate the field when an option is chosen\r\n                validator.revalidateField('task_status');\r\n            });\r\n\r\n            // Reset action handler\r\n            resetButton.addEventListener('click', e => {\r\n                e.preventDefault();\r\n\r\n                Swal.fire({\r\n                    text: \"Are you sure you would like to reset?\",\r\n                    icon: \"warning\",\r\n                    showCancelButton: true,\r\n                    buttonsStyling: false,\r\n                    confirmButtonText: \"Yes, reset it!\",\r\n                    cancelButtonText: \"No, return\",\r\n                    customClass: {\r\n                        confirmButton: \"btn btn-primary\",\r\n                        cancelButton: \"btn btn-active-light\"\r\n                    }\r\n                }).then(function (result) {\r\n                    if (result.value) {\r\n                        taskForm.reset(); // Reset form\t\t\r\n                        el.hide();\r\n                    } else if (result.dismiss === 'cancel') {\r\n                        Swal.fire({\r\n                            text: \"Your form was not reset!.\",\r\n                            icon: \"error\",\r\n                            buttonsStyling: false,\r\n                            confirmButtonText: \"Ok, got it!\",\r\n                            customClass: {\r\n                                confirmButton: \"btn btn-primary\",\r\n                            }\r\n                        });\r\n                    }\r\n                });\r\n            });\r\n\r\n            // Submit action handler\r\n            submitButton.addEventListener('click', e => {\r\n                e.preventDefault();\r\n\r\n                // Validate form before submit\r\n                if (validator) {\r\n                    validator.validate().then(function (status) {\r\n                        console.log('validated!');\r\n\r\n                        if (status == 'Valid') {\r\n                            // Show loading indication\r\n                            submitButton.setAttribute('data-kt-indicator', 'on');\r\n\r\n                            // Disable button to avoid multiple click \r\n                            submitButton.disabled = true;\r\n\r\n                            // Simulate form submission. For more info check the plugin's official documentation: https://sweetalert2.github.io/\r\n                            setTimeout(function () {\r\n                                // Remove loading indication\r\n                                submitButton.removeAttribute('data-kt-indicator');\r\n\r\n                                // Enable button\r\n                                submitButton.disabled = false;\r\n\r\n                                // Show popup confirmation \r\n                                Swal.fire({\r\n                                    text: \"Form has been successfully submitted!\",\r\n                                    icon: \"success\",\r\n                                    buttonsStyling: false,\r\n                                    confirmButtonText: \"Ok, got it!\",\r\n                                    customClass: {\r\n                                        confirmButton: \"btn btn-primary\"\r\n                                    }\r\n                                }).then(function (result) {\r\n                                    if (result.isConfirmed) {\r\n                                        el.hide();\r\n                                    }\r\n                                });\r\n\r\n                                //taskForm.submit(); // Submit form\r\n                            }, 2000);\r\n                        } else {\r\n                            // Show popup warning. For more info check the plugin's official documentation: https://sweetalert2.github.io/\r\n                            Swal.fire({\r\n                                text: \"Sorry, looks like there are some errors detected, please try again.\",\r\n                                icon: \"error\",\r\n                                buttonsStyling: false,\r\n                                confirmButtonText: \"Ok, got it!\",\r\n                                customClass: {\r\n                                    confirmButton: \"btn btn-primary\"\r\n                                }\r\n                            }).then(function(){\r\n                                //el.show();\r\n                            });\r\n                        }\r\n                    });\r\n                }\r\n            });\r\n        });\r\n    }\r\n\r\n    return {\r\n        // Public functions\r\n        init: function () {\r\n            initAddTask();\r\n            initUpdateTaskStatus();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function () {\r\n    KTUsersAddTask.init();\r\n});"], "names": [], "sourceRoot": ""}