{"version": 3, "file": "js/custom/apps/user-management/permissions/list.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uFAAuF;AACvF;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,8BAA8B;AAChD,kBAAkB,8BAA8B;AAChD;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA,yBAAyB;AACzB,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA,iBAAiB;AACjB,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC,E", "sources": ["webpack://keenthemes/../../../themes/metronic/html/demo3/src/js/custom/apps/user-management/permissions/list.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTUsersPermissionsList = function () {\r\n    // Shared variables\r\n    var datatable;\r\n    var table;\r\n\r\n    // Init add schedule modal\r\n    var initPermissionsList = () => {\r\n        // Set date data order\r\n        const tableRows = table.querySelectorAll('tbody tr');\r\n\r\n        tableRows.forEach(row => {\r\n            const dateRow = row.querySelectorAll('td');\r\n            const realDate = moment(dateRow[2].innerHTML, \"DD MMM YYYY, LT\").format(); // select date from 2nd column in table\r\n            dateRow[2].setAttribute('data-order', realDate);\r\n        });\r\n\r\n         // Init datatable --- more info on datatables: https://datatables.net/manual/\r\n         datatable = $(table).DataTable({\r\n            \"info\": false,\r\n            'order': [],\r\n            'columnDefs': [\r\n                { orderable: false, targets: 1 }, // Disable ordering on column 1 (assigned)\r\n                { orderable: false, targets: 3 }, // Disable ordering on column 3 (actions)\r\n            ]\r\n        });        \r\n    }\r\n\r\n    // Search Datatable --- official docs reference: https://datatables.net/reference/api/search()\r\n    var handleSearchDatatable = () => {\r\n        const filterSearch = document.querySelector('[data-kt-permissions-table-filter=\"search\"]');\r\n        filterSearch.addEventListener('keyup', function (e) {\r\n            datatable.search(e.target.value).draw();\r\n        });\r\n    }\r\n\r\n    // Delete user\r\n    var handleDeleteRows = () => {\r\n        // Select all delete buttons\r\n        const deleteButtons = table.querySelectorAll('[data-kt-permissions-table-filter=\"delete_row\"]');\r\n\r\n        deleteButtons.forEach(d => {\r\n            // Delete button on click\r\n            d.addEventListener('click', function (e) {\r\n                e.preventDefault();\r\n\r\n                // Select parent row\r\n                const parent = e.target.closest('tr');\r\n\r\n                // Get permission name\r\n                const permissionName = parent.querySelectorAll('td')[0].innerText;\r\n\r\n                // SweetAlert2 pop up --- official docs reference: https://sweetalert2.github.io/\r\n                Swal.fire({\r\n                    text: \"Are you sure you want to delete \" + permissionName + \"?\",\r\n                    icon: \"warning\",\r\n                    showCancelButton: true,\r\n                    buttonsStyling: false,\r\n                    confirmButtonText: \"Yes, delete!\",\r\n                    cancelButtonText: \"No, cancel\",\r\n                    customClass: {\r\n                        confirmButton: \"btn fw-bold btn-danger\",\r\n                        cancelButton: \"btn fw-bold btn-active-light-primary\"\r\n                    }\r\n                }).then(function (result) {\r\n                    if (result.value) {\r\n                        Swal.fire({\r\n                            text: \"You have deleted \" + permissionName + \"!.\",\r\n                            icon: \"success\",\r\n                            buttonsStyling: false,\r\n                            confirmButtonText: \"Ok, got it!\",\r\n                            customClass: {\r\n                                confirmButton: \"btn fw-bold btn-primary\",\r\n                            }\r\n                        }).then(function () {\r\n                            // Remove current row\r\n                            datatable.row($(parent)).remove().draw();\r\n                        });\r\n                    } else if (result.dismiss === 'cancel') {\r\n                        Swal.fire({\r\n                            text: customerName + \" was not deleted.\",\r\n                            icon: \"error\",\r\n                            buttonsStyling: false,\r\n                            confirmButtonText: \"Ok, got it!\",\r\n                            customClass: {\r\n                                confirmButton: \"btn fw-bold btn-primary\",\r\n                            }\r\n                        });\r\n                    }\r\n                });\r\n            })\r\n        });\r\n    }\r\n\r\n\r\n    return {\r\n        // Public functions\r\n        init: function () {\r\n            table = document.querySelector('#kt_permissions_table');\r\n            \r\n            if (!table) {\r\n                return;\r\n            }\r\n\r\n            initPermissionsList();\r\n            handleSearchDatatable();\r\n            handleDeleteRows();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function () {\r\n    KTUsersPermissionsList.init();\r\n});"], "names": [], "sourceRoot": ""}