{"version": 3, "file": "js/custom/apps/subscriptions/add/advanced.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA,qBAAqB;AACrB,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC,E", "sources": ["webpack://keenthemes/../../../themes/metronic/html/demo3/src/js/custom/apps/subscriptions/add/advanced.js"], "sourcesContent": ["\"use strict\";\r\n\r\nvar KTSubscriptionsAdvanced = function () {\r\n    // Shared variables\r\n    var table;\r\n    var datatable;\r\n\r\n    var initCustomFieldsDatatable = function () {\r\n        // Define variables\r\n        const addButton = document.getElementById('kt_create_new_custom_fields_add');\r\n\r\n        // Duplicate input fields\r\n        const fieldName = table.querySelector('tbody tr td:first-child').innerHTML;\r\n        const fieldValue = table.querySelector('tbody tr td:nth-child(2)').innerHTML;\r\n        const deleteButton = table.querySelector('tbody tr td:last-child').innerHTML;\r\n\r\n        // Init datatable --- more info on datatables: https://datatables.net/manual/\r\n        datatable = $(table).DataTable({\r\n            \"info\": false,\r\n            'order': [],\r\n            'ordering': false,\r\n            'paging': false,\r\n            \"lengthChange\": false\r\n        });\r\n\r\n        // Define datatable row node\r\n        var rowNode;\r\n\r\n        // Handle add button\r\n        addButton.addEventListener('click', function (e) {\r\n            e.preventDefault();\r\n\r\n            rowNode = datatable.row.add([\r\n                fieldName,\r\n                fieldValue,\r\n                deleteButton\r\n            ]).draw().node();\r\n\r\n            // Add custom class to last column -- more info: https://datatables.net/forums/discussion/22341/row-add-cell-class\r\n            $(rowNode).find('td').eq(2).addClass('text-end');\r\n\r\n            // Re-calculate index\r\n            initCustomFieldRowIndex();\r\n        });\r\n    }\r\n\r\n    // Handle row index count\r\n    var initCustomFieldRowIndex = function() {\r\n        const tableRows = table.querySelectorAll('tbody tr');\r\n\r\n        tableRows.forEach((tr, index) => {\r\n            // add index number to input names & id\r\n            const fieldNameInput = tr.querySelector('td:first-child input');\r\n            const fieldValueInput = tr.querySelector('td:nth-child(2) input');\r\n            const fieldNameLabel = fieldNameInput.getAttribute('id');\r\n            const fieldValueLabel = fieldValueInput.getAttribute('id');\r\n\r\n            fieldNameInput.setAttribute('name', fieldNameLabel + '-' + index);\r\n            fieldValueInput.setAttribute('name', fieldValueLabel + '-' + index);\r\n        });\r\n    }\r\n\r\n    // Delete product\r\n    var deleteCustomField = function() {\r\n        KTUtil.on(table, '[data-kt-action=\"field_remove\"]', 'click', function(e) {\r\n            e.preventDefault();\r\n\r\n            // Select parent row\r\n            const parent = e.target.closest('tr');\r\n\r\n            // SweetAlert2 pop up --- official docs reference: https://sweetalert2.github.io/\r\n            Swal.fire({\r\n                text: \"Are you sure you want to delete this field ?\",\r\n                icon: \"warning\",\r\n                showCancelButton: true,\r\n                buttonsStyling: false,\r\n                confirmButtonText: \"Yes, delete!\",\r\n                cancelButtonText: \"No, cancel\",\r\n                customClass: {\r\n                    confirmButton: \"btn fw-bold btn-danger\",\r\n                    cancelButton: \"btn fw-bold btn-active-light-primary\"\r\n                }\r\n            }).then(function (result) {\r\n                if (result.value) {\r\n                    Swal.fire({\r\n                        text: \"You have deleted it!.\",\r\n                        icon: \"success\",\r\n                        buttonsStyling: false,\r\n                        confirmButtonText: \"Ok, got it!\",\r\n                        customClass: {\r\n                            confirmButton: \"btn fw-bold btn-primary\",\r\n                        }\r\n                    }).then(function () {\r\n                        // Remove current row\r\n                        datatable.row($(parent)).remove().draw();\r\n                    });\r\n                } else if (result.dismiss === 'cancel') {\r\n                    Swal.fire({\r\n                        text: \"It was not deleted.\",\r\n                        icon: \"error\",\r\n                        buttonsStyling: false,\r\n                        confirmButtonText: \"Ok, got it!\",\r\n                        customClass: {\r\n                            confirmButton: \"btn fw-bold btn-primary\",\r\n                        }\r\n                    })\r\n                }\r\n            });\r\n        });\r\n    }\r\n\r\n    return {\r\n        init: function () {\r\n            table = document.getElementById('kt_create_new_custom_fields');\r\n\r\n            initCustomFieldsDatatable();\r\n            initCustomFieldRowIndex();            \r\n            deleteCustomField();\r\n        }\r\n    }\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function () {\r\n    KTSubscriptionsAdvanced.init();\r\n});"], "names": [], "sourceRoot": ""}