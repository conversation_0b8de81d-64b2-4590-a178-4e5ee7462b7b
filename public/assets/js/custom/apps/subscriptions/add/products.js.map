{"version": 3, "file": "js/custom/apps/subscriptions/add/products.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA,qBAAqB;AACrB,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA,mBAAmB;AACnB,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA,IAAI;AACJ,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B;AAC1B,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC", "sources": ["webpack://keenthemes/../../../themes/metronic/html/demo3/src/js/custom/apps/subscriptions/add/products.js"], "sourcesContent": ["\"use strict\";\r\n\r\nvar KTSubscriptionsProducts = function () {\r\n    // Shared variables\r\n    var table;\r\n    var datatable;\r\n    var modalEl;\r\n    var modal;\r\n\r\n    var initDatatable = function() {\r\n        // Init datatable --- more info on datatables: https://datatables.net/manual/        \r\n        datatable = $(table).DataTable({\r\n            \"info\": false,\r\n            'order': [],\r\n            'ordering': false,\r\n            'paging': false,\r\n            \"lengthChange\": false\r\n        });\r\n    }\r\n\r\n    // Delete product\r\n    var deleteProduct = function() {\r\n        KTUtil.on(table, '[data-kt-action=\"product_remove\"]', 'click', function(e) {\r\n            e.preventDefault();\r\n\r\n            // Select parent row\r\n            const parent = e.target.closest('tr');\r\n\r\n            // Get customer name\r\n            const productName = parent.querySelectorAll('td')[0].innerText;\r\n\r\n            // SweetAlert2 pop up --- official docs reference: https://sweetalert2.github.io/\r\n            Swal.fire({\r\n                text: \"Are you sure you want to delete \" + productName + \"?\",\r\n                icon: \"warning\",\r\n                showCancelButton: true,\r\n                buttonsStyling: false,\r\n                confirmButtonText: \"Yes, delete!\",\r\n                cancelButtonText: \"No, cancel\",\r\n                customClass: {\r\n                    confirmButton: \"btn fw-bold btn-danger\",\r\n                    cancelButton: \"btn fw-bold btn-active-light-primary\"\r\n                }\r\n            }).then(function (result) {\r\n                if (result.value) {\r\n                    Swal.fire({\r\n                        text: \"You have deleted \" + productName + \"!.\",\r\n                        icon: \"success\",\r\n                        buttonsStyling: false,\r\n                        confirmButtonText: \"Ok, got it!\",\r\n                        customClass: {\r\n                            confirmButton: \"btn fw-bold btn-primary\",\r\n                        }\r\n                    }).then(function () {\r\n                        // Remove current row\r\n                        datatable.row($(parent)).remove().draw();\r\n                    });\r\n                } else if (result.dismiss === 'cancel') {\r\n                    Swal.fire({\r\n                        text: customerName + \" was not deleted.\",\r\n                        icon: \"error\",\r\n                        buttonsStyling: false,\r\n                        confirmButtonText: \"Ok, got it!\",\r\n                        customClass: {\r\n                            confirmButton: \"btn fw-bold btn-primary\",\r\n                        }\r\n                    });\r\n                }\r\n            });\r\n        });\r\n    }\r\n\r\n    // Modal handlers\r\n    var addProduct = function() {\r\n        // Select modal buttons\r\n        const closeButton = modalEl.querySelector('#kt_modal_add_product_close');\r\n        const cancelButton = modalEl.querySelector('#kt_modal_add_product_cancel');\r\n        const submitButton = modalEl.querySelector('#kt_modal_add_product_submit');\r\n\r\n        // Cancel button action\r\n        cancelButton.addEventListener('click', function(e){\r\n            e.preventDefault();\r\n\r\n\t\t\tSwal.fire({\r\n\t\t\t\ttext: \"Are you sure you would like to cancel?\",\r\n\t\t\t\ticon: \"warning\",\r\n\t\t\t\tshowCancelButton: true,\r\n\t\t\t\tbuttonsStyling: false,\r\n\t\t\t\tconfirmButtonText: \"Yes, cancel it!\",\r\n\t\t\t\tcancelButtonText: \"No, return\",\r\n\t\t\t\tcustomClass: {\r\n\t\t\t\t\tconfirmButton: \"btn btn-primary\",\r\n\t\t\t\t\tcancelButton: \"btn btn-active-light\"\r\n\t\t\t\t}\r\n\t\t\t}).then(function (result) {\r\n\t\t\t\tif (result.value) {\r\n\t\t\t\t\tmodal.hide(); // Hide modal\t\t\t\t\r\n\t\t\t\t} else if (result.dismiss === 'cancel') {\r\n\t\t\t\t\tSwal.fire({\r\n\t\t\t\t\t\ttext: \"Your form has not been cancelled!.\",\r\n\t\t\t\t\t\ticon: \"error\",\r\n\t\t\t\t\t\tbuttonsStyling: false,\r\n\t\t\t\t\t\tconfirmButtonText: \"Ok, got it!\",\r\n\t\t\t\t\t\tcustomClass: {\r\n\t\t\t\t\t\t\tconfirmButton: \"btn btn-primary\",\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t});\r\n        });\r\n\r\n        // Add customer button handler\r\n        submitButton.addEventListener('click', function (e) {\r\n            e.preventDefault();\r\n\r\n            // Check all radio buttons\r\n            var radio = modalEl.querySelector('input[type=\"radio\"]:checked');            \r\n\r\n            // Define datatable row node\r\n            var rowNode;\r\n\r\n            if (radio && radio.checked === true) {\r\n                rowNode = datatable.row.add( [\r\n                    radio.getAttribute('data-kt-product-name'),\r\n                    '1',\r\n                    radio.getAttribute('data-kt-product-price') + ' / ' + radio.getAttribute('data-kt-product-frequency'),\r\n                    table.querySelector('tbody tr td:last-child').innerHTML\r\n                ]).draw().node();\r\n\r\n                // Add custom class to last column -- more info: https://datatables.net/forums/discussion/22341/row-add-cell-class\r\n                $( rowNode ).find('td').eq(3).addClass('text-end');\r\n            }           \r\n\r\n            modal.hide(); // Remove modal\r\n        });\r\n    }\r\n\r\n    return {\r\n        init: function () {\r\n            modalEl = document.getElementById('kt_modal_add_product');\r\n\r\n            // Select modal -- more info on Bootstrap modal: https://getbootstrap.com/docs/5.0/components/modal/\r\n            modal = new bootstrap.Modal(modalEl);\r\n\r\n            table = document.querySelector('#kt_subscription_products_table');\r\n\r\n            initDatatable();\r\n            deleteProduct();\r\n            addProduct();\r\n        }\r\n    }\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function () {\r\n    KTSubscriptionsProducts.init();\r\n});\r\n"], "names": [], "sourceRoot": ""}