{"version": 3, "file": "js/custom/apps/subscriptions/list/export.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qCAAqC;AACrC;AACA,6BAA6B;AAC7B;AACA,6CAA6C;AAC7C,yBAAyB;AACzB,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA,6BAA6B;AAC7B,yBAAyB;AACzB;AACA,iBAAiB;AACjB;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA,kCAAkC;AAClC,kCAAkC;AAClC;AACA;AACA;AACA;AACA,qBAAqB;AACrB,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA,yBAAyB;AACzB,qBAAqB;AACrB;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA,kCAAkC;AAClC,kCAAkC;AAClC;AACA;AACA;AACA;AACA,qBAAqB;AACrB,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA,yBAAyB;AACzB,qBAAqB;AACrB;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC,E", "sources": ["webpack://keenthemes/../../../themes/metronic/html/demo3/src/js/custom/apps/subscriptions/list/export.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTSubscriptionsExport = function () {\r\n    var element;\r\n    var submitButton;\r\n    var cancelButton;\r\n    var closeButton;\r\n    var validator;\r\n    var form;\r\n    var modal;\r\n\r\n    // Init form inputs\r\n    var handleForm = function () {\r\n        // Init form validation rules. For more info check the FormValidation plugin's official documentation:https://formvalidation.io/\r\n        validator = FormValidation.formValidation(\r\n            form,\r\n            {\r\n                fields: {\r\n                    'date': {\r\n                        validators: {\r\n                            notEmpty: {\r\n                                message: 'Date range is required'\r\n                            }\r\n                        }\r\n                    },\r\n                },\r\n                plugins: {\r\n                    trigger: new FormValidation.plugins.Trigger(),\r\n                    bootstrap: new FormValidation.plugins.Bootstrap5({\r\n                        rowSelector: '.fv-row',\r\n                        eleInvalidClass: '',\r\n                        eleValidClass: ''\r\n                    })\r\n                }\r\n            }\r\n        );\r\n\r\n        // Action buttons\r\n        submitButton.addEventListener('click', function (e) {\r\n            e.preventDefault();\r\n\r\n            const dateEls = form.querySelectorAll(\"input\");\r\n\r\n            // Disable form on submit click\r\n            dateEls.forEach(dateEl => {\r\n                dateEl.disabled = true;\r\n            });\r\n\r\n\r\n            // Validate form before submit\r\n            if (validator) {\r\n                validator.validate().then(function (status) {\r\n                    console.log('validated!');\r\n\r\n                    if (status == 'Valid') {\r\n                        submitButton.setAttribute('data-kt-indicator', 'on');\r\n\r\n                        // Disable submit button whilst loading\r\n                        submitButton.disabled = true;\r\n\r\n                        setTimeout(function () {\r\n                            submitButton.removeAttribute('data-kt-indicator');\r\n\r\n                            Swal.fire({\r\n                                text: \"Customer list has been successfully exported!\",\r\n                                icon: \"success\",\r\n                                buttonsStyling: false,\r\n                                confirmButtonText: \"Ok, got it!\",\r\n                                customClass: {\r\n                                    confirmButton: \"btn btn-primary\"\r\n                                }\r\n                            }).then(function (result) {\r\n                                if (result.isConfirmed) {\r\n                                    modal.hide();\r\n\r\n                                    // Enable submit button after loading\r\n                                    submitButton.disabled = false;\r\n\r\n                                    // Enable datepicker after loading\r\n                                    dateEls.forEach(dateEl => {\r\n                                        dateEl.disabled = false;\r\n                                    });\r\n                                }\r\n                            });\r\n\r\n                            //form.submit(); // Submit form\r\n                        }, 2000);\r\n                    } else {\r\n                        Swal.fire({\r\n                            text: \"Sorry, looks like there are some errors detected, please try again.\",\r\n                            icon: \"error\",\r\n                            buttonsStyling: false,\r\n                            confirmButtonText: \"Ok, got it!\",\r\n                            customClass: {\r\n                                confirmButton: \"btn btn-primary\"\r\n                            }\r\n                        }).then(function(){\r\n                            // Enable datepicker after loading\r\n                            dateEls.forEach(dateEl => {\r\n                                dateEl.disabled = false;\r\n                            });           \r\n                        });\r\n                    }\r\n                });\r\n            }\r\n        });\r\n\r\n        cancelButton.addEventListener('click', function (e) {\r\n            e.preventDefault();\r\n\r\n            const dateEls = form.querySelectorAll(\"input\");\r\n\r\n            // Disable form on submit click\r\n            dateEls.forEach(dateEl => {\r\n                dateEl.disabled = true;\r\n            });\r\n\r\n\r\n            Swal.fire({\r\n                text: \"Are you sure you would like to cancel?\",\r\n                icon: \"warning\",\r\n                showCancelButton: true,\r\n                buttonsStyling: false,\r\n                confirmButtonText: \"Yes, cancel it!\",\r\n                cancelButtonText: \"No, return\",\r\n                customClass: {\r\n                    confirmButton: \"btn btn-primary\",\r\n                    cancelButton: \"btn btn-active-light\"\r\n                }\r\n            }).then(function (result) {\r\n                if (result.value) {\r\n                    form.reset(); // Reset form\t\r\n                    modal.hide(); // Hide modal\t\r\n                    \r\n                    // Enable datepicker after loading\r\n                    dateEls.forEach(dateEl => {\r\n                        dateEl.disabled = false;\r\n                    });        \t\t\t\r\n                } else if (result.dismiss === 'cancel') {\r\n                    Swal.fire({\r\n                        text: \"Your form has not been cancelled!.\",\r\n                        icon: \"error\",\r\n                        buttonsStyling: false,\r\n                        confirmButtonText: \"Ok, got it!\",\r\n                        customClass: {\r\n                            confirmButton: \"btn btn-primary\",\r\n                        }\r\n                    }).then(function(){\r\n                        // Enable datepicker after loading\r\n                        dateEls.forEach(dateEl => {\r\n                            dateEl.disabled = false;\r\n                        });           \r\n                    });\r\n                }\r\n            });\r\n        });\r\n\r\n        closeButton.addEventListener('click', function (e) {\r\n            e.preventDefault();\r\n\r\n            const dateEls = form.querySelectorAll(\"input\");\r\n\r\n            // Disable form on submit click\r\n            dateEls.forEach(dateEl => {\r\n                dateEl.disabled = true;\r\n            });\r\n\r\n\r\n            Swal.fire({\r\n                text: \"Are you sure you would like to cancel?\",\r\n                icon: \"warning\",\r\n                showCancelButton: true,\r\n                buttonsStyling: false,\r\n                confirmButtonText: \"Yes, cancel it!\",\r\n                cancelButtonText: \"No, return\",\r\n                customClass: {\r\n                    confirmButton: \"btn btn-primary\",\r\n                    cancelButton: \"btn btn-active-light\"\r\n                }\r\n            }).then(function (result) {\r\n                if (result.value) {\r\n                    form.reset(); // Reset form\t\r\n                    modal.hide(); // Hide modal\t\t\r\n                    \r\n                    // Enable datepicker after loading\r\n                    dateEls.forEach(dateEl => {\r\n                        dateEl.disabled = false;\r\n                    });        \r\n                } else if (result.dismiss === 'cancel') {\r\n                    Swal.fire({\r\n                        text: \"Your form has not been cancelled!.\",\r\n                        icon: \"error\",\r\n                        buttonsStyling: false,\r\n                        confirmButtonText: \"Ok, got it!\",\r\n                        customClass: {\r\n                            confirmButton: \"btn btn-primary\",\r\n                        }\r\n                    }).then(function(){\r\n                        // Enable datepicker after loading\r\n                        dateEls.forEach(dateEl => {\r\n                            dateEl.disabled = false;\r\n                        });           \r\n                    });\r\n                }\r\n            });\r\n        });\r\n    }\r\n\r\n    var initForm = function () {\r\n        const datepicker = form.querySelector(\"[name=date]\");\r\n\r\n        // Handle datepicker range -- For more info on flatpickr plugin, please visit: https://flatpickr.js.org/\r\n        $(datepicker).flatpickr({\r\n            altInput: true,\r\n            altFormat: \"F j, Y\",\r\n            dateFormat: \"Y-m-d\",\r\n            mode: \"range\"\r\n        });\r\n    }\r\n\r\n    return {\r\n        // Public functions\r\n        init: function () {\r\n            // Elements\r\n            element = document.querySelector('#kt_subscriptions_export_modal');\r\n            modal = new bootstrap.Modal(element);\r\n\r\n            form = document.querySelector('#kt_subscriptions_export_form');\r\n            submitButton = form.querySelector('#kt_subscriptions_export_submit');\r\n            cancelButton = form.querySelector('#kt_subscriptions_export_cancel');\r\n            closeButton = element.querySelector('#kt_subscriptions_export_close');\r\n\r\n            handleForm();\r\n            initForm();\r\n        }\r\n    };\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function () {\r\n    KTSubscriptionsExport.init();\r\n});"], "names": [], "sourceRoot": ""}