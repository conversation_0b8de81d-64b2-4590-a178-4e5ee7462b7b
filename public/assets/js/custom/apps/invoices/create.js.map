{"version": 3, "file": "js/custom/apps/invoices/create.js", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC", "sources": ["webpack://keenthemes/../../../themes/metronic/html/demo3/src/js/custom/apps/invoices/create.js"], "sourcesContent": ["\"use strict\";\r\n\r\n// Class definition\r\nvar KTAppInvoicesCreate = function () {\r\n    var form;\r\n\r\n\t// Private functions\r\n\tvar updateTotal = function() {\r\n\t\tvar items = [].slice.call(form.querySelectorAll('[data-kt-element=\"items\"] [data-kt-element=\"item\"]'));\r\n\t\tvar grandTotal = 0;\r\n\r\n\t\tvar format = wNumb({\r\n\t\t\t//prefix: '$ ',\r\n\t\t\tdecimals: 2,\r\n\t\t\tthousand: ','\r\n\t\t});\r\n\r\n\t\titems.map(function (item) {\r\n            var quantity = item.querySelector('[data-kt-element=\"quantity\"]');\r\n\t\t\tvar price = item.querySelector('[data-kt-element=\"price\"]');\r\n\r\n\t\t\tvar priceValue = format.from(price.value);\r\n\t\t\tpriceValue = (!priceValue || priceValue < 0) ? 0 : priceValue;\r\n\r\n\t\t\tvar quantityValue = parseInt(quantity.value);\r\n\t\t\tquantityValue = (!quantityValue || quantityValue < 0) ?  1 : quantityValue;\r\n\r\n\t\t\tprice.value = format.to(priceValue);\r\n\t\t\tquantity.value = quantityValue;\r\n\r\n\t\t\titem.querySelector('[data-kt-element=\"total\"]').innerText = format.to(priceValue * quantityValue);\t\t\t\r\n\r\n\t\t\tgrandTotal += priceValue * quantityValue;\r\n\t\t});\r\n\r\n\t\tform.querySelector('[data-kt-element=\"sub-total\"]').innerText = format.to(grandTotal);\r\n\t\tform.querySelector('[data-kt-element=\"grand-total\"]').innerText = format.to(grandTotal);\r\n\t}\r\n\r\n\tvar handleEmptyState = function() {\r\n\t\tif (form.querySelectorAll('[data-kt-element=\"items\"] [data-kt-element=\"item\"]').length === 0) {\r\n\t\t\tvar item = form.querySelector('[data-kt-element=\"empty-template\"] tr').cloneNode(true);\r\n\t\t\tform.querySelector('[data-kt-element=\"items\"] tbody').appendChild(item);\r\n\t\t} else {\r\n\t\t\tKTUtil.remove(form.querySelector('[data-kt-element=\"items\"] [data-kt-element=\"empty\"]'));\r\n\t\t}\r\n\t}\r\n\r\n\tvar handeForm = function (element) {\r\n\t\t// Add item\r\n\t\tform.querySelector('[data-kt-element=\"items\"] [data-kt-element=\"add-item\"]').addEventListener('click', function(e) {\r\n\t\t\te.preventDefault();\r\n\r\n\t\t\tvar item = form.querySelector('[data-kt-element=\"item-template\"] tr').cloneNode(true);\r\n\r\n\t\t\tform.querySelector('[data-kt-element=\"items\"] tbody').appendChild(item);\r\n\r\n\t\t\thandleEmptyState();\r\n\t\t\tupdateTotal();\t\t\t\r\n\t\t});\r\n\r\n\t\t// Remove item\r\n\t\tKTUtil.on(form, '[data-kt-element=\"items\"] [data-kt-element=\"remove-item\"]', 'click', function(e) {\r\n\t\t\te.preventDefault();\r\n\r\n\t\t\tKTUtil.remove(this.closest('[data-kt-element=\"item\"]'));\r\n\r\n\t\t\thandleEmptyState();\r\n\t\t\tupdateTotal();\t\t\t\r\n\t\t});\t\t\r\n\r\n\t\t// Handle price and quantity changes\r\n\t\tKTUtil.on(form, '[data-kt-element=\"items\"] [data-kt-element=\"quantity\"], [data-kt-element=\"items\"] [data-kt-element=\"price\"]', 'change', function(e) {\r\n\t\t\te.preventDefault();\r\n\r\n\t\t\tupdateTotal();\t\t\t\r\n\t\t});\r\n\t}\r\n\r\n\tvar initForm = function(element) {\r\n\t\t// Due date. For more info, please visit the official plugin site: https://flatpickr.js.org/\r\n\t\tvar invoiceDate = $(form.querySelector('[name=\"invoice_date\"]'));\r\n\t\tinvoiceDate.flatpickr({\r\n\t\t\tenableTime: false,\r\n\t\t\tdateFormat: \"d, M Y\",\r\n\t\t});\r\n\r\n        // Due date. For more info, please visit the official plugin site: https://flatpickr.js.org/\r\n\t\tvar dueDate = $(form.querySelector('[name=\"invoice_due_date\"]'));\r\n\t\tdueDate.flatpickr({\r\n\t\t\tenableTime: false,\r\n\t\t\tdateFormat: \"d, M Y\",\r\n\t\t});\r\n\t}\r\n\r\n\t// Public methods\r\n\treturn {\r\n\t\tinit: function(element) {\r\n            form = document.querySelector('#kt_invoice_form');\r\n\r\n\t\t\thandeForm();\r\n            initForm();\r\n\t\t\tupdateTotal();\r\n        }\r\n\t};\r\n}();\r\n\r\n// On document ready\r\nKTUtil.onDOMContentLoaded(function () {\r\n    KTAppInvoicesCreate.init();\r\n});\r\n"], "names": [], "sourceRoot": ""}