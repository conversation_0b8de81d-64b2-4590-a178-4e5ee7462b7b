<style>
        //variables
        $file-uploader__primaryColor: rgb(114, 191, 167);
        $file-uploader__primaryColor--hover: lighten($file-uploader__primaryColor, 15%);
        $file-uploader__black: #242424;
        $file-uploader__error: rgb(214, 93, 56);

        //style
        .file-uploader {
        background-color: lighten($file-uploader__primaryColor, 30%);
        border-radius: 3px;
        color: $file-uploader__black;
        }

        .file-uploader__message-area {
        font-size: 13px;
        padding: 1em;
        text-align: center;
        color: darken($file-uploader__primaryColor, 25%);
        }

        .file-list {
        background-color: lighten($file-uploader__primaryColor, 45%);
        font-size: 12px;
        }

        .file-list__name {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        }

        .file-list li {
        height: 50px;
        width: 96%;
        line-height: 50px;
        margin-left: 0.9em;
        margin: auto;
        border: none;
        overflow: hidden;
        }

        .removal-button {
        width: 30px;
        border: 2px solid rgb(255, 87, 51 );
        border-radius: 25px;
        background-color: rgb(255, 87, 51 );
        color: white;

        &::before {
            content: "X";
        }
        &:focus {
            outline: 0;
        }
        }

        .file-chooser {
        padding: 1em;
        transition: background-color 1s, height 1s;
        & p {
            font-size: 18px;
            padding-top: 1em;
        }
        }

        //layout
        .file-uploader {
        max-width: 400px;
        height: auto;
        margin: 2em auto;

        & * {
            display: block;
        }
        & input[type="submit"] {
            margin-top: 2em;
            float: right;
        }
        }

        .file-list {
        margin: 0 auto;
        max-width: 90%;
        }

        .file-list__name {
        max-width: 70%;
        float: left;
        }

        .file-list li {
        @extend %clearfix;
        }

        /* .removal-button {
        display: inline-block;
        height: 30px;
        float: right;
        } */

        .file-chooser {
        width: 90%;
        margin: 0.5em auto;
        }

        .file-chooser__input {
        margin: 0 auto;
        }

        .file-uploader__submit-button {
        width: 100%;
        border: none;
        font-size: 1.5em;
        padding: 1em;
        background-color: $file-uploader__primaryColor;
        color: white;
        &:hover {
            background-color: $file-uploader__primaryColor--hover;
        }
        }

        //layout
        .file-uploader {
        @extend %clearfix;
        }

        //utility

        %clearfix {
        &:after {
            content: "";
            display: table;
            clear: both;
        }
        }

        .hidden {
        display: none;
        & input {
            display: none;
        }
        }

        .error {
        background-color: $file-uploader__error;
        color: white;
        }

        //reset
        *,
        *::before,
        *::after {
        box-sizing: border-box;
        }
        ul,
        li {
        margin: 0;
        padding: 0;
        }
</style>