<?php

namespace Database\Factories;

use App\Models\Checking;
use App\Models\Project;
use App\Models\TodolistCategory;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class CheckingFactory extends Factory
{
    protected $model = Checking::class;

    public function definition()
    {
        return [
            'project_id' => Project::exists() ? Project::inRandomOrder()->first()->id : Project::factory(),
            'todolist_category_id' => TodolistCategory::exists() ? TodolistCategory::inRandomOrder()->first()->id : TodolistCategory::factory(),
            'creator_id' => User::exists() ? User::inRandomOrder()->first()->id : User::factory(),
            'updater_id' => User::exists() ? User::inRandomOrder()->first()->id : User::factory(),
        ];
    }
}
