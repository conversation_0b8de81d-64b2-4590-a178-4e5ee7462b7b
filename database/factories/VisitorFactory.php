<?php

namespace Database\Factories;

use App\Models\Visitor;
use App\Models\User;
use App\Models\Project;
use Illuminate\Database\Eloquent\Factories\Factory;

class VisitorFactory extends Factory
{
    protected $model = Visitor::class;

    public function definition()
    {
        return [
            'user_id' => User::exists() ? User::inRandomOrder()->first()->id : User::factory(),
            'project_id' => Project::exists() ? Project::inRandomOrder()->first()->id : Project::factory(),
            'status' => $this->faker->randomElement(['visited', 'not_visited']),
            'visit_date' => $this->faker->optional()->date(),
            'comment' => $this->faker->text(200),
            'creator_id' => User::exists() ? User::inRandomOrder()->first()->id : User::factory(),
            'updater_id' => User::exists() ? User::inRandomOrder()->first()->id : User::factory(),
        ];
    }
}
