<?php

namespace Database\Factories;

use App\Models\Deposit;
use App\Models\Project;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class DepositFactory extends Factory
{
    protected $model = Deposit::class;

    public function definition()
    {
        return [
            'project_id' => Project::exists() ? Project::inRandomOrder()->first()->id : Project::factory(),
            'amount' => $this->faker->randomFloat(2, 1000, 10000), // Random amount between 1000 and 10000
            // 'payment_date' => $this->faker->date(),
            'creator_id' => User::exists() ? User::inRandomOrder()->first()->id : User::factory(),
            'updater_id' => User::exists() ? User::inRandomOrder()->first()->id : User::factory(),
        ];
    }
}
