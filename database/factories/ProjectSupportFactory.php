<?php

namespace Database\Factories;

use App\Models\ProjectSupport;
use App\Models\Project;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class ProjectSupportFactory extends Factory
{
    protected $model = ProjectSupport::class;

    public function definition()
    {
        return [
            'project_id' => Project::exists() ? Project::inRandomOrder()->first()->id : Project::factory(),
            'status' => $this->faker->optional()->randomElement(['pending', 'completed', 'in_progress']),
            'title' => $this->faker->word(),
            'start_date' => $this->faker->optional()->date,
            'end_date' => $this->faker->optional()->date,
            // 'supporter_end_date' => $this->faker->optional()->date,
            'supporter_id' => User::exists() ? User::role('visitor')->inRandomOrder()->first()->id : User::factory(),
            'supporter_comment' => $this->faker->optional()->paragraph,
            'creator_id' => User::exists() ? User::inRandomOrder()->first()->id : User::factory(),
            'creator_comment' => $this->faker->paragraph,
            'updater_id' => User::exists() ? User::inRandomOrder()->first()->id : User::factory(),
        ];
    }
}
