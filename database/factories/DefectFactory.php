<?php

namespace Database\Factories;

use App\Models\Defect;
use App\Models\Project;
use App\Models\TodolistCategory;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class DefectFactory extends Factory
{
    protected $model = Defect::class;

    public function definition()
    {
        return [
            'project_id' => Project::exists() ? Project::inRandomOrder()->first()->id : Project::factory(),
            'creator_id' => User::exists() ? User::inRandomOrder()->first()->id : User::factory(),
            'updater_id' => User::exists() ? User::inRandomOrder()->first()->id : User::factory(),
            'todolist_category_id' => TodolistCategory::exists() ? TodolistCategory::inRandomOrder()->first()->id : TodolistCategory::factory(),
        ];
    }
}
