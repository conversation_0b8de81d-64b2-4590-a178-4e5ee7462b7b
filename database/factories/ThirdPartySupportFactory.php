<?php

namespace Database\Factories;

use App\Models\ThirdPartySupport;
use App\Models\User;
use App\Models\Project;
use Illuminate\Database\Eloquent\Factories\Factory;

class ThirdPartySupportFactory extends Factory
{
    protected $model = ThirdPartySupport::class;

    public function definition()
    {
        return [
            'project_id' => Project::exists() ? Project::inRandomOrder()->first()->id : Project::factory(),
            'title' => $this->faker->sentence,
            'support_type' => $this->faker->randomElement(['Technical', 'Customer Service', 'Financial']),
            'price' => $this->faker->randomFloat(2, 100, 10000),
            'support_date' => $this->faker->optional()->date(),
            'support_start_date' => $this->faker->optional()->date(),
            'support_end_date' => $this->faker->optional()->date(),
            'support_comments' => $this->faker->text,
            'support_completed' => $this->faker->boolean,
            'support_performer_id' => User::exists() ? User::inRandomOrder()->first()->id : User::factory(),
            'creator_id' => User::exists() ? User::inRandomOrder()->first()->id : User::factory(),
            'updater_id' => User::exists() ? User::inRandomOrder()->first()->id : User::factory(),
        ];
    }
}
