<?php

namespace Database\Factories;

use App\Models\User;
use App\Providers\FakerBankProvider;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\User>
 */
class UserFactory extends Factory
{
    /**
     * The current password being used by the factory.
     */
    protected static ?string $password;
    protected $model = User::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        $this->faker->addProvider(new FakerBankProvider($this->faker));


        return [
            'first_name' => $this->faker->firstName,
            'last_name' => $this->faker->lastName,
            'phone' => $this->faker->unique()->phoneNumber,
            'phone2' => $this->faker->phoneNumber,
            'father_name' => $this->faker->firstNameMale,
            'national_code' => $this->faker->unique()->numerify('##########'),
            'bank' => $this->faker->bank,
            'bank_account_number' => $this->faker->bankAccountNumber,
            'bank_account_iban' => $this->faker->iban,
            'type' => $this->faker->randomElement(['personal', 'legal']),
            'otp_code' => $this->faker->numerify('######'),
            'otp_code_sent' => now(),
            'password' => Hash::make('********'), // Or use a hashed default password
            'creator_id' => User::exists() ? User::inRandomOrder()->first()->id : User::factory(),
            'updater_id' => User::exists() ? User::inRandomOrder()->first()->id : User::factory(),
            'remember_token' => Str::random(10),
            // 'city' => $this->faker->city,
            // 'country' => $this->faker->city,
            // 'province' => $this->faker->city,
            'postal_code' => $this->faker->postcode,
            'address' => $this->faker->address,

            'created_at' => now(),
            'updated_at' => now(),
        ];
    }

    /**
     * Indicate that the model's email address should be unverified.
     */
    public function unverified(): static
    {
        return $this->state(fn(array $attributes) => [
            'email_verified_at' => null,
        ]);
    }

    public function configure()
    {
        return $this->afterCreating(function (User $user) {
            $user->assignRole('customer'); // Assumes you are using Spatie's Laravel Permission package
        });
    }
}
