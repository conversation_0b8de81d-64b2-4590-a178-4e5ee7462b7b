<?php

namespace Database\Factories;

use App\Models\SerialPanel;
use App\Models\User;
use App\Models\Project;
use Illuminate\Database\Eloquent\Factories\Factory;

class SerialPanelFactory extends Factory
{
    protected $model = SerialPanel::class;

    public function definition()
    {
        return [
            'project_id' => Project::exists() ? Project::inRandomOrder()->first()->id : Project::factory(),
            'serial' => $this->faker->uuid,
            'creator_id' => User::exists() ? User::inRandomOrder()->first()->id : User::factory(),
            'updater_id' => User::exists() ? User::inRandomOrder()->first()->id : User::factory(),
        ];
    }
}
