<?php

namespace Database\Factories;

use App\Models\TodolistCategory;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class TodolistCategoryFactory extends Factory
{
    protected $model = TodolistCategory::class;

    public function definition()
    {
        return [
            'parent_id' => TodolistCategory::exists() ? TodolistCategory::inRandomOrder()->first()->id : null,
            'title' => $this->faker->word,
            'type' => $this->faker->randomElement(['checking', 'defect']),
            'status' => $this->faker->randomElement(['pending','published']),
            'creator_id' => User::exists() ? User::inRandomOrder()->first()->id : User::factory(),
            'updater_id' => User::exists() ? User::inRandomOrder()->first()->id : User::factory(),
        ];
    }
}
