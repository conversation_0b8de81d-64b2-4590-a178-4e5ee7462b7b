<?php

namespace Database\Factories;

use App\Models\Project;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class ProjectFactory extends Factory
{
    protected $model = Project::class;

    public function definition()
    {
        return [
            'title' => $this->faker->title,
//            'project_code' => $this->faker->unique()->bothify('PRJ-####'),
            'user_id' => User::whereHas('roles', function($query) {
                $query->where('name', 'customer');
                })->exists() ? User::whereHas('roles', function($query) {
                    $query->where('name', 'customer');
                })->inRandomOrder()->first()->id : User::factory(),
            'designer_id' => User::exists() ? User::role('design')->inRandomOrder()->first()->id : User::factory(),
            'installer_id' => User::exists() ? User::role('execution')->inRandomOrder()->first()->id : User::factory(),

            // 'electricity_country' => $this->faker->city,
            'electricity_affairs_code' => $this->faker->optional()->numerify('####'),
            'electricity_bill_id' => $this->faker->optional()->numerify('############'),
            'mehrsan_file_number' => $this->faker->optional()->bothify('MF-####'),
            'postal_code' => $this->faker->optional()->postcode,
            'power_plant_capacity' => $this->faker->optional()->numerify('### kW'),
            'province_id' => 17,
            'county_id' => 373,
            'city_id' => 1268,
            'status' => $this->faker->randomElement(['active', 'inactive', 'pending', 'completed', 'under-review', 'other']),
            'type' => $this->faker->optional()->randomElement(['support', 'full']),
            'support_organization' => $this->faker->randomElement(['01', '02', '03', '04', '05']),

            'payment_bank' => $this->faker->optional()->bank,
            'address' => $this->faker->optional()->address,
            'payment_status' => $this->faker->optional()->randomElement(['deposited-but-has-balance', 'not-deposited', 'deposited']),
            'remaining_deposit' => $this->faker->optional()->randomFloat(2, 1000, 10000),
            'paid_amount' => $this->faker->optional()->randomFloat(2, 1000, 10000),
            // 'payment_date' => $this->faker->optional()->date,
            'balance' => $this->faker->optional()->randomFloat(2, 0, 10000),
            // 'payment_until_today' => $this->faker->optional()->date,
            'inspection_status' => $this->faker->optional()->randomElement(['visited', 'not-visited']),
            // 'inspection_date' => $this->faker->optional()->date,
            'inspector_id' => User::exists() ? User::role('visitor')->inRandomOrder()->first()->id : User::factory(),
            'inspector_comment' => $this->faker->optional()->paragraph,

            'design_file_result' => $this->faker
                ->randomElement(['one-floor', 'two-floors', 'Shading', 'two-floors-land', 'one-floor-land']),

            // 'panel_installation_date' => $this->faker->optional()->date,
            // 'meter_installation_date' => $this->faker->optional()->date,
            'payment_to_execution_duration' => $this->faker->optional()->numberBetween(1, 365),
            'payment_to_network_connection_duration' => $this->faker->optional()->numberBetween(1, 365),
            'panel_to_meter_duration' => $this->faker->optional()->numberBetween(1, 365),
            'structure_type_image' => $this->faker
                ->randomElement(['one-floor', 'two-floors', 'Shading', 'two-floors-land', 'one-floor-land']),

            'current_phase' => $this->faker->optional()
                ->randomElement(['not-visited', 'visited-ready-to-design', 'designed-ready-to-send-commodity',
                    'commodity-sent-ready-to-implementation', 'run-ready-to-connect-to-the-network', 'connected-to-the-network']),

            'send_list_comments' => $this->faker->optional()->paragraph,
            // 'materials' => $this->faker->optional()->paragraph,
            'additional_costs_and_dorm_rent' => $this->faker->optional()->randomFloat(2, 100, 1000),
            'inverter_model' => $this->faker
                ->randomElement(['Arka5kw', 'Growat5kw', 'Arka10kw', 'Arka20kw']),

            'inverter_serial_number_image' => $this->faker->optional()->imageUrl,
            'meter_serial_number_image' => $this->faker->optional()->imageUrl,
            'sim_card_number' => $this->faker->optional()->phoneNumber,
            'utm_y' => $this->faker->optional()->latitude,
            'utm_x' => $this->faker->optional()->longitude,
            'longitude' => $this->faker->optional()->longitude,
            'latitude' => $this->faker->optional()->latitude,
            'google_maps_link' => $this->faker->optional()->url,
            'gis_code' => $this->faker->optional()->numerify('GIS-####'),
            // 'monitoring' => $this->faker->optional()->word,
            'mehrsan_status' => $this->faker->randomElement(['End', 'Visit by electrical inspector',
            'Connecting the power plant to the power company by the supervisor', 'Upload documents', 'Upload documents']),
            'insurance' => $this->faker->randomElement(['have', 'not-have']),
            'support_needed' => $this->faker->randomElement(['have', 'not-have']),
//            'support_need_declaration_date' => $this->faker->optional()->date,
//            'support_comments' => $this->faker->optional()->paragraph,
//            'support_completion_date' => $this->faker->optional()->date,
//            'support_performer' => $this->faker->optional()->name,
//            'support_performer_image' => $this->faker->optional()->imageUrl,
//            'support_maker_image' => $this->faker->optional()->imageUrl,
//            'support_declaration_to_completion_date' => $this->faker->optional()->numberBetween(1, 365) ,
            'creator_id' => User::exists() ? User::inRandomOrder()->first()->id : User::factory(),
            'updater_id' => User::exists() ? User::inRandomOrder()->first()->id : User::factory(),
        ];
    }
}
