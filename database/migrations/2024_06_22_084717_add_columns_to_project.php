<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('projects', function (Blueprint $table) 
        {
            
            
            $table->date('material_cost_payment_date')->nullable()
                ->comment(' تاریخ واریز هزینه ی مصالح  ')
                ->after('cost_of_materials');

            $table->date('additional_costs_and_dorm_rent_payment_date')->nullable()
                ->comment(' تاریخ واریز هزینه ی خوابگاه  ')
                ->after('additional_costs_and_dorm_rent');
        });
    }

    public function down(): void
    {
        Schema::table('projects', function (Blueprint $table) 
        {

            $table->dropColumn([
                'material_cost_payment_date',
                'additional_costs_and_dorm_rent_payment_date',
              
            ]);

            // $table->unsignedInteger('materials')->nullable();
        });
    }
    
};
