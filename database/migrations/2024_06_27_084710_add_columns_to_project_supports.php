<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {

        Schema::table('project_supports', function (Blueprint $table) 
        {
            $table->integer('support_implementation_duration')->nullable()->after('end_date')->comment('مدت زمان اعلام پشتیبانی تا انجام پشتیبانی');
        });
    }

    public function down()
    {
        Schema::table('project_supports', function (Blueprint $table) {
            $table->dropColumn('support_implementation_duration');
        });
    }
};
