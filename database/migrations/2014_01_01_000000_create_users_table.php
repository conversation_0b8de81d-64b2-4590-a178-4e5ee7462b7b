<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('users', function (Blueprint $table) {
            $table->id();
            $table->string('first_name')->nullable();
            $table->string('last_name')->nullable();
            $table->string('phone')->unique();
            $table->string('phone2')->nullable();
            $table->string('father_name')->nullable();
            $table->string('national_code')->nullable()->unique();

            $table->unsignedInteger('province_id')->nullable()->comment('استان');
            $table->unsignedInteger('county_id')->nullable()->comment('شهرستان');
            $table->unsignedInteger('city_id')->nullable()->comment('شهر');
            $table->string('postal_code')->comment('کد پستی')->nullable();
            $table->text('address')->comment('آدرس')->nullable();

            $table->string('bank')->nullable()->comment('بانک');
            $table->string('bank_account_number')->nullable()->comment('شماره حساب بانک');
            $table->string('bank_account_iban')->nullable()->comment('شماره شبا بانک مشترک');

            $table->string('type')->nullable()->default('personal');

            $table->string('otp_code')->nullable();
            $table->datetime('otp_code_sent')->nullable();

            $table->string('password')->nullable();

            $table->unsignedBigInteger('creator_id')->nullable();
            $table->foreign('creator_id')->references('id')->on('users')->onDelete('cascade');

            $table->unsignedBigInteger('updater_id')->nullable();
            $table->foreign('updater_id')->references('id')->on('users')->onDelete('cascade');

            $table->rememberToken();
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('password_reset_tokens', function (Blueprint $table) {
            $table->string('email')->primary();
            $table->string('token');
            $table->timestamp('created_at')->nullable();
        });

        Schema::create('sessions', function (Blueprint $table) {
            $table->string('id')->primary();
            $table->foreignId('user_id')->nullable()->index();
            $table->string('ip_address', 45)->nullable();
            $table->text('user_agent')->nullable();
            $table->longText('payload');
            $table->integer('last_activity')->index();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('users');
        Schema::dropIfExists('password_reset_tokens');
        Schema::dropIfExists('sessions');
    }
};
