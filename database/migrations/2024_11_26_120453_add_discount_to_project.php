<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('projects', function (Blueprint $table) {
            $table->string('discount')->nullable()->comment('تخفیف')->after('paid_amount');
            $table->string('accounting_status')->nullable()->comment('وضعیت حسابداری')->after('title');

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('projects', function (Blueprint $table) {
            $table->dropColumn('discount');
            $table->dropColumn('accounting_status');
        });
    }
};
