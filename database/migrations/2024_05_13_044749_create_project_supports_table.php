<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('project_supports', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('project_id')->comment('پروژه مرتبط');
            $table->foreign('project_id')->references('id')->on('projects')->onDelete('cascade');
            $table->string('status')->nullable()->comment('نیاز به پشتیبانی');
            $table->string('title')->nullable()->comment('عنوان');
            $table->date('start_date')->nullable()->comment('تاریخ اعلام پشتیبانی');
            $table->date('end_date')->nullable()->comment('تاریخ انجام پشتیبانی');
            $table->unsignedBigInteger('supporter_id')->nullable()->comment('پشتیبان');
            $table->foreign('supporter_id')->references('id')->on('users')->onDelete('cascade')->comment('پشتیبان');
            $table->text('supporter_comment')->nullable()->comment('توضیحات انجام دهنده پشتیبانی ');

            $table->unsignedBigInteger('creator_id')->nullable()->comment('سازنده پشتیبانی');
            $table->foreign('creator_id')->references('id')->on('users')->onDelete('cascade')->comment('سازنده پشتیبانی');
            $table->text('creator_comment')->comment('کامنت سازنده پشتیبانی')->nullable();

            $table->unsignedBigInteger('updater_id')->nullable()->comment('ویرایش کننده پشتیبانی');
            $table->foreign('updater_id')->references('id')->on('users')->onDelete('cascade')->comment('ویرایش کننده پشتیبانی');

            $table->timestamps();
            $table->softDeletes();

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('project_support');
    }
};
