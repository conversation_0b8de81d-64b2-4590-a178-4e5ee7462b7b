<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

class ModifyNationalCodeColumnInUsersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('users', function (Blueprint $table) {
            // Drop the unique index on the national_code column
            $table->dropUnique('users_national_code_unique');

            // Make the national_code column nullable
            $table->string('national_code')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('users', function (Blueprint $table) {
            // Revert the national_code column to not nullable and add the unique index back
            $table->string('national_code')->nullable(false)->change();
            $table->unique('national_code');
        });
    }
}
