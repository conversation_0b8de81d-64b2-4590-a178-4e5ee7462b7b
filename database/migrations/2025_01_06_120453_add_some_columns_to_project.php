<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('projects', function (Blueprint $table) {
            $table->string('meter_assignment')->nullable()->default('not-have')->comment('اختصاص کنتور')->after('execution_type');
            $table->string('announcement_sanam')->nullable()->default('not-have')->comment('ابلاغ در سنم')->after('meter_assignment');


        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('projects', function (Blueprint $table) {

        });
    }
};
