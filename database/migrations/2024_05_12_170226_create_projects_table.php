<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('projects', function (Blueprint $table) {
            $table->id();
            $table->string('project_code')->comment('کد پروژه')->nullable()->unique();
            // $table->unsignedInteger('user_id')->nullable();
            // $table->foreignId('user_id')->references('id')->on('users');
            $table->unsignedBigInteger('user_id')->nullable();
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');

            $table->string('title')->comment('عنوان')->nullable();
            $table->unsignedInteger('electricity_county_id')->comment('شهرستان برق')->nullable();
            $table->string('electricity_affairs_code')->nullable()->comment('کد امور برق');
            $table->string('electricity_bill_id')->nullable()->comment('شناسه قبض برق');
            $table->string('mehrsan_file_number')->nullable()->comment('شماره پرونده مهرسان');
            $table->string('postal_code')->nullable()->comment('کد پستی');
            $table->string('power_plant_capacity')->nullable()->comment('قدرت نیروگاه کیلو وات');
            $table->string('support_organization')->nullable()->comment('سازمان حمایتی');


            $table->unsignedInteger('province_id')->nullable()->comment('استان');
            $table->unsignedInteger('county_id')->nullable()->comment('شهرستان');
            $table->unsignedInteger('city_id')->nullable()->comment('شهر');

            //TODO : why two bank ?
            $table->string('payment_bank')->nullable()->comment('بانک');  // unused

            $table->string('address')->nullable()->comment('آدرس');
            $table->string('payment_status')->nullable()->comment('وضعیت واریزی');
            $table->string('remaining_deposit')->nullable()->comment('مانده'); //TODO : difference ?
            $table->string('paid_amount')->nullable()->comment('مبلغ واریزی');//TODO : difference ?
            $table->date('payment_date')->nullable()->comment('تاریخ واریز');
            $table->string('balance')->nullable()->comment('مبلغ کلی که باید واریز شود');//TODO : difference ?
            // $table->string('payment_until_today')->nullable()->comment('تاریخ واریز تا امروز'); // is it nesessa
            $table->date('execution_date')->nullable()->comment('تاریخ اجرا');

            $table->string('inspection_status')->nullable()->comment('وضعیت بازدید');
            $table->date('inspection_date')->nullable()->comment('تاریخ بازدید');
            $table->unsignedBigInteger('inspector_id')->nullable()->comment('بازدید کننده');
            $table->foreign('inspector_id')->references('id')->on('users');

            $table->text('inspector_comment')->nullable()->comment('کامنت بازدیدکننده');

            $table->string('design_file_result')->nullable()->comment('نتیجه طراحی فایل');
            $table->date('panel_installation_date')->nullable()->comment('تاریخ نصب پنل');
            $table->date('meter_installation_date')->nullable()->comment('تاریخ نصب کنتور و اتصال به شبکه');
            $table->string('panel_to_meter_duration')->nullable()->comment('زمان انتظار تا اتصال به شبکه');

            $table->string('payment_to_execution_duration')->nullable()->comment('مدت زمان واریز تا اجرا');
            $table->string('payment_to_network_connection_duration')->nullable()->comment('مدت زمان واریز تا اتصال به شبکه');
            // $table->string('installation_to_today_duration')->nullable()->comment('مدت زمان نصب تا امروز');
            $table->unsignedBigInteger('installer_id')->nullable()->comment('نصاب');
            $table->foreign('installer_id')->references('id')->on('users');

            $table->unsignedBigInteger('designer_id')->nullable()->comment('طراح');
            $table->foreign('designer_id')->references('id')->on('users');

            $table->string('structure_type_image')->nullable()->comment('نوع سازه عکس');
            $table->string('current_phase')->nullable()->comment('مرحله فعلی');
            $table->text('send_list_comments')->nullable()->comment('لیست ارسال ها + توضیحات');
            $table->text('materials')->nullable()->comment('مصالح');
            $table->string('additional_costs_and_dorm_rent')->nullable()->comment('هزینه جانبی و کرایه خوابگاه');
            $table->string('inverter_model')->nullable()->comment('مدل اینورتر');
            $table->string('inverter_serial_number_image')->nullable()->comment('سریال اینورتر عکس');
            $table->string('meter_serial_number_image')->nullable()->comment('سریال کنتور عکس');
            $table->string('sim_card_number')->nullable()->comment('شماره سیم کارت');
            $table->string('utm_y')->nullable()->comment('UTM Y');
            $table->string('utm_x')->nullable()->comment('UTM x');
            $table->string('longitude')->nullable()->comment('long');
            $table->string('latitude')->nullable()->comment('latitude');
            $table->string('google_maps_link')->nullable()->comment('لینک کامل موقعیت گوگل');
            $table->string('gis_code')->nullable()->comment('کد GIS');
            $table->string('has_monitoring')->nullable()->comment('مانیتورینگ');
            $table->string('monitoring_code')->nullable()->comment('بارکد مانیتورینگ');
            $table->string('mehrsan_status')->nullable()->comment('وضعیت مهرسان');
            $table->text('support_image_and_comments')->nullable()->comment('+عکس+توضیحات');
            $table->string('insurance')->nullable()->comment('بیمه');
            $table->string('support_needed')->nullable()->comment('نیاز به پشتیبانی');

            $table->text('defect_comment')->nullable();
            $table->text('checking_comment')->nullable();


            $table->string('type')->nullable();
            $table->string('status')->nullable();

            $table->unsignedBigInteger('creator_id')->nullable();
            $table->foreign('creator_id')->references('id')->on('users')->onDelete('cascade');

            $table->unsignedBigInteger('updater_id')->nullable();
            $table->foreign('updater_id')->references('id')->on('users')->onDelete('cascade');
            $table->timestamps();
            $table->softDeletes();

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('projects');
    }
};
