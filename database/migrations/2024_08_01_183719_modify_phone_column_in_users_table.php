<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

class ModifyPhoneColumnInUsersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('users', function (Blueprint $table) {
            // Drop the unique index on the phone column
            $table->dropUnique('users_phone_unique');

            // Optionally, if you need to change the column type or other attributes, you can do it here.
            // $table->string('phone')->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('users', function (Blueprint $table) {
            // Re-add the unique index on the phone column
            $table->unique('phone');

            // Optionally, revert any other column changes
            // $table->string('phone')->change();
        });
    }
}
