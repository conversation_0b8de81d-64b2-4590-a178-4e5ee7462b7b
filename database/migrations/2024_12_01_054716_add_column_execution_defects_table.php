<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {

            Schema::table('execution_defects', function (Blueprint $table) {
        
            $table->date('execution_defect_date')->nullable()
            ->comment('تاریخ ایجاد')->after('todolist_category_id');

            $table->unsignedInteger('execution_defect_user_id')->nullable()
                ->comment('مسئول ایجاد کننده')->after('execution_defect_date');
           
            $table->date('fix_execution_defect_date')->nullable()
            ->comment('تاریخ برطرف شده')->after('execution_defect_user_id');

            $table->unsignedInteger('fix_execution_defect_user_id')->nullable()
                ->comment('مسئول برطرف کننده')->after('fix_execution_defect_date');

            

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('execution_defects', function (Blueprint $table) {

        });
    }
};
