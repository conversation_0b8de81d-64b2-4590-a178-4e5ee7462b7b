<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {

        Schema::table('projects', function (Blueprint $table) 
        {
            $table->unsignedInteger('inverter_to_electrical_base_distance')->nullable()
            ->comment('فاصله ی اینورتر تا پایه ی برق')
            ->after('inverter_serial_number_image');
            ; 

            if (Schema::hasColumn('projects', 'panel_type_power_and_quantity')) {
                Schema::table('projects', function (Blueprint $table) {
                    $table->dropColumn('panel_type_power_and_quantity');
                });
            }

            $table->string('panel_type')->nullable()
            ->comment('نوع پنل')
            ->after('additional_costs_and_dorm_rent');
            ;  
            $table->unsignedInteger('panel_quantity')->nullable()
            ->comment('تعداد پنل')
            ->after('panel_type');
            ; 
            $table->string('panel_power')->nullable()
            ->comment('توان پنل')
            ->after('panel_quantity');
            ;   
        });
    }

    public function down()
    {
        Schema::table('projects', function (Blueprint $table) {
            $table->dropColumn('inverter_to_electrical_base_distance');
        });
    }
};
