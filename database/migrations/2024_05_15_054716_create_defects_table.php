<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('defects', function (Blueprint $table) {
            $table->id();

            $table->unsignedBigInteger('project_id');
            $table->foreign('project_id')->references('id')->on('projects');


            $table->unsignedBigInteger('todolist_category_id')->comment('کتگوری لیست انجام کار');
            $table->foreign('todolist_category_id')->references('id')->on('todolist_categories')->onDelete('cascade')->comment('کتگوری لیست انجام کار');

            $table->unsignedBigInteger('creator_id')->nullable()->comment('سازنده ایراد');
            $table->foreign('creator_id')->references('id')->on('users')->onDelete('cascade')->comment('سازنده ایراد');

            $table->unsignedBigInteger('updater_id')->nullable()->comment('ویرایش کننده ایراد');
            $table->foreign('updater_id')->references('id')->on('users')->onDelete('cascade')->comment('ویرایش کننده ایراد');


            $table->timestamps();
            $table->softDeletes();

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('defects');
    }
};
