<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dateTime('last_project_update')->nullable();
            $table->dateTime('last_user_update')->nullable();
            $table->timestamp('last_todo_update')->nullable();
            $table->dateTime('last_media_update')->nullable();

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'last_project_update',
                'last_user_update',
                'last_todo_update',
                'last_media_update'
            ]);
        });
    }
};
