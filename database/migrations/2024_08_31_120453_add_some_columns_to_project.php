<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('projects', function (Blueprint $table) {
            $table->date('loan_received_date')->nullable()->after('mehrsan_file_number');
            $table->text('network_connection_comment')->nullable()->comment('توضیحات اتصال به شبکه')->after('gis_code');


        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('projects', function (Blueprint $table) {
            $table->dropColumn('loan_received_date');

        });
    }
};
