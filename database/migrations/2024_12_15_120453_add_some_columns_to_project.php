<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('projects', function (Blueprint $table) {
            $table->string('earth_resistance')->nullable()->comment('مقدار مقاومت ارت')->after('execution_defect_comment');
            $table->date('measurement_date')->nullable()->comment('تاریخ اندازه گیری ارت')->after('execution_defect_comment');
            $table->string('execution_type')->nullable()->comment('نوع اجرا ارت')->after('execution_defect_comment');


        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('projects', function (Blueprint $table) {

        });
    }
};
