<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {

        Schema::table('deposits', function (Blueprint $table) 
        {
            $table->string('type')->nullable()->comment('نوع انتقال')->after('project_id');
            $table->text('info_deposit')->nullable()->comment('توضیحات واریزی')->after('payment_date');
        });
    }

    public function down()
    {
        Schema::table('deposits', function (Blueprint $table) {
            $table->dropColumn('type');
            $table->dropColumn('info_deposit');
        });
    }
};
