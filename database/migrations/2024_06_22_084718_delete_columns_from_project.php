<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {

        Schema::table('projects', function (Blueprint $table) 
        {
            if (Schema::hasColumn('projects', 'execution_date')) {
                $table->dropColumn('execution_date');
            }

            if (Schema::hasColumn('projects', 'support_image_and_comments')) {
                $table->dropColumn('support_image_and_comments');
            }
        });
    }

    public function down()
    {
        Schema::table('projects', function (Blueprint $table) {
            $table->date('execution_date')->nullable(); 
            $table->text('support_image_and_comments')->nullable();
        });
    }
};
