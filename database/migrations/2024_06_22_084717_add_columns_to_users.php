<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {

        Schema::table('users', function (Blueprint $table) 
        {
            $table->string('birth_date')->nullable()
            ->comment('تاریخ تولد')
            ->after('national_code');
            ;  
        });

        Schema::table('users', function (Blueprint $table) 
        {
            $table->string('bank_branch')->nullable()
            ->comment('شعبه بانک')
            ->after('bank');
            ;  
        });

    }

    public function down()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('birth_date');
        });
    }
};
