<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {

        Schema::table('mediables', function (Blueprint $table) {
            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down()
    {
        Schema::table('mediables', function (Blueprint $table) {
            $table->dropTimestamps();
            $table->dropSoftDeletes();
        });
    }
};
