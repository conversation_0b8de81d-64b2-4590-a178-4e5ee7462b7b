<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('projects', function (Blueprint $table) 
        {
            $table->unsignedInteger('inverter_to_the_power_base_cable_used')->nullable()
                ->comment('مقدار کابل مصرفی اینورتر تا پایه برق')
                ->after('inverter_to_electrical_base_distance');

            $table->unsignedInteger('monitoring_installer_id')->nullable()
                ->comment('نصاب مانیتورینگ')
                ->after('monitoring_code');
            
            $table->date('monitoring_installation_date')->nullable()
                ->comment('تاریخ نصب مانیتورینگ')
                ->after('monitoring_installer_id');

            $table->unsignedInteger('simban_id')->nullable()
                ->comment('مسئول اتصال به شبکه')
                ->after('panel_to_meter_duration');

            $table->text('execution_defect_comment')->nullable()
                ->comment('توضیحات نواقص اجرایی')
                ->after('defect_comment');

            $table->date('ware_postage_date')->nullable()
                ->comment('تاریخ ارسال جنس')
                ->after('checking_comment');

            $table->unsignedInteger('ware_sender_id')->nullable()
                ->comment('شخص ارسال کننده جنس')
                ->after('ware_postage_date');

            $table->integer('cost_of_materials')->nullable()
            ->comment('هزینه مصالح')
            ->after('ware_sender_id');;

            if (Schema::hasColumn('projects', 'materials')) {
                $table->dropColumn('materials');
            }

            // Adding foreign key constraints after the columns are defined
            // $table->foreign('monitoring_installer_id')->references('id')->on('users')->onDelete('set null');
            // $table->foreign('simban_id')->references('id')->on('users')->onDelete('set null');
        });
    }

    public function down(): void
    {
        Schema::table('projects', function (Blueprint $table) 
        {
            $table->dropForeign(['monitoring_installer_id']);
            $table->dropForeign(['simban_id']);

            $table->dropColumn([
                'inverter_to_the_power_base_cable_used',
                'monitoring_installer_id',
                'monitoring_installation_date',
                'simban_id',
                'postage_date',
                'ware_postage_date',
                'cost_of_materials'
            ]);

            // $table->unsignedInteger('materials')->nullable();
        });
    }
    
};
