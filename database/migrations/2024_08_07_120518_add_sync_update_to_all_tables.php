

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    protected $connection = 'mysql';

    public function up(): void
    {
        $tables = DB::select('SHOW TABLES');

        // Get the key name dynamically (the table names are under a dynamic key in MySQL)
        $key = 'Tables_in_' . \Illuminate\Support\Facades\DB::getDatabaseName();

        // Exclude specific tables that shouldn't have these columns
        $excludedTables = ['migrations']; // Add other tables to exclude as needed

        foreach ($tables as $table) {
            // Get the table name from the fetched result
            $tableName = $table->$key;

            if (in_array($tableName, $excludedTables)) {
                continue;
            }

            // Add columns to each table
            Schema::table($tableName, function (Blueprint $table) use ($tableName) {
                if (!Schema::hasColumn($tableName, 'sync_update')) {
                    $table->string('sync_update')->nullable()->unique();
                }
              
            });
        }
    }

    public function down(): void
    {
        $tables = \Illuminate\Support\Facades\DB::select('SHOW TABLES');

        // Get the key name dynamically (the table names are under a dynamic key in MySQL)
        $key = 'Tables_in_' . \Illuminate\Support\Facades\DB::getDatabaseName();

        // Exclude specific tables that shouldn't have these columns removed
        $excludedTables = ['migrations']; // Add other tables to exclude as needed

        foreach ($tables as $table) {
            // Get the table name from the fetched result
            $tableName = $table->$key;

            if (in_array($tableName, $excludedTables)) {
                continue;
            }

            // Remove columns from each table
            Schema::table($tableName, function (Blueprint $table) {
                $table->dropColumn('sync_update');
            });
        }
    }
};

