<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('third_party_supports', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('project_id')->comment('پروژه مرتبط');
            $table->foreign('project_id')->references('id')->on('projects')->onDelete('cascade');
            $table->string('title')->nullable()->comment('عنوان');
            $table->string('support_type')->nullable()->comment('نوع پشتیبانی');
            $table->string('price')->nullable()->comment('مبلغ');
            $table->date('support_date')->nullable()->comment('تاریخ درخواست پشتیبانی');
            $table->date('support_start_date')->nullable()->comment('تاریخ شروع پشتیبانی');
            $table->date('support_end_date')->nullable()->comment('تاریخ پایان پشتیبانی');

            $table->text('support_comments')->nullable()->comment('توضیحات پشتیبانی');
            $table->boolean('support_completed')->default(false)->comment('پشتیبانی انجام شده یا نشده');
            $table->unsignedBigInteger('support_performer_id')->nullable()->comment('شخص انجام دهنده پشتیبانی');
            $table->foreign('support_performer_id')->references('id')->on('users')->onDelete('cascade');

            $table->unsignedBigInteger('creator_id')->nullable();
            $table->foreign('creator_id')->references('id')->on('users')->onDelete('cascade');

            $table->unsignedBigInteger('updater_id')->nullable();
            $table->foreign('updater_id')->references('id')->on('users')->onDelete('cascade');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('admin_menus');
    }
};
