<?php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user_updates', function (Blueprint $table) {
            $table->string('last_project_update')->nullable()->change();
            $table->string('last_user_update')->nullable()->change();
            $table->string('last_todo_update')->nullable()->change();
            $table->string('last_media_update')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('user_updates', function (Blueprint $table) {
            $table->dateTime('last_project_update')->nullable()->change();
            $table->dateTime('last_user_update')->nullable()->change();
            $table->dateTime('last_todo_update')->nullable()->change();
            $table->dateTime('last_media_update')->nullable()->change();
        });
    }
};
