<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use <PERSON>tie\Permission\Models\Permission;
use Illuminate\Support\Facades\DB;

class PermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::statement('SET FOREIGN_KEY_CHECKS=0');
        DB::table('permissions')->truncate();

        DB::statement('SET FOREIGN_KEY_CHECKS=1');

        $permissions = [
            'view-logs',

            'view-dashboard',
            'view-reports',
            'view-users',
            'create-users',
            'update-users',
            'delete-users',

            'view-projects',
            'create-projects',
            'update-projects',
            'delete-projects',

            'view-project_supports',
            'create-project_supports',
            'update-project_supports',
            'delete-project_supports',

            'view-database',
            'create-database',
            'update-database',
            'delete-database',

            'view-todolist_categories',
            'create-todolist_categories',
            'update-todolist_categories',
            'delete-todolist_categories',

            // -----------------------
            // project permissions ---
            'update-projects-project_code',
            'update-projects-user_id',
            'update-projects-title',
            'update-projects-electricity_county_id',
            'update-projects-electricity_affairs_code',
            'update-projects-electricity_bill_id',
            'update-projects-mehrsan_file_number',
            'update-projects-support_organization',
            'update-projects-province_id',
            'update-projects-county_id',
            'update-projects-city_id',
            'update-projects-payment_bank',
            'update-projects-address',
            'update-projects-postal_code',
            'update-projects-defect_comment',
            'update-projects-checking_comment',


            'update-projects-payment_status',
            'update-projects-remaining_deposit',
            'update-projects-paid_amount',
            'update-projects-payment_date',
            'update-projects-balance',
            'update-projects-payment_until_today',
            'update-projects-inspection_status',
            'update-projects-inspection_date',
            'update-projects-inspector',
            'update-projects-inspector_comment',
            'update-projects-design_file_result',
            'update-projects-panel_installation_date',
            'update-projects-meter_installation_date',
            'update-projects-payment_to_execution_duration',
            'update-projects-payment_to_network_connection_duration',
            'update-projects-installation_to_today_duration',
            'update-projects-inverter_model',
            'update-projects-inverter_to_electrical_base_distance',
            'update-projects-inspector_id',
            'update-projects-send_execution_defect',
            'update-projects-simban_id',



            'update-projects-structure_type_image',
            'update-projects-current_phase',
            'update-projects-send_list_comments',
            'update-projects-additional_costs_and_dorm_rent',
            'update-projects-panel_type',
            'update-projects-panel_power',
            'update-projects-panel_quantity',
            'update-projects-panel_serial_numbers_image',
            'update-projects-inverter_serial_number_image',
            'update-projects-meter_serial_number_image',
            'update-projects-sim_card_number',
            'update-projects-utm_y',
            'update-projects-utm_x',
            'update-projects-longitude',
            'update-projects-latitude',
            'update-projects-google_maps_link',
            'update-projects-gis_code',
            'update-projects-has_monitoring',
            'update-projects-mehrsan_status',
            'update-projects-mehrsan_date',
            'update-projects-insurance',
            'update-projects-support_needed',
            'update-projects-type',
            'update-projects-status',
            'update-projects-creator_id',
            'update-projects-updater_id',
            'update-projects-power_plant_capacity',
            'update-projects-monitoring_code',
            'update-projects-inverter_to_the_power_base_cable_used',
            'update-projects-monitoring_installation_date',
            'update-projects-monitoring_installer_id',
            'update-projects-ware_postage_date',
            'update-projects-ware_sender_id',
            'update-projects-cost_of_materials',
            'update-projects-material_cost_payment_date',
            'update-projects-execution_defect_comment',
            'update-projects-additional_costs_and_dorm_rent_payment_date',



            'update-projects-installer_id',
            'update-projects-designer_id',
            // -----------------------


            // -----------------------
            // user permissions ---
            'update-users-first_name',
            'update-users-birth_date',
            'update-users-last_name',
            'update-users-phone',
            'update-users-phone2',
            'update-users-father_name',
            'update-users-national_code',
            'update-users-city_id',
            'update-users-county_id',
            'update-users-province_id',
            'update-users-postal_code',
            'update-users-address',
            'update-users-bank',
            'update-users-bank_account_number',
            'update-users-bank_account_iban',
            'update-users-type',
            'update-users-otp_code',
            'update-users-otp_code_sent',
            'update-users-password',
            'update-users-creator_id',
            'update-users-updater_id',
            'update-users-remember_token',


            // -----------------------
            // deposit permissions ---
            'update-deposits-project_id',
            'update-deposits-amount',
            'update-deposits-payment_date',
            'update-deposits-user_id',
            'update-deposits-creator_id',
            'update-deposits-updater_id',
            'update-deposits-created_at',
            'update-deposits-bank',

            // -----------------------
            // checking permissions ---
            'update-checkings-project_id',
            'update-checkings-user_id',
            'update-checkings-creator_id',
            'update-checkings-updater_id',
            'update-checkings-created_at',
            'update-checkings-todolist_category_id',


            // -----------------------
            // visitor permissions ---
            'update-visitors-user_id',
            'update-visitors-project_id',
            'update-visitors-status',
            'update-visitors-visit_date',
            'update-visitors-comment',
            'update-visitors-creator_id',
            'update-visitors-updater_id',
            'update-visitors-created_at',
            'update-visitors-updated_at',


            // -----------------------
            // serial_panel permissions ---
            'update-serial_panels-project_id',
            'update-serial_panels-serial',
            'update-serial_panels-creator_id',
            'update-serial_panels-updater_id',
            'update-serial_panels-created_at',
            'update-serial_panels-updated_at',
            'update-serial_panels-description',


            // -----------------------
            // third_party_support permissions ---
            'update-third_party_supports-project_id',
            'update-third_party_supports-title',
            'update-third_party_supports-support_type',
            'update-third_party_supports-price',
            'update-third_party_supports-support_date',
            'update-third_party_supports-support_start_date',
            'update-third_party_supports-support_end_date',
            'update-third_party_supports-support_comments',
            'update-third_party_supports-support_completed',
            'update-third_party_supports-support_performer',
            'update-third_party_supports-creator_id',
            'update-third_party_supports-updater_id',
            'update-third_party_supports-created_at',
            'update-third_party_supports-updated_at',
            // project_support permissions ---
            'update-project_supports-project_id',
            'update-project_supports-status',
            'update-project_supports-title',
            'update-project_supports-start_date',
            'update-project_supports-end_date',
            'update-project_supports-supporter_comment',
            'update-project_supports-creator_id',
            'update-project_supports-creator_comment',
            'update-project_supports-updater_id',
            'update-project_supports-supporter_id',
            'update-project_supports-support_implementation_duration',




            //add new
            'update-projects-network_connection_comment',
            'update-projects-loan_received_date',

            'update-projects-project_support_comment',




        ];

        foreach ($permissions as $permission) {
            Permission::create(['name' => $permission, 'guard_name' => 'web']);
            Permission::create(['name' => $permission, 'guard_name' => 'api']);
        }


        $permissions = [

            // project permissions ---
            'view-projects-project_code',
            'view-projects-user_id',
            'view-projects-title',
            'view-projects-electricity_county_id',
            'view-projects-electricity_affairs_code',
            'view-projects-electricity_bill_id',
            'view-projects-mehrsan_file_number',
            'view-projects-support_organization',
            'view-projects-province_id',
            'view-projects-county_id',
            'view-projects-city_id',
            'view-projects-payment_bank',
            'view-projects-address',
            'view-projects-postal_code',
            'view-projects-defect_comment',
            'view-projects-checking_comment',


            'view-projects-payment_status',
            'view-projects-remaining_deposit',
            'view-projects-paid_amount',
            'view-projects-payment_date',
            'view-projects-balance',
            'view-projects-payment_until_today',
            'view-projects-inspection_status',
            'view-projects-inspection_date',
            'view-projects-inspector',
            'view-projects-inspector_comment',
            'view-projects-design_file_result',
            'view-projects-panel_installation_date',
            'view-projects-meter_installation_date',
            'view-projects-payment_to_execution_duration',
            'view-projects-payment_to_network_connection_duration',
            'view-projects-installation_to_today_duration',
            'view-projects-inverter_model',
            'view-projects-inverter_to_electrical_base_distance',
            'view-projects-inspector_id',
            'view-projects-send_execution_defect',
            'view-projects-simban_id',



            'view-projects-structure_type_image',
            'view-projects-current_phase',
            'view-projects-send_list_comments',
            'view-projects-additional_costs_and_dorm_rent',
            'view-projects-panel_type',
            'view-projects-panel_power',
            'view-projects-panel_quantity',
            'view-projects-panel_serial_numbers_image',
            'view-projects-inverter_serial_number_image',
            'view-projects-meter_serial_number_image',
            'view-projects-sim_card_number',
            'view-projects-utm_y',
            'view-projects-utm_x',
            'view-projects-longitude',
            'view-projects-latitude',
            'view-projects-google_maps_link',
            'view-projects-gis_code',
            'view-projects-has_monitoring',
            'view-projects-mehrsan_status',
            'view-projects-mehrsan_date',

            'view-projects-insurance',
            'view-projects-support_needed',
            'view-projects-type',
            'view-projects-status',
            'view-projects-creator_id',
            'view-projects-updater_id',
            'view-projects-power_plant_capacity',
            'view-projects-monitoring_code',
            'view-projects-inverter_to_the_power_base_cable_used',
            'view-projects-monitoring_installation_date',
            'view-projects-monitoring_installer_id',
            'view-projects-ware_postage_date',
            'view-projects-ware_sender_id',
            'view-projects-cost_of_materials',
            'view-projects-material_cost_payment_date',
            'view-projects-execution_defect_comment',
            'view-projects-additional_costs_and_dorm_rent_payment_date',



            'view-projects-installer_id',
            'view-projects-designer_id',
            // -----------------------


            // -----------------------
            // user permissions ---
            'view-users-first_name',
            'view-users-birth_date',
            'view-users-last_name',
            'view-users-phone',
            'view-users-phone2',
            'view-users-father_name',
            'view-users-national_code',
            'view-users-city_id',
            'view-users-county_id',
            'view-users-province_id',
            'view-users-postal_code',
            'view-users-address',
            'view-users-bank',
            'view-users-bank_account_number',
            'view-users-bank_account_iban',
            'view-users-type',
            'view-users-otp_code',
            'view-users-otp_code_sent',
            'view-users-password',
            'view-users-creator_id',
            'view-users-updater_id',
            'view-users-remember_token',


            // -----------------------
            // deposit permissions ---
            'view-deposits-project_id',
            'view-deposits-amount',
            'view-deposits-payment_date',
            'view-deposits-user_id',
            'view-deposits-creator_id',
            'view-deposits-updater_id',
            'view-deposits-created_at',
            'view-deposits-bank',

            // -----------------------
            // checking permissions ---
            'view-checkings-project_id',
            'view-checkings-user_id',
            'view-checkings-creator_id',
            'view-checkings-updater_id',
            'view-checkings-created_at',
            'view-checkings-todolist_category_id',


            // -----------------------
            // visitor permissions ---
            'view-visitors-user_id',
            'view-visitors-project_id',
            'view-visitors-status',
            'view-visitors-visit_date',
            'view-visitors-comment',
            'view-visitors-creator_id',
            'view-visitors-updater_id',
            'view-visitors-created_at',
            'view-visitors-updated_at',


            // -----------------------
            // serial_panel permissions ---
            'view-serial_panels-project_id',
            'view-serial_panels-serial',
            'view-serial_panels-creator_id',
            'view-serial_panels-updater_id',
            'view-serial_panels-created_at',
            'view-serial_panels-updated_at',
            'view-serial_panels-description',


            // -----------------------
            // third_party_support permissions ---
            'view-third_party_supports-project_id',
            'view-third_party_supports-title',
            'view-third_party_supports-support_type',
            'view-third_party_supports-price',
            'view-third_party_supports-support_date',
            'view-third_party_supports-support_start_date',
            'view-third_party_supports-support_end_date',
            'view-third_party_supports-support_comments',
            'view-third_party_supports-support_completed',
            'view-third_party_supports-support_performer',
            'view-third_party_supports-creator_id',
            'view-third_party_supports-updater_id',
            'view-third_party_supports-created_at',
            'view-third_party_supports-updated_at',
            // project_support permissions ---
            'view-project_supports-project_id',
            'view-project_supports-status',
            'view-project_supports-title',
            'view-project_supports-start_date',
            'view-project_supports-end_date',
            'view-project_supports-supporter_comment',
            'view-project_supports-creator_id',
            'view-project_supports-creator_comment',
            'view-project_supports-updater_id',
            'view-project_supports-supporter_id',
            'view-project_supports-support_implementation_duration',




            //add new
            'view-projects-network_connection_comment',
            'view-projects-loan_received_date',

            'view-projects-project_support_comment',


            'view-roles',
            'create-roles',
            'update-roles',
            'delete-roles',

            'view-reports-financials',
            'view-reports-projects',
            'view-reports-users',

            'view-thirdparty-support',
            'create-thirdparty-support',
            'update-thirdparty-support',
            'delete-thirdparty-support',


            //user permissions
            'update-users-bank_branch',
            'update-users-roles',
            'update-users-access_province_ids',
            'update-users-access_support_organizations',
            'view-users-bank_branch',
            'view-users-roles',
            'view-users-access_province_ids',
            'view-users-access_support_organizations',

            //project tab permission
            'view-project-admin_tab',
            
            'view-project-accounting_tab',
            
            'view-project-visitor_tab',
            
            'view-project-design_tab',
            
            'view-project-warehouse_tab',
            
            'view-project-execution_tab',
            
            'view-project-supporting_tab',
            
            'view-project-checking_tab',
            
            'view-project-defect_tab',
            
            'view-project-execution_defect_tab',
            

            //project section permission
            'view-project-general_section',
            'view-project-more_information_section',           
            'view-project-deposit_section',            
            'view-project-time_statistics_information_section',            
            'view-project-executive_operation_status_section',           
            'view-project-visit_section',           
            'view-project-design_information_section',            
            'view-project-shipping_information_section',
            'view-project-executive_information_section',            
            'view-project-network_connection_information_section',
            


            //Btn mobile
            'mobile-access-admin',
            'mobile-access-accounting',
            'mobile-access-visitor',
            'mobile-access-design',
            'mobile-access-warehouse',
            'mobile-access-execution',
            'mobile-access-supporting',
            'mobile-access-checking',
            'mobile-access-defect',
            'mobile-access-execution_defect',



            'view-users-list',
            'view-projects-list',


            
            'view-projects-phone',
            'view-projects-phone2',
            'view-projects-father_name',
            'view-projects-national_code',
            'view-projects-bank',
            'view-projects-bank_branch',
            'view-projects-bank_account_number',
            'view-projects-bank_account_iban',
            'view-projects-birth_date',

            'access-customer-dashboard',


            'view-projects-discount',
            'update-projects-discount',
            'view-projects-accounting_status',
            'update-projects-accounting_status',
           

            'view-deposits-type',
            'update-deposits-type',
            'view-deposits-info_deposit',
            'update-deposits-info_deposit',


            'view-projects-earth_resistance',
            'update-projects-earth_resistance',
            'view-projects-execution_type',
            'update-projects-execution_type',
            'view-projects-measurement_date',
            'update-projects-measurement_date',

            'view-projects-meter_assignment',
            'update-projects-meter_assignment',
            'view-projects-announcement_sanam',
            'update-projects-announcement_sanam',

            'view-users-village_id',
            'update-users-village_id',
            'view-projects-village_id',
            'update-projects-village_id',
            
            'view-villages',
            'create-villages',
            'update-villages',
            'delete-villages',

            'create-change-status-mehrsan-with-file'
            
        ];

        foreach ($permissions as $permission) {
            Permission::create(['name' => $permission, 'guard_name' => 'web']);
            Permission::create(['name' => $permission, 'guard_name' => 'api']);
        }
    }
}
