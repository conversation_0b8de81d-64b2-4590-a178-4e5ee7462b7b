<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $admin = User::updateOrCreate([
            'national_code' => '***********',
            'phone' => '***********',
            'first_name' =>  'مدیر',
            'last_name' =>  'فولادی',

            'password' => Hash::make('***********'),
        ]);
        $admin->assignRole('super_admin');
        // $admin->assignRole('admin');
        // $admin->assignRole('visitor');
        // $admin->assignRole('supporting');
        // $admin->assignRole('accounting');
        // $admin->assignRole('design');
        // $admin->assignRole('warehouse');
        // $admin->assignRole('execution');
        // $admin->assignRole('defect');
        // $admin->assignRole('checking');


        $admin = User::updateOrCreate([
            'national_code' => '***********',
            'phone' => '***********',
            'first_name' =>  'محمد جواد',
            'last_name' =>  'محرر',
            'password' => Hash::make('***********'),
        ]);
        $admin->assignRole('admin');
        $admin->assignRole('visitor');
        $admin->assignRole('supporting');
        $admin->assignRole('accounting');
        $admin->assignRole('design');
        $admin->assignRole('warehouse');
        $admin->assignRole('execution');
        $admin->assignRole('defect');
        $admin->assignRole('checking');

        // $supporting = User::create([
        //     'national_code' => 'supporting',
        //     'first_name' =>  'پشتتیبانی',
        //     'phone' => '***********',
        //     'password' => Hash::make('123456'),
        // ]);
        // $supporting->assignRole('supporting');



        // $accounting = User::create([
        //     'national_code' => 'accounting',
        //     'first_name' => 'حسابداری',
        //     'phone' => '***********',
        //     'password' => Hash::make('123456'),
        // ]);
        // $accounting->assignRole('accounting');


        // $visitor = User::create([
        //     'national_code' => 'visitor',
        //     'first_name' => 'بازدید کننده',
        //     'phone' => '***********',
        //     'password' => Hash::make('123456'),
        // ]);
        // $visitor->assignRole('visitor');
        // $visitor->assignRole('supporting');
        // $visitor->assignRole('defect');
        // $visitor->assignRole('checking');


        // $design = User::create([
        //     'national_code' => 'design',
        //     'first_name' => 'طراحی',
        //     'phone' => '***********',
        //     'password' => Hash::make('123456'),
        // ]);
        // $design->assignRole('design');
        // $design->assignRole('supporting');
        // $design->assignRole('defect');
        // $design->assignRole('checking');



        // $warehouse = User::create([
        //     'national_code' => 'warehouse',
        //     'first_name' => 'انباردار',
        //     'phone' => '09176666666',
        //     'password' => Hash::make('123456'),
        // ]);
        // $warehouse->assignRole('warehouse');
        // $warehouse->assignRole('supporting');
        // $warehouse->assignRole('defect');
        // $warehouse->assignRole('checking');


        // $execution = User::create([
        //     'national_code' => 'execution',
        //     'first_name' => 'اجرا',
        //     'phone' => '09177777777',
        //     'password' => Hash::make('123456'),
        // ]);
        // $execution->assignRole('execution');
        // $execution->assignRole('supporting');
        // $execution->assignRole('defect');
        // $execution->assignRole('checking');




        // $customer = User::create([
        //     'national_code' => 'customer',
        //     'first_name' => 'مشتری',
        //     'phone' => '09178888888',
        //     'password' => Hash::make('123456'),
        // ]);
        // $customer->assignRole('customer');

    }

}
