<?php

namespace Database\Seeders;

use App\Models\Checking;
use App\Models\Defect;
use App\Models\Deposit;
use App\Models\GlobalConfig;
use App\Models\Project;
use App\Models\ProjectSupport;
use App\Models\SerialPanel;
use App\Models\ThirdPartySupport;
use App\Models\TodolistCategory;
use App\Models\User;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Config;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        Config::set('sun.seeding', true);

        // User::factory(10)->create();
        Artisan::call('iran:import --target=cities');
        $this->call(MenuSeeder::class);
        $this->call(RoleSeeder::class);
        $this->call(UserSeeder::class);

        // User::factory(10)->create();
        // Project::factory(100)->create();
        // ProjectSupport::factory(10)->create();
        // TodolistCategory::factory()->count(30)->create();
        // Checking::factory()->count(10)->create();
        // Deposit::factory()->count(10)->create();
        // SerialPanel::factory()->count(10)->create();
        // ThirdPartySupport::factory()->count(10)->create();
        // Defect::factory()->count(10)->create();
        GlobalConfig::create([
            'label' => 'app_version',
            'value' => '{
        "version_code" : 3,
        "text": "نسخه جدید رسید",
        "link": "https://khorshid-taban.com/android.apk",
        "force_update":true
        }',
        ]);
        Config::set('sun.seeding', false);

    }
}
