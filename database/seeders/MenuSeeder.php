<?php

namespace Database\Seeders;

use App\Models\AdminMenu;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class MenuSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::table('admin_menus')->truncate();

        //Last Id = 18 change if you inserted a new menu
        $adminMenus = [
            ['id' => '1', 'persian' => 'پیشخوان', 'english' => 'Dashboard', 'route' => 'dashboard', 'permission' => 'view-dashboard', 'priority' => '0'],
            ['id' => '2', 'persian' => 'کاربران', 'english' => 'Users', 'route' => 'none', 'permission' => 'view-users', 'priority' => '1'],
            ['id' => '3', 'persian' => 'پروژه ها', 'english' => 'Projects', 'route' => 'none', 'permission' => 'view-projects', 'priority' => '2'],
            ['id' => '11', 'persian' => 'گزارش گیری', 'english' => 'Users', 'route' => 'none', 'permission' => 'view-reports', 'priority' => '3'],
            ['id' => '8', 'persian' => 'پایگاه داده', 'english' => 'Database', 'route' => 'none', 'permission' => 'view-database', 'priority' => '4'],
//            ['id' => '', 'persian' => 'لاگ ها', 'english' => 'Logs', 'route' => 'logs.index', 'permission' => 'view-database', 'priority' => '5'],
            ['id' => '15', 'persian' => 'نقش ها', 'english' => 'Roles', 'route' => 'none', 'permission' => 'view-roles', 'priority' => '5'],

            ['id' => '4', 'persian' => 'همه کاربران', 'english' => 'All Users', 'route' => 'users.index', 'permission' => 'view-users', 'priority' => '0', 'parent_id' => '2'],
            ['id' => '5', 'persian' => 'افزودن کاربر', 'english' => 'Add User', 'route' => 'users.create', 'permission' => 'create-users', 'priority' => '1', 'parent_id' => '2'],
            ['id' => '10', 'persian' => 'افزودن کاربر با بارگذاری فایل', 'english' => 'Add User With Import File', 'route' => 'user.create-import-file', 'permission' => 'create-users', 'priority' => '2', 'parent_id' => '2'],

            ['id' => '6', 'persian' => 'همه پروژه ها', 'english' => 'All Projects', 'route' => 'projects.index', 'permission' => 'view-projects', 'priority' => '1', 'parent_id' => '3'],
            ['id' => '7', 'persian' => 'افزودن پروژه', 'english' => 'Add Project', 'route' => 'projects.create', 'permission' => 'create-projects', 'priority' => '2', 'parent_id' => '3'],
            ['id' => '18', 'persian' => 'به روزرسانی مهرسان با فایل', 'english' => 'Add Project', 'route' => 'projects.create-import-file', 'permission' => 'create-change-status-mehrsan-with-file', 'priority' => '3', 'parent_id' => '3'],


            ['id' => '9', 'persian' => 'لیست اجرایی و نواقص', 'english' => 'TODO List', 'route' => 'todolist-categories.index', 'permission' => 'view-todolist_categories', 'priority' => '0', 'parent_id' => '8'],

            ['id' => '12', 'persian' => 'پروژه ها', 'english' => 'Users', 'route' => 'report.project.index', 'permission' => 'view-reports', 'priority' => '0', 'parent_id' => 11],
            ['id' => '13', 'persian' => 'کاربران', 'english' => 'Users', 'route' => 'report.user.index', 'permission' => 'view-reports', 'priority' => '0', 'parent_id' => 11],
            ['id' => '14', 'persian' => 'مالی', 'english' => 'Financial', 'route' => 'report.financial.index', 'permission' => 'view-reports', 'priority' => '0', 'parent_id' => 11],

            ['id' => '16', 'persian' => 'همه نقش ها', 'english' => 'All Roles', 'route' => 'role.index', 'permission' => 'view-roles', 'priority' => '0', 'parent_id' => 15],
            ['id' => '17', 'persian' => 'افزودن نقش', 'english' => 'Create Roles', 'route' => 'role.create', 'permission' => 'create-roles', 'priority' => '1', 'parent_id' => 15],


        ];

        foreach ($adminMenus as $menu) {
            $menu['type'] = 'menu';
            AdminMenu::create($menu);
        }
    }
}
