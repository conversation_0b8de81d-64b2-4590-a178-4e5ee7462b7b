<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Validation Language Lines
    |--------------------------------------------------------------------------
    |
    | The following language lines contain the default error messages used by
    | the validator class. Some of these rules have multiple versions such
    | as the size rules. Feel free to tweak each of these messages here.
    |
    */

    'accepted' => ':attribute باید پذیرفته شده باشد.',
    'active_url' => 'آدرس :attribute معتبر نیست.',
    'after' => ':attribute باید تاریخی بعد از :date باشد.',
    'after_or_equal' => ':attribute باید تاریخی بعد از :date، یا مطابق با آن باشد.',
    'alpha' => ':attribute باید فقط حروف الفبا باشد.',
    'alpha_dash' => ':attribute باید فقط حروف الفبا، اعداد، خط تیره و زیرخط باشد.',
    'alpha_num' => ':attribute باید فقط حروف الفبا و اعداد باشد.',
    'array' => ':attribute باید آرایه باشد.',
    'before' => ':attribute باید تاریخی قبل از :date باشد.',
    'before_or_equal' => ':attribute باید تاریخی قبل از :date، یا مطابق با آن باشد.',
    'between' => [
        'numeric' => ':attribute باید بین :min و :max باشد.',
        'file' => ':attribute باید بین :min و :max کیلوبایت باشد.',
        'string' => ':attribute باید بین :min و :max کاراکتر باشد.',
        'array' => ':attribute باید بین :min و :max آیتم باشد.',
    ],
    'boolean' => 'فیلد :attribute فقط می‌تواند true و یا false باشد.',
    'confirmed' => ':attribute با فیلد تکرار مطابقت ندارد.',
    'date' => ':attribute یک تاریخ معتبر نیست.',
    'date_equals' => ':attribute باید یک تاریخ برابر با تاریخ :date باشد.',
    'date_format' => ':attribute با الگوی :format مطابقت ندارد.',
    'different' => ':attribute و :other باید از یکدیگر متفاوت باشند.',
    'digits' => ':attribute باید :digits رقم باشد.',
    'digits_between' => ':attribute باید بین :min و :max رقم باشد.',
    'dimensions' => 'ابعاد تصویر :attribute قابل قبول نیست.',
    'distinct' => 'فیلد :attribute مقدار تکراری دارد.',
    'email' => ':attribute باید یک ایمیل معتبر باشد.',
    'ends_with' => 'فیلد :attribute باید با یکی از مقادیر زیر خاتمه یابد: :values',
    'exists' => ':attribute انتخاب شده، معتبر نیست.',
    'file' => ':attribute باید یک فایل معتبر باشد.',
    'filled' => 'فیلد :attribute باید مقدار داشته باشد.',
    'gt' => [
        'numeric' => ':attribute باید بزرگتر از :value باشد.',
        'file' => ':attribute باید بزرگتر از :value کیلوبایت باشد.',
        'string' => ':attribute باید بیشتر از :value کاراکتر داشته باشد.',
        'array' => ':attribute باید بیشتر از :value آیتم داشته باشد.',
    ],
    'gte' => [
        'numeric' => ':attribute باید بزرگتر یا مساوی :value باشد.',
        'file' => ':attribute باید بزرگتر یا مساوی :value کیلوبایت باشد.',
        'string' => ':attribute باید بیشتر یا مساوی :value کاراکتر داشته باشد.',
        'array' => ':attribute باید بیشتر یا مساوی :value آیتم داشته باشد.',
    ],
    'image' => ':attribute باید یک تصویر معتبر باشد.',
    'in' => ':attribute انتخاب شده، معتبر نیست.',
    'in_array' => 'فیلد :attribute در لیست :other وجود ندارد.',
    'integer' => ':attribute باید عدد صحیح باشد.',
    'ip' => ':attribute باید آدرس IP معتبر باشد.',
    'ipv4' => ':attribute باید یک آدرس معتبر از نوع IPv4 باشد.',
    'ipv6' => ':attribute باید یک آدرس معتبر از نوع IPv6 باشد.',
    'json' => 'فیلد :attribute باید یک رشته از نوع JSON باشد.',
    'lt' => [
        'numeric' => ':attribute باید کوچکتر از :value باشد.',
        'file' => ':attribute باید کوچکتر از :value کیلوبایت باشد.',
        'string' => ':attribute باید کمتر از :value کاراکتر داشته باشد.',
        'array' => ':attribute باید کمتر از :value آیتم داشته باشد.',
    ],
    'lte' => [
        'numeric' => ':attribute باید کوچکتر یا مساوی :value باشد.',
        'file' => ':attribute باید کوچکتر یا مساوی :value کیلوبایت باشد.',
        'string' => ':attribute باید کمتر یا مساوی :value کاراکتر داشته باشد.',
        'array' => ':attribute باید کمتر یا مساوی :value آیتم داشته باشد.',
    ],
    'max' => [
        'numeric' => ':attribute نباید بزرگتر از :max باشد.',
        'file' => ':attribute نباید بزرگتر از :max کیلوبایت باشد.',
        'string' => ':attribute نباید بیشتر از :max کاراکتر داشته باشد.',
        'array' => ':attribute نباید بیشتر از :max آیتم داشته باشد.',
    ],
    'mimes' => 'فرمت‌های معتبر فایل عبارتند از: :values.',
    'mimetypes' => 'فرمت‌های معتبر فایل عبارتند از: :values.',
    'min' => [
        'numeric' => ':attribute نباید کوچکتر از :min باشد.',
        'file' => ':attribute نباید کوچکتر از :min کیلوبایت باشد.',
        'string' => ':attribute نباید کمتر از :min کاراکتر داشته باشد.',
        'array' => ':attribute نباید کمتر از :min آیتم داشته باشد.',
    ],
    'multiple_of' => 'مقدار :attribute باید مضربی از :value باشد.',
    'not_in' => ':attribute انتخاب شده، معتبر نیست.',
    'not_regex' => 'فرمت :attribute معتبر نیست.',
    'numeric' => ':attribute باید عدد یا رشته‌ای از اعداد باشد.',
    'password' => 'رمزعبور اشتباه است.',
    'present' => 'فیلد :attribute باید در پارامترهای ارسالی وجود داشته باشد.',
    'regex' => 'فرمت :attribute معتبر نیست.',
    'required' => 'فیلد :attribute الزامی است.',
    'required_if' => 'هنگامی که :other برابر با :value است، فیلد :attribute الزامی است.',
    'required_unless' => 'فیلد :attribute الزامی است، مگر آنکه :other در :values موجود باشد.',
    'required_with' => 'در صورت وجود فیلد :values، فیلد :attribute نیز الزامی است.',
    'required_with_all' => 'در صورت وجود فیلدهای :values، فیلد :attribute نیز الزامی است.',
    'required_without' => 'در صورت عدم وجود فیلد :values، فیلد :attribute الزامی است.',
    'required_without_all' => 'در صورت عدم وجود هر یک از فیلدهای :values، فیلد :attribute الزامی است.',
    'same' => ':attribute و :other باید همانند هم باشند.',
    'size' => [
        'numeric' => ':attribute باید برابر با :size باشد.',
        'file' => ':attribute باید برابر با :size کیلوبایت باشد.',
        'string' => ':attribute باید برابر با :size کاراکتر باشد.',
        'array' => ':attribute باید شامل :size آیتم باشد.',
    ],
    'starts_with' => ':attribute باید با یکی از این‌ها شروع شود: :values',
    'string' => 'فیلد :attribute باید متن باشد.',
    'timezone' => 'فیلد :attribute باید یک منطقه زمانی معتبر باشد.',
    'unique' => ':attribute قبلا انتخاب شده است.',
    'uploaded' => 'بارگذاری فایل :attribute موفقیت آمیز نبود.',
    'url' => ':attribute معتبر نمی‌باشد.',
    'uuid' => ':attribute باید یک UUID معتبر باشد.',
    'jdate' => ':attribute تاریخ جلالی معتبری نیست',
    'jdate_equal' => ':attribute برابر نیست با  :date',
    'jdate_not_equal' => ':attribute نابرابر نیست با :date',
    'jdatetime' => ':attribute تاریخ و ساعت جلالی معتبری نیست',
    'jdatetime_equal' => ':attribute برابر نیست با :date',
    'jdatetime_not_equal' => ':attribute نابرابر نیست با :date',
    'jdate_after' => ':attribute باید تاریخی باشد بعد از :date',
    'jdate_after_equal' => ':attribute باید تاریخی باشد برابر یا بعد از :date',
    'jdatetime_after' => ':attribute باید تاریخ و ساعتی باشد بعد از :date',
    'jdatetime_after_equal' => ':attribute باید تاریخ و ساعتی باشد برابر یا بعد از :date',
    'jdate_before' => ':attribute باید تاریخی باشد قبل از :date',
    'jdate_before_equal' => ':attribute باید تاریخی باشد برابر یا قبل از :date',
    'jdatetime_before' => ':attribute باید تاریخ و ساعتی باشد قبل از :date',
    'jdatetime_before_equal' => ':attribute باید تاریخ و ساعتی باشد برابر یا قبل از :date',
    'captcha' => 'کد امنیتی وارد شده اشتباه است !',

    /*
    |--------------------------------------------------------------------------
    | Custom Validation Language Lines
    |--------------------------------------------------------------------------
    |
    | Here you may specify custom validation messages for attributes using the
    | convention "attribute.rule" to name the lines. This makes it quick to
    | specify a specific custom language line for a given attribute rule.
    |
    */

    'custom' => [
        'validation.captcha' => 'کد امنیتی وارد شده اشتباه است !',
        'captcha' => 'کد امنیتی وارد شده اشتباه است !',
    ],

    /*
    |--------------------------------------------------------------------------
    | Custom Validation Attributes
    |--------------------------------------------------------------------------
    |
    | The following language lines are used to swap attribute place-holders
    | with something more reader friendly such as E-Mail Address instead
    | of "email". This simply helps us make messages a little cleaner.
    |
    */

    'attributes' => [
        'validation.captcha' => 'کد امنیتی وارد شده اشتباه است !',
        'captcha' => 'کد امنیتی وارد شده اشتباه است !',
        'name' => 'نام',
        'username' => 'نام کاربری',
        'email' => 'ایمیل',
        'first_name' => 'نام',
        'last_name' => 'نام خانوادگی',
        'password' => 'رمز عبور',
        'password_confirmation' => 'تکرار رمز عبور',
        'city' => 'شهر',
        'country' => 'کشور',
        'address' => 'نشانی',
        'phone' => 'شماره موبایل',
        'mobile' => 'شماره همراه',
        'age' => 'سن',
        'sex' => 'جنسیت',
        'gender' => 'جنسیت',
        'day' => 'روز',
        'month' => 'ماه',
        'year' => 'سال',
        'hour' => 'ساعت',
        'minute' => 'دقیقه',
        'second' => 'ثانیه',
        'title' => 'عنوان',
        'text' => 'متن',
        'content' => 'محتوا',
        'description' => 'توضیحات',
        'excerpt' => 'گزیده مطلب',
        'date' => 'تاریخ',
        'time' => 'زمان',
        'available' => 'موجود',
        'size' => 'اندازه',
        'terms' => 'شرایط',
        'province' => 'استان',
        'district' => 'ناحیه',
        'area' => 'منطقه',
        'national_code' => 'کد ملی',
        'featured_pic' => 'تصویر شاخص',
        'type' => 'نقش',
        'unit_count' => 'تعداد واحد',
        'price_meter' => 'قیمت هر متر مربع',
        'max_unit_per_person' => 'تعداد درخواست هر کاربر',
        'date_start' => 'تاریخ شروع',
        'date_end' => 'تاریخ پایان',
        'prepayment' => 'پیش پرداخت',
        'is_promotion' => 'ویژه برای فروش',
        'is_finish' => 'تمام شده',
        'short_desc' => 'مشخصات',
        'camera_link' => 'لینک دوربین',
        'block_count' => 'تعداد بلوک',
        'block_unit_count' => 'تعداد واحد هر بلوک',
        'short_address' => 'ادرس',
        'create_date' => 'سال ساخت',
        'block_image' => 'نقشه جانمایی بلوک‌ها',
        'block_text' => 'متن جانمایی بلوک‌ها',
        'shared_area' => 'متراژ مشا',
        'full_area' => 'متراژ کل',
        'cold_system' => 'سیستم سرمایشی',
        'warm_system' => 'سیستم گرمایشی',
        'usable_area' => 'متراژ مفید',
        'building_type' => 'نوع پروژه',
        'front_type' => 'نوع نما',
        'progress' => 'درصد پیشرفت',
        'floor_count' => 'تعداد طبقات',
        'form.name' => 'نام',
        'form.info' => 'توضیحات',
        'form.buildingType' => 'نوع پروژه',
        'form.buildingId' => 'پروژه',
        'parts.*.amount' => 'مقدار',
        'parts.*.info' => 'توضیحات قسط',
        'parts.*.type' => 'مرحله پرداخت',
        'parts.*.date' => 'تاریخ قسط',
        'info' => 'اطلاعات',
        'amount' => 'مقدار',
        'status' => 'وضعیت',
        'pay_type' => 'مرحله پرداخت',
        'opinion' => 'نظر',
        'pic' => 'تصویر',
        'file' => 'فایل',
        'unit_number' => 'شماره واحد',
        'request_id' => 'درخواست',
        'family' => 'نام خانوادگی',
        'code_melli' => 'کد ملی',
        'birthday' => 'تولد',
        'city_id' => 'شهر',
        'province_id' => 'استان',
        'postal_code' => 'کد پستی',
        'ostan_tel_number' => 'کد استان',
        'home_num' => 'شماره خانه',
        'bank_name' => 'نام بانک',
        'shaba_code' => 'کد شبا',
        'number' => 'شماره همراه',
        'code' => 'کد',
        'name' => 'نام',
        'nickname' => 'نام مستعار',
        'roles' => 'سطح دسترسی',
        'password_repeat' => 'تکرار رمز عبور',
        'font_name' => 'نام فونت',
        'font_version' => 'نسخه فونت',
        'font_slug' => 'نامک فونت',
        'font_slug_name' => 'نامک انگلیسی فونت لب',
        'font_designer' => 'طراح فونت',
        'priority_date' => 'تاریخ اولویت بندی',
        'category_name' => 'نام دسته بندی',
        'category_slug' => 'نامک دسته بندی',
        'category_priority' => 'اولویت دسته بندی',
        'label' => 'متن کمکی برای خریدار',
        'url_label' => 'متن کمکی برای لینک سایت',
        'category' => 'دسته‌بندی',
        'value' => 'مقدار',
        'limit' => 'حد استفاده',
        'associate_repeater.*.associate_user_id' => "نام کاربری شریک",
        'associate_repeater.*.associate_percent' => "درصد شراکت",
        'associate_repeater.*.associate_date_start' => "تاریخ شروع شراکت",
        'associate_repeater.*.associate_date_end' => "تاریخ پایان شراکت",
        'associate_repeater.*.associate_role' => "نقش در شراکت",
        'discount' => 'خبرنامه تخفیف‌ها',
        'new_feature' => 'خبرنامه امکانات جدید وبسایت',
        'new_blog' => 'خبرنامه مطالب آموزشی تخصصی',
        'new_font' => 'خبرنامه فونت جدید',
        'part_repeat' => 'متن خبرنامه',
        'priority' => 'اولویت',
        'expirationDate' => 'تاریخ انقضاء',
        'old_password' => 'رمزعبور قبلی',
        'legal_postal_code' => 'کد پستی حقوقی',
        'registration_number' => 'شماره ثبت',
        'national_id' => 'شناسه ملی',
        'bank_account' => 'شماره حساب',
        'card_number' => 'شماره کارت',
        'shaba_number' => 'شماره شبا',
        'first_address' => 'لینک اول',
        'second_address' => 'لینک دوم',
        'third_address' => 'لینک سوم',
        'package_price' => 'قیمت پکیج',
        'package_weights' => 'وزن پکیج',
        'package_subtitle' => 'زیرعنوان پکیج',
        'package_title' => 'عنوان پکیج',
        'package_file' => 'فایل پکیج',
        'package_repeater' => 'پکیج',
        'package_repeater.*.package_weights' => 'وزن پکیج',
        'package_repeater.*.package_price' => 'قیمت پکیج',
        'package_repeater.*.package_subtitle' => 'زیرعنوان پکیج',
        'package_repeater.*.package_title' => 'عنوان پکیج',
        'package_repeater.*.package_file' => 'فایل پکیج',
        'package_repeater.*.package_discount_price' => 'قیمت تخفیف',
        'single_file' => 'فایل تک',
        'percentage' => 'درصد',
        'avatar' => 'تصویر',
        'legal_name' => 'نام شرکت',
        'legal_address' => 'آدرس شرکت',
        'natural_address' => 'آدرس',
        'comment_text' => 'متن کامنت',
        'comment_images.*' => 'فایل پیوست',
        'father_name' => 'نام پدر',
        'bank_account_number' => 'شماره حساب بانک',
        'bank_account_iban' => 'شماره شبا',
        'county_id' => 'شهرستان'
    ]
];
