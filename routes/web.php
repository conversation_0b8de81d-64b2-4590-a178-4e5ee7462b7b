<?php

use App\Http\Controllers\AuthController;
use App\Http\Controllers\BankController;
use App\Http\Controllers\ExecutionDefectController;
use App\Http\Controllers\LogsController;
use App\Http\Controllers\ProjectController;
use App\Http\Controllers\ProjectImportFileController;
use App\Http\Controllers\ReportController;
use App\Http\Controllers\RoleController;
use App\Http\Controllers\ThirdPartySupportsController;
use App\Http\Controllers\TodolistCategoryController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\VillageController;
use App\Http\Middleware\HideDetection;
use App\Models\Visitor;
use Illuminate\Database\Query\Builder;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Route;


Route::middleware([HideDetection::class])->group(function () {
    Route::prefix('auth')->name('auth.')->controller(AuthController::class)->group(function () {
        Route::get('logout', 'logoutWeb')->name('logout-web');
        Route::post('login', 'loginWeb')->name('login-web');
        Route::get('login', 'loginPage')->name('login');
        Route::get('otp', 'otpPage')->name('otp-page');
        Route::post('send-sms', 'sendSms')->name('send-sms');
        Route::post('check-phone-code', 'checkPhoneCode')->name('check-phone-code');
    });
    Route::get('login', fn() => redirect()->route('auth.login'))->name('login');

    Route::middleware('auth:web')->group(function () {
        Route::get('users/get-users', [UserController::class, 'getUsers'])->name('users.get-users');
        Route::resource('users', UserController::class);
        Route::get('/user/create-import-file', [UserController::class, 'createUserWithImportFile'])->name('user.create-import-file');
        Route::post('/user/create-import-file', [UserController::class, 'storeUserWithImportFile'])->name('user.store-import-file');
        Route::post('/users/filter', [UserController::class, 'filteredIndex'])->name('user.filter');
        Route::get('users-export', [UserController::class, 'export'])->name('users.export');
        Route::post('users-import', [UserController::class, 'import'])->name('users.import');


        Route::resource('todolist-categories', TodolistCategoryController::class);
        //Route::resource('projects', ProjectController::class)->except('create');
        Route::get('projects/index', [ProjectController::class, 'index'])->name('projects.index');
        Route::get('projects/{project}/show', [ProjectController::class, 'show'])->name('projects.show');
        Route::get('projects/create', [ProjectController::class, 'create'])->name('projects.create');
        Route::get('projects/{project}/edit', [ProjectController::class, 'edit'])->name('projects.edit');
        Route::post('projects/store', [ProjectController::class, 'store'])->name('projects.store');
        Route::delete('projects/{project}/delete', [ProjectController::class, 'destroy'])->name('projects.destroy');
        Route::post('/upload', [ProjectController::class, 'upload'])->name('upload');
        Route::get('/media/download/{id}', [ProjectController::class, 'downloadMedia'])->name('media.download');
        Route::delete('/media/delete/{id}', [ProjectController::class, 'destroyMedia'])->name('media.delete');

        Route::post('/projects/uploadMedia', [ProjectController::class, 'uploadMedia'])->name('projects.uploadMedia');

        Route::prefix('/projects')->name('projects.')->group(function () {

            Route::get('/get-projects', [ProjectController::class, 'getProjects'])->name('getProjects');

            Route::post('/search', [ProjectController::class, 'search'])->name('search');
            Route::post('/{project?}', [ProjectController::class, 'updateOrCreateProjectDetail'])->name('update');


            Route::get('/support/{project?}', [ProjectController::class, 'getSupportDetails'])->name('support');
            Route::post('/support/{project?}', [ProjectController::class, 'updateOrCreateSupportDetail'])->name('support.update');

            Route::get('/accounting/{project?}', [ProjectController::class, 'getAccountingDetails'])->name('accounting');
            Route::post('/accounting/{project?}', [ProjectController::class, 'updateOrCreateAccountingDetails'])->name('accounting.update');

            Route::get('/deposit/{project?}', [ProjectController::class, 'getDepositDetails'])->name('deposit');
            Route::post('/deposit/{project?}', [ProjectController::class, 'updateOrCreateDepositDetails'])->name('deposit.update');

            Route::get('/get-project-by-customer/{id}', [ProjectController::class, 'getProjectByCustomer'])->name('get-project-by-customer');

            Route::get('/project/create-import-file', [ProjectImportFileController::class, 'changeMehrsanWithImportFile'])->name('create-import-file');
            Route::post('/project/store-import-file', [ProjectImportFileController::class, 'storeMehrsanWithImportFile'])->name('store-import-file');

        });

        Route::prefix('report')->name('report.')->controller(ReportController::class)->group(function () {
            Route::get('projects', 'projects')->name('project.index');
            Route::post('project-search', 'projectSearch')->name('project.search');

            Route::get('user', 'users')->name('user.index');
            Route::post('user-search', 'userSearch')->name('user.search');

            Route::get('financial', 'financials')->name('financial.index');
            Route::post('financial-search', 'financialSearch')->name('financial.search');


        });

        Route::prefix('third-party-support/{project}/')->name('third-party-support.')->controller(ThirdPartySupportsController::class)->group(function () {
            Route::get('index', 'index')->name('index');
            Route::get('create', 'create')->name('create');
            Route::get('{support}/edit', 'edit')->name('edit');
            Route::post('store', 'store')->name('store');
            Route::post('{support}/update', 'update')->name('update');

        });


        Route::prefix('execution-defect/{project}/')->name('execution-defect.')->controller(ExecutionDefectController::class)->group(function () {
            Route::get('create', 'create')->name('create');
            Route::get('{execution_defect}/fix', 'fix')->name('fix');
            Route::post('store', 'store')->name('store');
            Route::post('{execution_defect}/fix-store', 'fixStore')->name('fix-store');

        });
        Route::get('/get_all_village', [VillageController::class, 'getAllVillagesAjax'])->name('village.all');
        Route::get('/get_all_bank', [BankController::class, 'getAllBanksAjax'])->name('bank.all');

        Route::prefix('logs')->controller(LogsController::class)->middleware(['can:view-logs'])->group(function () {
            Route::get('/', 'index')->name('logs.index');
            Route::post('/get_all_users', 'getAllUsersAjax')->name('logs.get.all.users');
            Route::post('/get_users', 'getUsersAjax')->name('logs.get.users');
            Route::post('/get_models', 'getModelsAjax')->name('logs.get.models');
        });

        Route::resource('/role', RoleController::class);



        Route::get('/', function () {
            return view('dashboard.index');
        })->name('dashboard');

    });

});

Route::get('/test', function () {
    // $visitor = Visitor::factory(1)->create(['creator_id' => null , 'updater_id' => null]);
    // dd($visitor);
    // $users=\App\Models\User::get();
    //     foreach ($users as $user) {
    //         $user->save();
    //     }

});
/*Route::get('/test2', function () {
    $path = public_path('sample-file-user.xlsx');
    $media = MediaUploader::fromSource($path)->useFilename("test")
        ->toDirectory('panels/serial') // Specify the directory path
        ->upload();

    $project = \App\Models\Project::first();
    $project->syncMedia($media, 'test');
});


Route::get('/test', function () {
    $url = config('app.url');
//        DB::raw("Concat($url/m.directory/m.) as url"), 'm.*', 'md.*')
    $user = auth()->user();
    if (is_null($user->last_media_update))
        $user->last_media_update = Carbon::parse('2009/9/9');
    $lastUpdate = $user->last_media_update;

    $medias = DB::table('media as m')->select(
        DB::raw("CONCAT('" . $url . '/' . "', m.directory, '/', m.filename, '.', m.extension) as media")
        , 'm.id', 'md.mediable_type', 'md.mediable_id', 'md.tag', 'md.order', 'md.created_at', 'md.updated_at', 'md.deleted_at',DB::raw('m.updated_at as mediaupdated')
    )
        ->join('mediables as md', 'md.media_id', '=', 'm.id')
        ->where(fn(Builder $query) => $query->where('m.updated_at', '>', $lastUpdate)
            ->orWhere('md.updated_at', '>', $lastUpdate))
        ->get()->map(function ($media) {
            $media->mediable_type = app($media->mediable_type)->getTable();
            return $media;
        });
    dd($medias);
});
*/


